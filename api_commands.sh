#!/bin/bash

# A script to run API commands for testing the Vyoo server.
#
# Usage:
# 1. Make this script executable: chmod +x api_commands.sh
# 2. Run the desired function: ./api_commands.sh signup

# --- Configuration ---
BASE_URL="http://localhost:8081/v1/api"
EMAIL="<EMAIL>"
PASSWORD="password123"
USERNAME="testuser"

# --- Functions ---

# Function to sign up a new user
signup() {
  echo "--- Signing up new user: $EMAIL ---"
  curl -s -X POST "$BASE_URL/auth/email/signup" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$EMAIL\",
    \"username\": \"$USERNAME\",
    \"password\": \"$PASSWORD\"
  }"
  echo -e "\n"
}

# Function to log in and extract the token
login() {
  echo "--- Logging in as: $EMAIL ---"
  LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"loginIdentifier\": \"$EMAIL\",
    \"password\": \"$PASSWORD\"
  }")

  if echo "$LOGIN_RESPONSE" | grep -q "token"; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | sed -E 's/.*"token":"([^"]+)".*/\1/')
    echo "Login successful. Token extracted."
    echo "TOKEN=$TOKEN" > .api_token # Save token for other commands
  else
    echo "Login failed. Response:"
    echo "$LOGIN_RESPONSE"
  fi
  echo -e "\n"
}

# Function to create a stream using the saved token
create_stream() {
  if [ ! -f .api_token ]; then
    echo "Token file not found. Please log in first."
    return
  fi
  source .api_token
  echo "--- Creating a new stream ---"
  CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "My API Test Stream",
    "description": "Testing from the command line!",
    "type": "video-live",
    "status": "draft",
    "url": "placeholder"
  }')
  STREAM_ID=$(echo "$CREATE_RESPONSE" | sed -E 's/.*"_id":"([^"]+)".*/\1/')
  echo "Stream created with ID: $STREAM_ID"
  echo "STREAM_ID=$STREAM_ID" >> .api_token
  echo -e "\n"
}

get_stream_key() {
  if [ ! -f .api_token ]; then
    echo "Token file not found. Please log in and create a stream first."
    return
  fi
  source .api_token
  echo "--- Getting stream key for stream: $STREAM_ID ---"
  STREAM_DETAILS=$(curl -s -X GET "$BASE_URL/stream/$STREAM_ID" \
  -H "Authorization: Bearer $TOKEN")
  
  STREAM_KEY=$(echo "$STREAM_DETAILS" | sed -n 's/.*"streamKey":"\([^"]*\)".*/\1/p')
  echo "Stream Key: $STREAM_KEY"
  echo "STREAM_KEY=$STREAM_KEY" >> .api_token
  echo -e "\n"
}

# Function to start streaming with ffmpeg
stream_with_ffmpeg() {
  if [ ! -f .api_token ]; then
    echo "Token file not found. Please run the 'all' command first."
    return
  fi
  source .api_token
  if [ -z "$STREAM_KEY" ]; then
    echo "Stream key not found. Please run 'get_stream_key' first."
    return
  fi
  echo "--- Starting FFmpeg stream ---"
  echo "RTMP URL: rtmp://localhost:1935/live"
  echo "Stream Key: $STREAM_KEY"
  
  ffmpeg -re -f lavfi -i testsrc2=size=1280x720:rate=30 \
         -c:v libx264 -preset ultrafast -tune zerolatency -f flv \
         "rtmp://localhost:1935/live/$STREAM_KEY"
}

all() {
    signup
    login
    create_stream
    get_stream_key
}

# --- Command Line Argument Handling ---
case "$1" in
  signup)
    signup
    ;;
  login)
    login
    ;;
  create_stream)
    create_stream
    ;;
  all)
    all
    ;;
  get_stream_key)
    get_stream_key
    ;;
  stream)
    stream_with_ffmpeg
    ;;
  *)
    echo "Usage: $0 {signup|login|create_stream|all|get_stream_key|stream}"
    exit 1
esac 
#!/bin/bash

echo "=== VYOO STREAMING TEST ==="
echo "This script will help you test both streaming and S3 upload functionality"
echo ""

# Test 1: Check if container is running
echo "1. Checking container status..."
if sudo docker ps | grep -q rtmp-server; then
    echo "✅ Container is running"
else
    echo "❌ Container is not running"
    exit 1
fi

# Test 2: Check HLS directory before streaming
echo ""
echo "2. Checking HLS directory before streaming..."
echo "Files currently in HLS directory:"
sudo docker exec rtmp-server ls -la /mnt/hls/
echo ""

# Test 3: Check S3 bucket before streaming
echo "3. Checking S3 bucket before streaming..."
echo "Current S3 bucket contents:"
sudo docker exec rtmp-server aws s3 ls s3://vyooo-abr/ --recursive
echo ""

echo "=== READY FOR STREAMING TEST ==="
echo ""
echo "Now start your stream using FFmpeg:"
echo "ffmpeg -re -i <your_input> -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f flv rtmp://localhost:1935/live/teststream"
echo ""
echo "Or use OBS Studio with these settings:"
echo "Server: rtmp://localhost:1935/live"
echo "Stream Key: teststream"
echo ""
echo "Then run this script again with 'check' parameter to verify:"
echo "./test-stream.sh check"
echo ""
echo "To play the stream:"
echo "ffplay http://localhost:8080/hls/teststream.m3u8"
echo ""

# If 'check' parameter is provided, check the results
if [ "$1" = "check" ]; then
    echo "=== CHECKING STREAMING RESULTS ==="
    echo ""
    
    echo "4. Checking HLS directory after streaming..."
    echo "Files in HLS directory:"
    sudo docker exec rtmp-server ls -la /mnt/hls/
    echo ""
    
    echo "5. Checking S3 bucket after streaming..."
    echo "S3 bucket contents:"
    sudo docker exec rtmp-server aws s3 ls s3://vyooo-abr/ --recursive
    echo ""
    
    echo "6. Checking upload logs..."
    echo "Recent upload log entries:"
    sudo docker exec rtmp-server tail -10 /var/log/upload.log 2>/dev/null || echo "No upload logs yet"
    echo ""
    
    echo "7. Checking failed uploads..."
    failed_count=$(sudo docker exec rtmp-server wc -l /var/log/failed_uploads.log 2>/dev/null | cut -d' ' -f1)
    if [ "$failed_count" -gt 0 ]; then
        echo "⚠️ Found $failed_count failed uploads:"
        sudo docker exec rtmp-server cat /var/log/failed_uploads.log
    else
        echo "✅ No failed uploads"
    fi
    echo ""
    
    echo "8. Testing stream playback..."
    echo "Try playing the stream with:"
    echo "ffplay http://localhost:8080/hls/teststream.m3u8"
    echo ""
    
    echo "=== TEST SUMMARY ==="
    # Count files in HLS directory
    hls_files=$(sudo docker exec rtmp-server find /mnt/hls -name "*.ts" -o -name "*.m3u8" | wc -l)
    s3_files=$(sudo docker exec rtmp-server aws s3 ls s3://vyooo-abr/ --recursive | wc -l)
    
    echo "HLS files created: $hls_files"
    echo "S3 files uploaded: $s3_files"
    
    if [ "$hls_files" -gt 0 ] && [ "$s3_files" -gt 0 ]; then
        echo "🎉 SUCCESS: Both streaming and S3 upload are working!"
    elif [ "$hls_files" -gt 0 ]; then
        echo "⚠️ PARTIAL: Streaming works but S3 upload may have issues"
    else
        echo "❌ FAILED: No HLS files created - streaming not working"
    fi
fi 
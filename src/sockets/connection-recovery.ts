import { Server, Socket } from 'socket.io';
import logger from '../config/logger';
import authenticateSocket from '../middlewares/authenticate-socket';

interface ConnectionState {
  userId: string;
  username: string;
  joinedStreams: Set<string>;
  lastActivity: number;
  connectionAttempts: number;
  isAuthenticated: boolean;
}

interface SocketData {
  user: {
    _id: string;
    username: string;
  };
}

interface StreamState {
  activeViewers: Set<string>;
  metadata: {
    streamId: string;
    creatorId: string;
    title: string;
    isLive: boolean;
  };
}

class SocketConnectionRecovery {
  private connectionStates = new Map<string, ConnectionState>();
  private streamStates = new Map<string, StreamState>();
  private io: Server | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  private readonly stateTimeout = 5 * 60 * 1000; // 5 minutes
  private readonly heartbeatInterval_ms = 30 * 1000; // 30 seconds
  private readonly cleanupInterval_ms = 2 * 60 * 1000; // 2 minutes
  private readonly maxReconnectAttempts = 5;

  public initialize(io: Server): void {
    this.io = io;
    this.setupEventHandlers(io);
    this.startHeartbeat();
    this.startCleanup();
    
    logger.info('Socket connection recovery system initialized');
  }

  private setupEventHandlers(io: Server): void {
    // Enhanced authentication with recovery state
    io.use((socket, next) => {
      authenticateSocket(socket, (error: any) => {
        if (error) {
          logger.warn('Socket authentication failed', {
            socketId: socket.id,
            error: error.message
          });
          next(error);
          return;
        }
        
        const socketData = socket.data as SocketData;
        if (socketData.user) {
          this.handleAuthentication(socket, socketData.user);
        }
        
        next();
      });
    });

    io.on('connection', (socket) => {
      this.handleConnection(socket);
    });
  }

  private handleAuthentication(socket: Socket, user: { _id: string; username: string }): void {
    const existingState = this.connectionStates.get(user._id);
    
    if (existingState) {
      // Restore previous state
      logger.info('Restoring connection state for user', {
        userId: user._id,
        username: user.username,
        socketId: socket.id,
        joinedStreams: Array.from(existingState.joinedStreams),
        lastActivity: new Date(existingState.lastActivity)
      });
      
      // Rejoin previous streams
      existingState.joinedStreams.forEach(streamId => {
        socket.join(streamId);
        this.updateStreamViewerCount(streamId);
      });
      
      // Reset connection attempts on successful reconnection
      existingState.connectionAttempts = 0;
      existingState.lastActivity = Date.now();
      existingState.isAuthenticated = true;
    } else {
      // Create new state
      this.connectionStates.set(user._id, {
        userId: user._id,
        username: user.username,
        joinedStreams: new Set(),
        lastActivity: Date.now(),
        connectionAttempts: 0,
        isAuthenticated: true
      });
      
      logger.info('New connection state created for user', {
        userId: user._id,
        username: user.username,
        socketId: socket.id
      });
    }
  }

  private handleConnection(socket: Socket): void {
    const socketData = socket.data as SocketData;
    const user = socketData?.user;
    
    if (!user) {
      logger.warn('Socket connected without authentication', { socketId: socket.id });
      socket.disconnect(true);
      return;
    }

    logger.info('Socket connected with recovery support', {
      userId: user._id,
      username: user.username,
      socketId: socket.id
    });

    // Set up enhanced event handlers with recovery
    this.setupSocketEventHandlers(socket, user);

    // Send connection recovery acknowledgment
    socket.emit('connection:recovered', {
      status: 'connected',
      userId: user._id,
      recoveredStreams: Array.from(this.connectionStates.get(user._id)?.joinedStreams || []),
      timestamp: Date.now()
    });
  }

  private setupSocketEventHandlers(socket: Socket, user: { _id: string; username: string }): void {
    // Enhanced stream joining with state persistence
    socket.on('VIEW_STREAM', (streamId: string) => {
      this.handleStreamJoin(socket, user, streamId);
    });

    socket.on('LEAVE_STREAM', (streamId: string) => {
      this.handleStreamLeave(socket, user, streamId);
    });

    // Heartbeat handling
    socket.on('heartbeat', () => {
      this.updateUserActivity(user._id);
      socket.emit('heartbeat:ack', { timestamp: Date.now() });
    });

    // Connection recovery request
    socket.on('connection:recover', () => {
      this.handleConnectionRecovery(socket, user);
    });

    // State synchronization request
    socket.on('state:sync', () => {
      this.handleStateSync(socket, user);
    });

    // Enhanced disconnect handling
    socket.on('disconnect', (reason) => {
      this.handleDisconnect(socket, user, reason);
    });

    // Error handling
    socket.on('error', (error) => {
      logger.error('Socket error', {
        userId: user._id,
        socketId: socket.id,
        error: error.message || error
      });
    });
  }

  private handleStreamJoin(socket: Socket, user: { _id: string; username: string }, streamId: string): void {
    try {
      const state = this.connectionStates.get(user._id);
      if (!state) {
        logger.warn('Attempting to join stream without connection state', {
          userId: user._id,
          streamId
        });
        return;
      }

      // Join socket room
      socket.join(streamId);
      
      // Update connection state
      state.joinedStreams.add(streamId);
      state.lastActivity = Date.now();

      // Update stream state
      this.updateStreamState(streamId, user._id, 'join');

      logger.info('User joined stream with recovery support', {
        userId: user._id,
        username: user.username,
        streamId,
        totalJoinedStreams: state.joinedStreams.size
      });

      // Emit updated viewer count
      this.updateStreamViewerCount(streamId);

    } catch (error) {
      logger.error('Error handling stream join', {
        userId: user._id,
        streamId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private handleStreamLeave(socket: Socket, user: { _id: string; username: string }, streamId: string): void {
    try {
      const state = this.connectionStates.get(user._id);
      if (!state) return;

      // Leave socket room
      socket.leave(streamId);
      
      // Update connection state
      state.joinedStreams.delete(streamId);
      state.lastActivity = Date.now();

      // Update stream state
      this.updateStreamState(streamId, user._id, 'leave');

      logger.info('User left stream', {
        userId: user._id,
        username: user.username,
        streamId,
        remainingStreams: state.joinedStreams.size
      });

      // Emit updated viewer count
      this.updateStreamViewerCount(streamId);

    } catch (error) {
      logger.error('Error handling stream leave', {
        userId: user._id,
        streamId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private handleConnectionRecovery(socket: Socket, user: { _id: string; username: string }): void {
    const state = this.connectionStates.get(user._id);
    if (!state) {
      socket.emit('connection:recovery:failed', {
        error: 'No state to recover'
      });
      return;
    }

    // Rejoin all streams
    const recoveredStreams: string[] = [];
    state.joinedStreams.forEach(streamId => {
      socket.join(streamId);
      recoveredStreams.push(streamId);
    });

    state.lastActivity = Date.now();
    state.connectionAttempts = 0;

    logger.info('Connection recovered successfully', {
      userId: user._id,
      username: user.username,
      recoveredStreams: recoveredStreams.length
    });

    socket.emit('connection:recovery:success', {
      recoveredStreams,
      timestamp: Date.now()
    });
  }

  private handleStateSync(socket: Socket, user: { _id: string; username: string }): void {
    const state = this.connectionStates.get(user._id);
    if (!state) return;

    const syncData = {
      userId: user._id,
      joinedStreams: Array.from(state.joinedStreams),
      lastActivity: state.lastActivity,
      isAuthenticated: state.isAuthenticated,
      timestamp: Date.now()
    };

    socket.emit('state:synced', syncData);
  }

  private handleDisconnect(socket: Socket, user: { _id: string; username: string }, reason: string): void {
    logger.info('Socket disconnected with recovery support', {
      userId: user._id,
      username: user.username,
      socketId: socket.id,
      reason
    });

    const state = this.connectionStates.get(user._id);
    if (state) {
      state.isAuthenticated = false;
      state.connectionAttempts++;
      
      // Update viewer counts for all joined streams
      state.joinedStreams.forEach(streamId => {
        this.updateStreamViewerCount(streamId);
      });
    }
  }

  private updateStreamState(streamId: string, userId: string, action: 'join' | 'leave'): void {
    let streamState = this.streamStates.get(streamId);
    
    if (!streamState) {
      streamState = {
        activeViewers: new Set(),
        metadata: {
          streamId,
          creatorId: '',
          title: '',
          isLive: true
        }
      };
      this.streamStates.set(streamId, streamState);
    }

    if (action === 'join') {
      streamState.activeViewers.add(userId);
    } else {
      streamState.activeViewers.delete(userId);
    }
  }

  private updateStreamViewerCount(streamId: string): void {
    if (!this.io) return;

    const streamState = this.streamStates.get(streamId);
    if (!streamState) return;

    // Count authenticated viewers only
    let authenticatedViewers = 0;
    streamState.activeViewers.forEach(userId => {
      const userState = this.connectionStates.get(userId);
      if (userState?.isAuthenticated) {
        authenticatedViewers++;
      }
    });

    this.io.to(streamId).emit('LIVE_STREAM_VIEWS', {
      viewers: authenticatedViewers,
      streamId,
      timestamp: Date.now()
    });
  }

  private updateUserActivity(userId: string): void {
    const state = this.connectionStates.get(userId);
    if (state) {
      state.lastActivity = Date.now();
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (!this.io) return;

      this.io.emit('heartbeat:ping', { timestamp: Date.now() });
      
      // Check for inactive connections
      const now = Date.now();
      this.connectionStates.forEach((state, userId) => {
        if (now - state.lastActivity > this.stateTimeout) {
          logger.warn('Connection state expired due to inactivity', {
            userId,
            username: state.username,
            lastActivity: new Date(state.lastActivity)
          });
          
          // Mark as disconnected but keep state for potential recovery
          state.isAuthenticated = false;
        }
      });
      
    }, this.heartbeatInterval_ms);
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredStates();
    }, this.cleanupInterval_ms);
  }

  private cleanupExpiredStates(): void {
    const now = Date.now();
    let cleanedConnections = 0;
    let cleanedStreams = 0;

    // Cleanup connection states
    this.connectionStates.forEach((state, userId) => {
      if (now - state.lastActivity > this.stateTimeout && 
          state.connectionAttempts > this.maxReconnectAttempts) {
        
        logger.info('Cleaning up expired connection state', {
          userId,
          username: state.username,
          lastActivity: new Date(state.lastActivity),
          attempts: state.connectionAttempts
        });
        
        this.connectionStates.delete(userId);
        cleanedConnections++;
      }
    });

    // Cleanup stream states with no active viewers
    this.streamStates.forEach((streamState, streamId) => {
      if (streamState.activeViewers.size === 0) {
        this.streamStates.delete(streamId);
        cleanedStreams++;
      }
    });

    if (cleanedConnections > 0 || cleanedStreams > 0) {
      logger.info('Connection recovery cleanup completed', {
        cleanedConnections,
        cleanedStreams,
        remainingConnections: this.connectionStates.size,
        remainingStreams: this.streamStates.size
      });
    }
  }

  // Public methods for monitoring and management
  public getConnectionStats(): {
    totalConnections: number;
    authenticatedConnections: number;
    totalStreams: number;
    activeViewers: number;
  } {
    let authenticatedConnections = 0;
    let activeViewers = 0;

    this.connectionStates.forEach(state => {
      if (state.isAuthenticated) {
        authenticatedConnections++;
        activeViewers += state.joinedStreams.size;
      }
    });

    return {
      totalConnections: this.connectionStates.size,
      authenticatedConnections,
      totalStreams: this.streamStates.size,
      activeViewers
    };
  }

  public getStreamViewers(streamId: string): string[] {
    const streamState = this.streamStates.get(streamId);
    return streamState ? Array.from(streamState.activeViewers) : [];
  }

  public forceDisconnectUser(userId: string): boolean {
    const state = this.connectionStates.get(userId);
    if (!state) return false;

    // Find and disconnect all sockets for this user
    if (this.io) {
      this.io.sockets.sockets.forEach(socket => {
        const socketData = socket.data as SocketData;
        if (socketData?.user?._id === userId) {
          socket.disconnect(true);
        }
      });
    }

    // Clean up state
    this.connectionStates.delete(userId);
    
    logger.info('Force disconnected user', { userId, username: state.username });
    return true;
  }

  public shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    logger.info('Socket connection recovery system shut down');
  }
}

// Export singleton instance
export const socketConnectionRecovery = new SocketConnectionRecovery();

export default socketConnectionRecovery; 
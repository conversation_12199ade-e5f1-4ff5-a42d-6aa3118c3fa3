import { Socket } from "socket.io";
import { UserService } from "../../services";
import { FOLLOW_EMITS, FOLLOW_MESSAGES } from "../../constants/socketMessage";
import { sendNotification } from "../../models/notification/notification.model";
import { Schema } from "mongoose";
import { ENotificationType } from "../../types/enum";
/**
 * Following socket handler is responsible for following and unfollowing user.
 * @param socket socket
 * @param targetUserId user Id of user which needs to be followed or unfollowed.
 * @returns socket emits for success and errors
 */
export const followUserHandler = async (
  socket: Socket,
  targetUserId: string
) => {
  try {
    const user = socket.user;
    if (!targetUserId) {
      return socket.emit(FOLLOW_EMITS.ERROR, FOLLOW_MESSAGES.BAD_REQUEST);
    }

    if (targetUserId === user._id.toString()) {
      return socket.emit(FOLLOW_EMITS.ERROR, FOLLOW_MESSAGES.INVALID_ACTION);
    }
    const targetUser = await UserService.getUser(targetUserId);

    if (!targetUser) {
      return socket.emit(FOLLOW_EMITS.ERROR, FOLLOW_MESSAGES.NOT_FOUND);
    }

    if (user.following.includes(targetUserId)) {
      user.following = user.following.filter(
        (id: Schema.Types.ObjectId) => id.toString() !== targetUserId
      );
      targetUser.followers = targetUser.followers.filter(
        (id) => id.toString() !== user._id.toString()
      );
      console.log(targetUser.followers);
    } else {
      user.following.push(targetUserId);
      targetUser.followers.push(user._id);

      const isSent = await sendNotification({
        userId: targetUserId,
        personId: user._id,
        type: ENotificationType.newFollower,
        contentId: null,
        message: "",
        commentId: undefined,
        contentType: null,
      });

      if (!isSent) {
        socket.emit(FOLLOW_EMITS.ERROR, FOLLOW_MESSAGES.NOTIFICATION_ERROR);
      }
    }

    await user.save();
    await targetUser.save();

    socket.emit(FOLLOW_EMITS.FOLLOW_RESPONSE, {
      success: true,
      followingCount: user.following.length,
    });

    socket.to(targetUserId).emit(FOLLOW_EMITS.FOLLOW_UPDATE, {
      followersCount: targetUser.followers.length,
    });
  } catch (err) {
    console.error("Follow/Unfollow error:", err);
    socket.emit(FOLLOW_EMITS.ERROR, FOLLOW_MESSAGES.SERVER_ERROR);
  }
};

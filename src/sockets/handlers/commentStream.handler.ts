import { Socket } from "socket.io";
import Stream from "../../models/stream/stream.schema";
import Comment from "../../models/comment/comment.schema";
import { sendNotification } from "../../models/notification/notification.model";

import {
  COMMENT_EMITS,
  COMMENT_MESSAGES,
  SOCKET_EMIT_MESSAGES,
} from "../../constants/socketMessage";
import { ENotificationContentModel, ENotificationType } from "../../types/enum";
/**
 * We Create the comment data.
 * If it's a reply, we set the parentId to the parent comment's ID.
 * If the comment is not a reply, We will add it to the stream's comments array.
 * If it's a reply, we can add the comment to the parent comment's replies array.
 * Post that we will send notification about the reply.
 * And the save it to Db and send the newly created comment with populated user information.
 * Finally the response emit "deletedComment" event is emitted.
 * @param socket
 * @param streamId
 * @param content
 * @param replyTo
 */
export const commentStreamHandler = async (
  socket: Socket,
  streamId: string,
  content?: string,
  replyTo?: string
) => {
  try {
    if (socket.user) {
      const commentData: any = {
        user: socket.user._id,
        stream: streamId,
        content,
      };

      if (replyTo) {
        commentData.parentId = replyTo;
      }

      const comment = new Comment(commentData);

      const updatedStream = await Stream.findByIdAndUpdate(streamId, {
        $addToSet: { comments: comment._id },
      }).populate("likes", "-_id -__v -password");

      if (replyTo) {
        await Comment.findByIdAndUpdate(replyTo, {
          $addToSet: { replies: comment._id },
        });
        if (updatedStream?.creator?.toString() !== socket.user._id.toString()) {
          const isSent = await sendNotification({
            userId: (updatedStream?.creator ?? "").toString(),
            personId: socket.user._id,
            type: ENotificationType.commentReply,
            contentId: streamId,
            message: content,
            commentId: replyTo,
            contentType: ENotificationContentModel.stream,
          });

          if (!isSent) {
            socket.emit(
              COMMENT_EMITS.ERROR,
              SOCKET_EMIT_MESSAGES.NOTIFICATION_ERROR
            );
          }
        }
      } else {
        if (socket.user._id.toString() !== comment.user.toString()) {
          const isSent = await sendNotification({
            userId: (updatedStream?.creator ?? "").toString(),
            personId: socket.user._id,
            type: ENotificationType.newMessage,
            contentId: streamId,
            message: content,
            commentId: undefined,
            contentType: ENotificationContentModel.stream,
          });

          if (!isSent) {
            socket.emit(
              COMMENT_EMITS.ERROR,
              SOCKET_EMIT_MESSAGES.NOTIFICATION_ERROR
            );
          }
        }
      }

      await comment.save();

      const populatedComment = await Comment.findById(comment._id)
        .populate("user", "username displayName profilePicture")
        .lean();

      socket.emit(COMMENT_EMITS.ADD_COMMENT_RESPONSE_EMIT, {
        success: true,

        comment: populatedComment,
      });
    } else {
      socket.emit(COMMENT_EMITS.ERROR, SOCKET_EMIT_MESSAGES.UNAUTHORIZED);
    }
  } catch (error) {
    console.log(error);
    socket.emit("error", error);
  }
};

export const deleteCommentFromStreamHandler = async (
  socket: Socket,
  commentId: string
) => {
  try {
    const comment = await Comment.findById(commentId);

    if (!comment) {
      return socket.emit(COMMENT_EMITS.ERROR, {
        success: false,
        message: COMMENT_MESSAGES.COMMENT_NOT_FOUND,
      });
    }

    if (socket.user._id.toString() !== comment.user.toString()) {
      return socket.emit(COMMENT_EMITS.ERROR, {
        success: false,
        message: COMMENT_MESSAGES.UNAUTHORIZED,
      });
    }

    await Comment.deleteMany({ parentId: commentId });

    await comment.deleteOne();

    await Stream.findByIdAndUpdate(comment.stream, {
      $pull: { comments: comment._id },
    });

    // Notify other users about the deletion
    // socket.to(comment.stream.toString()).emit("commentDeletedStream", {
    //   success: true,
    //   commentId,
    // });

    socket.emit(COMMENT_EMITS.DELETED_COMMENT, {
      success: true,
      commentId,
    });
  } catch (error) {
    console.error(error);
    socket.emit(COMMENT_EMITS.ERROR, {
      success: false,
      message: COMMENT_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

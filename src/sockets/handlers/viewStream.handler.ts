import { Socket } from "socket.io";
import { Types } from "mongoose";

import Stream from "../../models/stream/stream.schema";
import WatchHistory from "../../models/watchHistory/watchHistory.schema";
import { VIEW_EMITS, VIEW_MESSAGES } from "../../constants/socketMessage";

/**
 * VyooO stream socket handler
 * @param socket socket instance
 * @param streamId id of stream got VyoooD
 * @returns
 */
export const viewStreamHandler = async (socket: Socket, streamId: string) => {
  {
    try {
      if (!Types.ObjectId.isValid(streamId)) {
        return socket.emit(VIEW_EMITS.ERROR, VIEW_MESSAGES.INVALID_STREAM_ID);
      }
      const userId = socket.user._id;

      const stream = await Stream.findByIdAndUpdate(
        streamId,
        {
          $addToSet: { views: userId },
        },
        { new: true }
      ).populate("likes", "-_id -__v -password");

      if (!stream) {
        return socket.emit(VIEW_EMITS.ERROR, VIEW_MESSAGES.STREAM_NOT_FOUND);
      }
      await WatchHistory.findOneAndUpdate(
        { userId, streamId },
        { watchedAt: new Date() },
        { upsert: true, new: true }
      );

      socket.emit(VIEW_EMITS.VIEW_UPDATED, {
        success: true,
        viewers: stream.views,
        viewcount: stream.views.length,
      });
    } catch (error) {
      console.error("Error updating views:", error);
      socket.emit(VIEW_EMITS.ERROR, VIEW_MESSAGES.VIEW_UPDATE_ERROR);
    }
  }
};

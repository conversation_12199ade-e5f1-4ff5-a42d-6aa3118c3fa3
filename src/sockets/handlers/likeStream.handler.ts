import { Socket } from "socket.io";
import Stream from "../../models/stream/stream.schema";
import Comment from "../../models/comment/comment.schema";
import { sendNotification } from "../../models/notification/notification.model";
import { updateStreamLikes } from "../../models/stream/stream.model";
import {
  LIKE_EMITS,
  SOCKET_EMIT_MESSAGES,
} from "../../constants/socketMessage";
import { ENotificationContentModel, ENotificationType } from "../../types/enum";
//following function sends like notification to everyone including user who likes their own video
// export const likeStreamHandler = async (
//   socket: Socket,
//   streamId: string,
//   action: "like" | "unlike"
// ) => {
//   if (socket.user) {
//     const result = await updateStreamLikes(socket.user._id, streamId, action);
//     const stream = await Stream.findById(streamId).select("creator").lean();
//     socket.emit("likedStream", {
//       success: true,
//       action,
//       likes: result?.likesCount,
//     });

//     if ("like" === action) {
//       const isSent = await sendNotification({
//         userId: (stream?.creator ?? "").toString(),
//         personId: socket.user._id,
//         type: "like",
//         streamId,
//         message: "",
//         commentId: undefined,
//       });

//       if (!isSent) {
//         socket.emit("error", "Error sending notification");
//       }
//     }
//   } else {
//     socket.emit("error", "Unauthorized");
//   }
// };

/**
 *
 * @param socket socket
 * @param streamId Id of stream that user wants to like
 * @param action Type of action "like" | "unlike"
 */
export const likeStreamHandler = async (
  socket: Socket,
  streamId: string,
  action: "like" | "unlike"
) => {
  if (socket.user) {
    const result = await updateStreamLikes(socket.user._id, streamId, action);
    const stream = await Stream.findById(streamId).select("creator").lean();
    socket.emit(LIKE_EMITS.LIKE_RESPONSE_EMIT, {
      success: true,
      action,
      likes: result?.likesCount,
    });

    if (LIKE_EMITS.LIKE_ACTION === action) {
      if (stream?.creator?.toString() !== socket.user._id.toString()) {
        const isSent = await sendNotification({
          userId: (stream?.creator ?? "").toString(),
          personId: socket.user._id,
          type: ENotificationType.like,
          contentId: streamId,
          message: "",
          commentId: undefined,
          contentType: ENotificationContentModel.stream,
        });

        if (!isSent) {
          socket.emit(
            LIKE_EMITS.ERROR,
            SOCKET_EMIT_MESSAGES.NOTIFICATION_ERROR
          );
        }
      }
    }
  } else {
    socket.emit(LIKE_EMITS.ERROR, SOCKET_EMIT_MESSAGES.UNAUTHORIZED);
  }
};

import { UriOptions } from "joi";
import { Document, ObjectId, Schema, Types } from "mongoose";
import {
  ENotificationContentModel,
  ENotificationType,
  EShareContentModel,
} from "./enum";

export type Providers = "email" | "google" | "facebook" | "apple";

export interface IAuthProvider {
  provider: Providers;
  id?: string;
}

export type NotificationSettings = {
  [key: string]: boolean;
};
export type ThemeSettings = "light" | "dark" | "system";
export type DeleteReason = {
  category: string;
  reason: string;
  deletedAt: Date;
};

export interface ProfileSettings {
  notifications: NotificationSettings;
  theme: ThemeSettings;
}

export type Visibility = "followers" | "everyone" | "subscribers" | "none";

export interface PostSettings {
  postVisibility: Visibility;
  liveVisibility: Visibility;
  liveVideoPricing: {
    currency: "USD" | "INR";
    price: number;
  };
}

export interface Subscription {
  plan: Schema.Types.ObjectId;
  status: "active" | "expired";
}

export interface IUser extends Document {
  username: string;
  email: string;
  phoneNumber: string;
  displayName?: string;
  bio?: string;
  profilePicture?: string;
  password?: string;
  provider?: IAuthProvider;
  followers: Schema.Types.ObjectId[];
  following: Schema.Types.ObjectId[];
  notifications: Schema.Types.ObjectId[];
  verified: boolean;
  verifyPassword: (password: string) => Promise<boolean>;
  isDeleted: boolean;
  stripeAccountId?: string;
  stripeCustomerId?: string;
  fcmToken?: string;
  deletedReason: DeleteReason[];
  profileSettings: ProfileSettings;
  postSettings: PostSettings;
  platformSubscription: Subscription | null;
  creatorSubscriptions: Subscription[];
  notInterestedIn: Schema.Types.ObjectId[];
  lastLogin: Date;
  isProfileComplete?: boolean;
  userInstagram?: string;
  userFacebook?: string;
  userYoutube?: string;
  onboardingSteps: {
    emailVerified: boolean;
    phoneVerified: boolean;
    interestsFilled: boolean;
    usernameSet: boolean;
  };
  sharesCount: number;
}

export interface IUserWithStreams extends IUser {
  streams: IStream[];
}

export type StreamType = "vr" | "video" | "video-live";
export type StreamStatus =
  | "uploading"
  | "draft"
  | "published"
  | "ended"
  | "uploaded";

export interface IStreamSettings {
  visibility: Visibility;
}

export interface IShare {
  sharedBy: Schema.Types.ObjectId;
  url: String;
  platform?: string;
  contentId: ObjectId;
  contentType: EShareContentModel;
  createdAt?: string;
  updatedAt?: string;
}

export interface IWatchHistory {
  userId: Schema.Types.ObjectId;
  streamId: Schema.Types.ObjectId;
  watchedAt: Date;
  totalWatchTime: number;
}

export interface ILiveChat {
  user: Schema.Types.ObjectId;
  streamId: Schema.Types.ObjectId;
  message: string;
  timestamp: Date;
}

interface IModeration {
  reviewedBy: string;
  reviewTimestamp: Date;
  reason: string;
}
export interface IStream extends Document {
  creator: Schema.Types.ObjectId;
  url?: string;
  transcodedUrl?: string;
  type: StreamType;
  status: StreamStatus;
  isLive: boolean;
  title: string;
  description?: string;
  category?: string;
  tags?: string[];
  cover?: string;
  views: Schema.Types.ObjectId[];
  likes: Schema.Types.ObjectId[];
  reactions: Schema.Types.ObjectId[];
  comments: Schema.Types.ObjectId[];
  sharesCount: number;
  settings: IStreamSettings;
  isDeleted: boolean;
  createdAt: Date;
  endedAt?: Date;
  videoLength?: number;
  liveStreamId: string;
  liveUrl: string;
  liveUrls: Map<string, string>;
  vodUrls: Map<string, string>;
  streamKey: string;
  rtmpUrl: string;
  vodStatus: String;
  moderationDetails: IModeration;
}

export interface IReport extends Document {
  entityId: Schema.Types.ObjectId;
  entityType: "stream" | "short" | "comment" | "user";
  reporter: Schema.Types.ObjectId;
  reason:
    | "Spam or misleading"
    | "Sexual content"
    | "Inappropriate hateful or abusive content"
    | "Harmful or dangerous acts"
    | "Violent or repulsive content";
  description?: string;
  status: "pending" | "reviewed" | "resolved" | "rejected";
  adminFeedback?: string;
  reviewedBy?: Schema.Types.ObjectId;
  reviewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface INotInterested extends Document {
  userId: Schema.Types.ObjectId;
  contentId: Schema.Types.ObjectId;
  contentType: String;
  reason: String;
  createdAt: Date;
}
//for aggregation service
export interface ICreatorDetails {
  _id: Types.ObjectId;
  bio?: string;
  username: string;
  profilePicture?: string;
  displayName: string;
  notInterestedIn?: string[];
  isFollowing: boolean;
}
//for aggregation service
export interface ITrendingStream {
  _id: Types.ObjectId;
  title: string;
  description?: string;
  url?: string;
  transcodedUrl?: string;
  category?: string;
  tags?: string[];
  cover?: string;
  videoLength?: number;
  type: StreamType;
  status: StreamStatus;
  commentCount: number;
  viewsCount: number;
  likesCount: number;
  sharesCount: number;
  isLiked: boolean;
  creator: ICreatorDetails;
}

export interface IDeleteStreamResult {
  stream: IStream | null;
}

export interface IFeaturedStream extends Document {
  userId: Types.ObjectId;
  streams: Types.ObjectId[];
}

export type ReactionType = "like";

export interface IReaction extends Document {
  user: Schema.Types.ObjectId;
  stream: Schema.Types.ObjectId;
  type: ReactionType;
  createdAt: Date;
}

export interface IInterest extends Document {
  category: string;
  subCategories: string[];
}

export interface IUserInterests extends Document {
  userId: Schema.Types.ObjectId;
  interests: {
    category?: string;
    subCategories?: string[];
  }[];
}

export interface IComment extends Document {
  user: Schema.Types.ObjectId;
  stream: Schema.Types.ObjectId;
  content: string;
  parentId?: Schema.Types.ObjectId;
  replies: Schema.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

export interface INotification extends Document {
  user: Schema.Types.ObjectId;
  type: ENotificationType;
  message?: string;
  isRead: boolean;
  createdAt: Date;
  person: Schema.Types.ObjectId;
  comment?: Schema.Types.ObjectId | null;
  // stream?: Schema.Types.ObjectId | null;

  // Updated dynamic reference fields
  contentId?: Types.ObjectId | null;
  contentType?: "Stream" | "Story" | "Shorts" | null;
}

export interface IEmailVerificationToken extends Document {
  user: Schema.Types.ObjectId;
  token: string;
  createdAt: Date;
}

export type BillingInterval = "month" | "year";

export interface ISubscriptionPlan extends Document {
  creator: Schema.Types.ObjectId | null;
  title: string;
  description?: string;
  billingInterval: BillingInterval;
  price: number;
  currency: string;
  features: string[];
  stripePriceId: string;
  createdAt: Date;
}

export interface IStreamView extends Document {
  user: Schema.Types.ObjectId;
  stream: Schema.Types.ObjectId;
  minutesWatched: number;
  lastUpdated: Date;
  lastHeartbeat: Date;
  socketId: string;
}

export interface IPayment extends Document {
  user: Schema.Types.ObjectId;
  amount: number;
  currency: string;
  paymentMethod: string;
  status: string;
  stripePaymentIntentId: string;
  createdAt: Date;
}

export interface IProblemReport extends Document {
  user: Schema.Types.ObjectId;
  category: string;
  message: string;
  images: string[];
  timestamp: Date;
}

export interface SendNotificationParams {
  userId: string; // receiver
  personId: string; // sender/trigger person
  type: ENotificationType;
  shortId?: string | null;
  message?: string; // optional for certain types
  commentId?: string; // optional comment ID
  contentId?: string | null;
  contentType: ENotificationContentModel | null;
}

export interface FirebaseMessage {
  title: string;
  body: string;
}

export enum PlatformType {
  IOS = "IOS",
  ANDROID = "ANDROID",
  WEB = "WEB",
}

export interface IDevice extends Document {
  platform: PlatformType;
  OSVersion?: string;
  active: boolean;
  userId?: Schema.Types.ObjectId;
  fcmToken: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITrendingScore extends Document {
  streamId: Schema.Types.ObjectId;
  trendingScore: number;
}

// Single text overlay
export interface ITextOverlay {
  id: string;
  text: string;
  font: string;
  color: string;
  position: {
    x: number;
    y: number;
  };
  scale: number;
  rotation: number;
}

// Single path inside a doodle overlay
export interface IDoodlePath {
  path: string;
  color: string;
  strokeWidth: number;
}

// Single doodle overlay with paths
export interface IDoodleOverlay {
  id: string;
  paths: IDoodlePath[];
  scale: number;
  rotation: number;
}

export interface IStory extends Document {
  user: Schema.Types.ObjectId;
  media: string[];
  caption?: string;
  location?: {
    coordinates: {
      lat: string;
      lng: string;
    };
    address: string;
  };
  viewBy: IUser[];
  createdAt: Date;
  expiresAt: Date;
  textOverlays: ITextOverlay[];
  doodleOverlays: IDoodleOverlay[];
}

export interface IShorts extends Document {
  description: string;
  videoUrl?: string;
  thumbnailUrl: string;
  saved: IUser[];
  audioDetails?: {
    audioId?: string;
    title?: string;
    artist?: string;
    duration?: number;
  };
  shortDuration: number;
  tags: string[];
  author: Schema.Types.ObjectId;
  likes: Schema.Types.ObjectId[];
  likesCount: number;
  commentCount: number;
  viewsCount: Number;
  location?: {
    coordinates: {
      lat: string;
      lng: string;
    };
    address: string;
  };
  sharesCount: number;
  notInterestedBy?: {
    userId: IUser[];
    reason: string;
  }[];
  reportedBy: IUser[];
  interactionScore: number;
  createdAt: Date;
  updatedAt: Date;
  category?: string;
}

export interface ITrendingShortScore {
  shortId: Schema.Types.ObjectId;
  trendingScore: number;
}

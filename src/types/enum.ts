export enum EUserProvider {
  email = "email",
  google = "google",
  facebook = "facebook",
  apple = "apple",
  phone = "phoneNumber",
}

export enum ENotificationType {
  newMessage = "newMessage",
  newFollower = "newFollower",
  commentReply = "commentReply", //send only if user comment notification setting is on in profile
  like = "like", //send only if user like notification setting is on in profile
  streamUpdate = "streamUpdate",
  recommendedContent = "recommendedContent",
  post = "post",
}

export enum ENotificationContentModel {
  story = "Story",
  stream = "Stream",
  short = "Shorts",
}

export enum EShareContentModel {
  stream = "Stream",
  short = "Shorts",
  user = "User",
}

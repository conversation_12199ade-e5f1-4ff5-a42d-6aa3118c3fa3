import mongoose, { Types } from "mongoose";
import ShareModel from "../../models/share/share.schema";
import { EShareContentModel } from "../../types/enum";
import { IShare } from "../../types/schema";

export const createShare = async (data: IShare): Promise<IShare> => {
  const share = new ShareModel(data);
  const newShare = await share.save();

  // Increase share count
  const shareCountIncreased = await increaseShareCount(
    String(data.contentId),
    data.contentType as EShareContentModel
  );
  if (!shareCountIncreased) {
    throw new Error(`Failed to increase share count of ${data.contentType}`);
  }
  return newShare;
};

export const increaseShareCount = async (
  id: string,
  contentType: EShareContentModel
): Promise<boolean> => {
  try {
    const objectId = new Types.ObjectId(id);
    await mongoose.model(contentType).findByIdAndUpdate(objectId, {
      $inc: { sharesCount: 1 },
    });
    return true;
  } catch (error) {
    console.error("Error in increaseShareCount in stream service:", error);
    return false;
  }
};

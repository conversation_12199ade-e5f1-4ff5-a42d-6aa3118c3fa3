import { NOT_INTERESTED_MESSAGES } from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import {  NotInterested } from "../../models/notInterestedItems/notInterested.schema";
import ShortsModel from "../../models/shorts/shorts.schema";
import Stream from "../../models/stream/stream.schema";
import { INotInterested, IStream } from "../../types/schema";
import { ApiResponse, ResultDB } from "../../utils/helper";
import {Model as MongooseModel} from "mongoose"


export const addToNotInterested = async (
  userId: string,
  contentId: string,
  contentType: "stream" | "short"
): Promise<ApiResponse<INotInterested>> => {
  let item: any = null;
  let ownerId: string | undefined;

  if (contentType === "stream") {
    item = await Stream.findById(contentId).lean();
    ownerId = item?.creator?.toString();
  } else if (contentType === "short") {
    item = await ShortsModel.findById(contentId).lean();
    ownerId = item?.author?.toString();
  }

  if (!item) {
    return ResultDB<INotInterested>(
      STATUS_CODES.NOT_FOUND,
      false,
      NOT_INTERESTED_MESSAGES.NOT_FOUND,
      null
    );
  }

  if (ownerId === userId) {
    return ResultDB<INotInterested>(
      STATUS_CODES.FORBIDDEN,
      false,
      NOT_INTERESTED_MESSAGES.CANNOT_ADD,
      null
    );
  }

  const exists = await NotInterested.findOne({ userId, contentId, contentType });
  if (exists) {
    return ResultDB<INotInterested>(
      STATUS_CODES.CONFLICT,
      false,
      NOT_INTERESTED_MESSAGES.ALREADY_EXISTS,
      null
    );
  }

  const notInterested = new NotInterested({ userId, contentId, contentType });
  await notInterested.save();

  return ResultDB<INotInterested>(
    STATUS_CODES.CREATED,
    true,
    NOT_INTERESTED_MESSAGES.SUCCESS,
    notInterested
  );
};


export const removeFromNotInterested = async (
  userId: string,
  contentId: string,
  contentType: "stream" | "short"
): Promise<ApiResponse<INotInterested>> => {
  const deleted = await NotInterested.findOneAndDelete({ userId, contentId, contentType });
  if (!deleted) {
    return ResultDB<INotInterested>(
      STATUS_CODES.NOT_FOUND,
      false,
      NOT_INTERESTED_MESSAGES.NOT_FOUND,
      null
    );
  }

  return ResultDB<INotInterested>(
    STATUS_CODES.OK,
    true,
    NOT_INTERESTED_MESSAGES.DELETED,
    deleted
  );
};





/**
 * Following function is revised function for above one.
 * will be using this once the Author field changes to creator
 */

// export const addToNotInterested = async (
//     userId: string,
//     itemId: string,
//     contentType: "stream" | "short"
//   ): Promise<ApiResponse<INotInterested>> => {
//     const Model = contentType === "stream" ? Stream : ShortsModel;
  
//     const item = await Model.findById(itemId).lean();
//     if (!item) {
//       return ResultDB(STATUS_CODES.NOT_FOUND, false, NOT_INTERESTED_MESSAGES.NOT_FOUND, null);
//     }
  
//     if (item.creator?.toString() === userId) {
//       return ResultDB(STATUS_CODES.FORBIDDEN, false, NOT_INTERESTED_MESSAGES.CANNOT_ADD, null);
//     }
  
//     const exists = await NotInterested.findOne({ userId, itemId, contentType });
//     if (exists) {
//       return ResultDB(STATUS_CODES.CONFLICT, false, NOT_INTERESTED_MESSAGES.ALREADY_EXISTS, null);
//     }
  
//     const notInterested = new NotInterested({ userId, itemId, contentType });
//     await notInterested.save();
  
//     return ResultDB(STATUS_CODES.CREATED, true, NOT_INTERESTED_MESSAGES.SUCCESS, notInterested);
//   };
  
  
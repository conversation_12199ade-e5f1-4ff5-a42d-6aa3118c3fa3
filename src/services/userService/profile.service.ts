import mongoose from "mongoose";
import { ApiResponse, ResultDB } from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  MESSAGES,
  STREAM_MESSAGES,
  USER_MESSAGES,
} from "../../constants/responseMessage";

import { ProfileView } from "../../models/profileViews/profileView.schema";
import User from "../../models/user/user.schema";
import { FollowSearchStage } from "../../utils/pipelineStages";
import Stream from "../../models/stream/stream.schema";

// export const getProfileViewersWithMetaData = async (
//   targetId: string,
//   currentUserId: string,
//   page = 1,
//   limit = 10
// ): Promise<ApiResponse<any>> => {
//   const skip = (page - 1) * limit;

//   const matchStage = { target: new mongoose.Types.ObjectId(targetId) };

//   const [viewers, totalCount] = await Promise.all([
//     ProfileView.aggregate([
//       { $match: matchStage },
//       {
//         $lookup: {
//           from: "users",
//           localField: "viewer",
//           foreignField: "_id",
//           as: "viewerInfo",
//         },
//       },
//       { $unwind: "$viewerInfo" },
//       {
//         $project: {
//           _id: 0,
//           viewedAt: 1,
//           viewerId: "$viewerInfo._id",
//           username: "$viewerInfo.username",
//           displayName: "$viewerInfo.displayName",
//           profilePicture: "$viewerInfo.profilePicture",
//           isFollowing: {
//             $in: [
//               new mongoose.Types.ObjectId(currentUserId),
//               "$viewerInfo.followers",
//             ],
//           },
//         },
//       },
//       { $sort: { viewedAt: -1 } },
//       { $skip: skip },
//       { $limit: limit },
//     ]),
//     ProfileView.countDocuments(matchStage),
//   ]);

//   return ResultDB(STATUS_CODES.OK, true, USER_MESSAGES.FETCHED_VIEWERS, {
//     total: totalCount,
//     page,
//     totalPages: Math.ceil(totalCount / limit),
//     data: viewers,
//   });
// };

export const getProfileViewersWithMetaData = async (
  userId: string,
  page = 1,
  limit = 10
): Promise<ApiResponse<any>> => {
  const skip = (page - 1) * limit;
  const matchStage = { target: new mongoose.Types.ObjectId(userId) };

  const [viewers, totalCount] = await Promise.all([
    ProfileView.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: "users",
          localField: "viewer",
          foreignField: "_id",
          as: "viewerInfo",
        },
      },
      { $unwind: "$viewerInfo" },
      {
        $project: {
          _id: 0,
          viewedAt: 1,
          viewerId: "$viewerInfo._id",
          username: "$viewerInfo.username",
          displayName: "$viewerInfo.displayName",
          profilePicture: "$viewerInfo.profilePicture",
          isFollowing: {
            $in: [new mongoose.Types.ObjectId(userId), "$viewerInfo.followers"],
          },
        },
      },
      { $sort: { viewedAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ]),
    ProfileView.countDocuments(matchStage),
  ]);

  return ResultDB(STATUS_CODES.OK, true, USER_MESSAGES.FETCHED_VIEWERS, {
    total: totalCount,
    page,
    totalPages: Math.ceil(totalCount / limit),
    data: viewers,
  });
};

/**
 * following function is used to mark a profile as viewed by a user.
 * this function requres few modifications.
 * @param viewerId
 * @param targetUserId
 * @returns
 */
export const markProfileViewed = async (
  viewerId: string,
  targetUserId: string
): Promise<ApiResponse<null>> => {
  try {
    if (viewerId === targetUserId) {
      return ResultDB(
        STATUS_CODES.BAD_REQUEST,
        false,
        USER_MESSAGES.CANNOT_VIEW_OWN_PROFILE,
        null
      );
    }

    // Try to insert view, skip if duplicate
    try {
      await ProfileView.create({ viewer: viewerId, target: targetUserId });
      return ResultDB(STATUS_CODES.CREATED, true, USER_MESSAGES.SUCCESS, null);
    } catch (err: any) {
      if (err.code === 11000) {
        return ResultDB(
          STATUS_CODES.OK,
          true,
          "This Profile is already viewed",
          null
        );
      }

      // Other DB errors
      return ResultDB(
        STATUS_CODES.INTERNAL_SERVER_ERROR,
        false,
        USER_MESSAGES.DATABASE_ERROR,
        null
      );
    }
  } catch (err) {
    console.error("Error in markProfileViewed:", err);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const getTotalLikesByCreator = async (
  creatorId: string
): Promise<ApiResponse<number | null>> => {
  try {
    const [result] = await Stream.aggregate([
      {
        $match: {
          creator: new mongoose.Types.ObjectId(creatorId),
          isDeleted: false,
        },
      },
      {
        $project: {
          likeCount: {
            $size: {
              $cond: {
                if: { $isArray: "$likes" },
                then: "$likes",
                else: []
              }
            }
          },
        },
      },
      {
        $group: {
          _id: null,
          totalLikes: { $sum: "$likeCount" },
        },
      },
    ]);

    const totalLikes = result?.totalLikes || 0;

    return ResultDB(
      STATUS_CODES.OK,
      true,
      STREAM_MESSAGES.TOTAL_LIKES_FETCHED,
      totalLikes
    );
  } catch (error) {
    console.error("Error fetching total likes:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STREAM_MESSAGES.ERROR_TOTAL_LIKES,
      null
    );
  }
};

export const getStreamsLikedByUser = async (
  userId: string,
  page = 1,
  limit = 10
): Promise<ApiResponse<any>> => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: any[] = [
      {
        $match: {
          likes: new mongoose.Types.ObjectId(userId),
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          videoLength: 1,
          transcodedUrl: 1,
          liveUrls: 1,
          vodUrls: 1,
          liveUrl: 1,
          url: 1,
          type: 1,
          status: 1,
          isLive: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          cover: 1,
          createdAt: 1,
          viewsCount: { $size: "$views" },
          likesCount: { $size: "$likes" },
          isLiked: { $literal: true },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
          comments: { $size: "$comments" },
        },
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ];

    const [streams, total] = await Promise.all([
      Stream.aggregate(pipeline),
      Stream.countDocuments({
        likes: new mongoose.Types.ObjectId(userId),
        isDeleted: false,
      }),
    ]);

    return ResultDB(STATUS_CODES.OK, true, MESSAGES.SUCCESS, {
      page,
      total,
      totalPages: Math.ceil(total / limit),
      data: streams,
    });
  } catch (error) {
    console.error("Error fetching liked streams:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const getFollowersService = async (
  targetUserId: string,
  currentUserId: string,
  page: number = 1,
  limit: number = 10,
  search: string = ""
) => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: any[] = [
      { $match: { _id: new mongoose.Types.ObjectId(targetUserId) } },
      {
        $lookup: {
          from: "users",
          localField: "followers",
          foreignField: "_id",
          as: "followersData",
        },
      },
      { $unwind: "$followersData" },
      { $replaceRoot: { newRoot: "$followersData" } },
      ...FollowSearchStage(search),
      { $skip: skip },
      { $limit: limit },
      {
        $addFields: {
          isFollowedByCurrentUser: {
            $in: [new mongoose.Types.ObjectId(currentUserId), "$followers"],
          },
          followerCount: {
            $size: {
              $cond: {
                if: { $isArray: "$followers" },
                then: "$followers",
                else: []
              }
            }
          },
          followingCount: {
            $size: {
              $cond: {
                if: { $isArray: "$following" },
                then: "$following",
                else: []
              }
            }
          },
        },
      },
      {
        $project: {
          username: 1,
          displayName: 1,
          profilePicture: 1,
          isFollowedByCurrentUser: 1,
          followerCount: 1,
          followingCount: 1,
        },
      },
    ];

    const followers = await User.aggregate(pipeline);
    return ResultDB(STATUS_CODES.OK, true, USER_MESSAGES.FETCHED_FOLLOWERS, {
      followers,
      pagination: { currentPage: page, limit },
    });
  } catch (error) {
    console.error("Error in getFollowersService:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const getFollowingService = async (
  targetUserId: string,
  currentUserId: string,
  page: number = 1,
  limit: number = 10,
  search: string = ""
) => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: any[] = [
      { $match: { _id: new mongoose.Types.ObjectId(targetUserId) } },
      {
        $lookup: {
          from: "users",
          localField: "following",
          foreignField: "_id",
          as: "followingData",
        },
      },
      { $unwind: "$followingData" },
      { $replaceRoot: { newRoot: "$followingData" } },
      ...FollowSearchStage(search),
      { $skip: skip },
      { $limit: limit },
      {
        $addFields: {
          isFollowedByCurrentUser: {
            $in: [new mongoose.Types.ObjectId(currentUserId), "$followers"],
          },
          followerCount: {
            $size: {
              $cond: {
                if: { $isArray: "$followers" },
                then: "$followers",
                else: []
              }
            }
          },
          followingCount: {
            $size: {
              $cond: {
                if: { $isArray: "$following" },
                then: "$following",
                else: []
              }
            }
          },
        },
      },
      {
        $project: {
          username: 1,
          displayName: 1,
          profilePicture: 1,
          isFollowedByCurrentUser: 1,
          followerCount: 1,
          followingCount: 1,
        },
      },
    ];

    const following = await User.aggregate(pipeline);
    return ResultDB(STATUS_CODES.OK, true, USER_MESSAGES.FETCHED_FOLLOWING, {
      following,
      pagination: { currentPage: page, limit },
    });
  } catch (error) {
    console.error("Error in getFollowingService:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

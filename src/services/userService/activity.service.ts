import mongoose, { PipelineStage } from "mongoose";
import { SAVE_ITEMS } from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import { SavedItem } from "../../models/savedItem/saveditem.schema";
import { ResultDB } from "../../utils/helper";
import { buildProjectStage } from "../../utils/pipelineStages";

export const saveItem = async (
  userId: string,
  itemId: string,
  itemType: "stream" | "short"
) => {
  try {
    await SavedItem.updateOne(
      { userId, itemId, itemType },
      { $set: { savedAt: new Date() } },
      { upsert: true }
    );

    return ResultDB(STATUS_CODES.OK, true, SAVE_ITEMS.SAVED_SUCCESSFULLY, null);
  } catch (error) {
    console.error("Error saving item:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SAVE_ITEMS.ERROR_SAVING,
      null
    );
  }
};

export const unsaveItem = async (
  userId: string,
  itemId: string,
  itemType: "stream" | "short"
) => {
  try {
    await SavedItem.deleteOne({ userId, itemId, itemType });

    return ResultDB(
      STATUS_CODES.OK,
      true,
      SAVE_ITEMS.UNSAVED_SUCCESSFULLY,
      null
    );
  } catch (error) {
    console.error("Error unsaving item:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SAVE_ITEMS.ERROR_UNSAVING,
      null
    );
  }
};

export const getSavedItemsService = async (
  userId: string,
  type: "stream" | "short",
  page: number = 1,
  limit: number = 10
) => {
  try {
    const skip = (page - 1) * limit;

    const pipeline = buildSavedItemsPipeline(userId, type, skip, limit);

    const savedItems = await SavedItem.aggregate(pipeline);

    return ResultDB(
      STATUS_CODES.OK,
      true,
      SAVE_ITEMS.FETCHED_SUCCESSFULLY,
      savedItems
    );
  } catch (error) {
    console.error("Error in getSavedItemsService:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SAVE_ITEMS.ERROR_FETCHING,
      null
    );
  }
};

const buildSavedItemsPipeline = (
  userId: string,
  type: "stream" | "short",
  skip: number,
  limit: number
) => {
  const collectionName = type === "stream" ? "streams" : "shorts";

  const pipeline: any[] = [
    {
      $match: { userId: new mongoose.Types.ObjectId(userId), itemType: type },
    },
    {
      $lookup: {
        from: collectionName,
        localField: "itemId",
        foreignField: "_id",
        as: "itemDetails",
      },
    },
    { $unwind: "$itemDetails" },
  ];

  if (type === "stream") {
    pipeline.push(
      {
        $lookup: {
          from: "users",
          localField: "itemDetails.creator",
          foreignField: "_id",
          as: "itemDetails.creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$itemDetails.creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      }
    );
  }

  pipeline.push(
    { $sort: { savedAt: -1 } },
    { $skip: skip },
    { $limit: limit },
    { $project: buildProjectStage(type, userId) }
  );

  return pipeline;
};

// export const getAllSavedItemsService = async (
//   userId: string,
//   page = 1,
//   limit = 10
// ) => {
//   try {
//     const skip = (page - 1) * limit;
//     const userObjectId = new mongoose.Types.ObjectId(userId);

//     // STREAMS aggregation
//     const streamPipeline: any[] = [
//       {
//         $match: {
//           userId: userObjectId,
//           itemType: "stream",
//         },
//       },
//       {
//         $lookup: {
//           from: "streams",
//           localField: "itemId",
//           foreignField: "_id",
//           as: "itemDetails",
//         },
//       },
//       { $unwind: "$itemDetails" },
//       {
//         $lookup: {
//           from: "users",
//           localField: "itemDetails.creator",
//           foreignField: "_id",
//           as: "itemDetails.creatorDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$itemDetails.creatorDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $project: {
//           ...buildProjectStage("stream", userId),
//           type: { $literal: "stream" },
//         },
//       },
//     ];

//     // SHORTS aggregation
//     const shortPipeline: any[] = [
//       {
//         $match: {
//           userId: userObjectId,
//           itemType: "short",
//         },
//       },
//       {
//         $lookup: {
//           from: "shorts",
//           localField: "itemId",
//           foreignField: "_id",
//           as: "itemDetails",
//         },
//       },
//       { $unwind: "$itemDetails" },
//       {
//         $project: {
//           ...buildProjectStage("short", userId),
//           type: { $literal: "short" },
//         },
//       },
//     ];

//     // Execute both
//     const [streamResults, shortResults] = await Promise.all([
//       SavedItem.aggregate(streamPipeline),
//       SavedItem.aggregate(shortPipeline),
//     ]);

//     // Combine + sort by savedAt
//     const allItems = [...streamResults, ...shortResults].sort(
//       (a, b) => new Date(b.savedAt).getTime() - new Date(a.savedAt).getTime()
//     );

//     // Paginate in-memory
//     const total = allItems.length;
//     const paginatedItems = allItems.slice(skip, skip + limit);

//     return ResultDB(STATUS_CODES.OK, true, SAVE_ITEMS.FETCHED_SUCCESSFULLY, {
//       total,
//       page,
//       limit,
//       items: paginatedItems,
//     });
//   } catch (error) {
//     console.error("Error in getAllSavedItemsService:", error);
//     return ResultDB(
//       STATUS_CODES.INTERNAL_SERVER_ERROR,
//       false,
//       SAVE_ITEMS.ERROR_FETCHING,
//       null
//     );
//   }
// };

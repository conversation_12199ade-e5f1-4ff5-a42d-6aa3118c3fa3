import { STATUS_CODES } from "../../constants/statusCodes";
import User from "../../models/user/user.schema";
import { IUser, IUserWithStreams } from "../../types/schema";
import mongoose, { PipelineStage } from "mongoose";
import { ApiResponse, ResultDB } from "../../utils/helper";
import { USER_MESSAGES } from "../../constants/responseMessage";
import { ProfileView } from "../../models/profileViews/profileView.schema";
import { ENotificationType } from "../../types/enum";

export const createUser = async (
  user: Partial<IUser>
): Promise<IUser | null> => {
  return await User.create(user);
};

export const getUser = async (id: string): Promise<IUser | null> => {
  return await User.findById(id, { password: 0, __v: 0 });
};

/**
 * following function aggregates creators details
 * @param userId
 * @returns user data
 */
export const getCreatorProfile = async (
  userId: string,
  creatorId: string
): Promise<IUserWithStreams | []> => {
  try {
    const result = await User.aggregate([
      {
        $match: { _id: new mongoose.Types.ObjectId(creatorId) },
      },

      {
        $lookup: {
          from: "streams",
          localField: "_id",
          foreignField: "creator",
          as: "streams",
          pipeline: [
            {
              $project: {
                _id: 1,
                title: 1,
                description: 1,
                transcodedUrl: 1,
                url: 1,
                tags: 1,
                cover: 1,
                videoLength: 1,
                createdAt: 1,
                viewsCount: { $size: "$views" },
                likesCount: { $size: "$likes" },
                commentsCount: { $size: "$comments" },
              },
            },
          ],
        },
      },
      // Add computed fields for followers and following counts
      {
        $addFields: {
          followersCount: { $size: "$followers" },
          followingCount: { $size: "$following" },
          isFollowing: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$followers", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
        },
      },

      {
        $lookup: {
          from: "profileviews",
          let: {
            viewerId: new mongoose.Types.ObjectId(userId),
            targetId: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$viewer", "$$viewerId"] },
                    { $eq: ["$target", "$$targetId"] },
                  ],
                },
              },
            },
            { $limit: 1 },
          ],
          as: "viewedInfo",
        },
      },

      {
        $addFields: {
          isViewed: {
            $cond: {
              if: { $gt: [{ $size: "$viewedInfo" }, 0] },
              then: true,
              else: false,
            },
          },
        },
      },
      {
        $project: {
          password: 0,
          __v: 0,
          followers: 0,
          following: 0,
          provider: 0,
          profileSettings: 0,
          postSettings: 0,
          deletedReason: 0,
          isProfileComplete: 0,
          lastLogin: 0,
          notInterestedIn: 0,
          platformSubscription: 0,
          isDeleted: 0,
          notifications: 0,

          "streams.__v": 0,
        },
      },
      {
        $unset: "viewedInfo",
      },
    ]);

    return result.length > 0 ? result[0] : [];
  } catch (error) {
    console.error("Error fetching user with streams:", error);
    throw new Error("Failed to fetch user with streams.");
  }
};

export const getUserByEmail = async (
  email: string,
  password: boolean = false
): Promise<IUser | null> => {
  if (!password) return await User.findOne({ email, isDeleted: false });
  return await User.findOne({ email, isDeleted: false }, { __v: 0 });
};

export const updateUserProfileCompletion = async (
  userId: string
): Promise<IUser | null> => {
  try {
    // Find the user by ID and update the profile completion status in one operation
    const user = await User.findByIdAndUpdate(
      userId,
      { $set: { isProfileComplete: true } },
      { new: true } // Ensure the updated user document is returned
    );

    // If user doesn't exist, return null
    if (!user) {
      console.error("User not found");
      return null;
    }

    return user;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw new Error("Unable to update user profile completion.");
  }
};

export const getUserByAppProvider = async (
  sub: string,
  provider: string
): Promise<IUser | null> => {
  return await User.findOne({
    "provider.id": sub,
    "provider.provider": provider,
  });
};

export const getUserByUsername = async (
  username: string
): Promise<IUser | null> => {
  return await User.findOne({ username, isDeleted: false });
};

export const checkUsernameExists = async (
  username: string
): Promise<boolean> => {
  const user = await User.findOne({ username });
  return !!user;
};

export const getRecommendedUsersService = async (
  userId: string,
  page = 1,
  limit = 10
) => {
  try {
    const currentUser = await User.findById(userId).select(
      "following notInterestedIn"
    );

    if (!currentUser) {
      return ResultDB(
        STATUS_CODES.NOT_FOUND,
        false,
        USER_MESSAGES.NOT_FOUND,
        null
      );
    }

    const pipeline = buildRecommendationPipeline(currentUser, page, limit);
    const recommendedUsers = await User.aggregate(pipeline);

    return ResultDB(
      STATUS_CODES.OK,
      true,
      USER_MESSAGES.RECOMMENDED_USERS_FETCHED,
      {
        recommendedUsers,
        pagination: {
          currentPage: page,
          limit,
        },
      }
    );
  } catch (error) {
    console.error("Error in getRecommendedUsersService:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const buildRecommendationPipeline = (
  currentUser: IUser,
  page: number,
  limit: number
): PipelineStage[] => {
  const objectId = new mongoose.Types.ObjectId(currentUser._id);

  return [
    { $match: { _id: objectId } },
    {
      $lookup: {
        from: "users",
        localField: "following",
        foreignField: "_id",
        as: "followingUsers",
      },
    },
    {
      $project: {
        followingUsers: "$followingUsers.following",
        notInterestedIn: 1,
        following: 1,
      },
    },
    { $unwind: "$followingUsers" },
    { $unwind: "$followingUsers" },
    { $group: { _id: null, potential: { $addToSet: "$followingUsers" } } },
    {
      $lookup: {
        from: "users",
        localField: "potential",
        foreignField: "_id",
        as: "recommendedUsers",
      },
    },
    { $unwind: "$recommendedUsers" },
    {
      $match: {
        "recommendedUsers._id": {
          $nin: [
            ...currentUser.following,
            currentUser._id,
            ...currentUser.notInterestedIn,
          ],
        },
        "recommendedUsers.isDeleted": false,
      },
    },
    {
      $lookup: {
        from: "userinterests",
        localField: "recommendedUsers._id",
        foreignField: "userId",
        as: "interestData",
      },
    },
    {
      $addFields: {
        score: { $size: "$interestData.interests" },
      },
    },
    { $sort: { score: -1 } },
    { $skip: (page - 1) * limit },
    { $limit: limit },
    {
      $project: {
        _id: "$recommendedUsers._id",
        username: "$recommendedUsers.username",
        displayName: "$recommendedUsers.displayName",
        profilePicture: "$recommendedUsers.profilePicture",
        score: 1,
      },
    },
  ];
};

export const isUserNotificationSettingOn = async (
  userId: string,
  notificationType: ENotificationType
): Promise<boolean> => {
  const user = await User.findById(userId);
  if (!user) {
    return false;
  }
  const hasUserAllowed = user.profileSettings.notifications[notificationType];
  return hasUserAllowed;
};

export const getDeletedAppleUserByEmail = async (
  email: string,
  appleId: string
) => {
  return await User.findOne({
    email,
    isDeleted: true,
    "provider.provider": "apple",
    "provider.id": appleId,
  }).select("-password -__v");
};

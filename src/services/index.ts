import * as featuredStreamService from "./streamService/featuredStreams.service";
import * as StreamService from "./streamService/streams.service";
import * as NotInterestedStream from "./streamService/notInterested.service";
import * as LiveStreamService from "./streamService/liveStream.service";
import { live2Service } from "./streamService/live2.service";

// Imports from User service and exports them as a single object.
import * as UserService from "./userService/user.service";
import * as ProfileService from "./userService/profile.service";
import * as UserActivityService from "./userService/activity.service";
// Story service
import * as StoryService from "./storyService/story.service";

// Shorts service
import * as ShortsService from "./shortsService/shorts.service";

// Playlist service
import * as PlaylistService from "./playlistService/playlist.service";

// Not Interested service
import * as NotInterestedService from "./notInterestedService/notInterested.service"

// reporting service

import * as ReportService from "./reportService/report.service";
export {
  featuredStreamService,
  StreamService,
  LiveStreamService,
  NotInterestedStream,
  live2Service,
  UserService,
  ProfileService,
  UserActivityService,
  StoryService,
  ShortsService,
  PlaylistService,
  NotInterestedService,
  ReportService
};

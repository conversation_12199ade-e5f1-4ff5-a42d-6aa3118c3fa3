import { NOT_INTERESTED_MESSAGES } from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import { NotInterested } from "../../models/notInterestedItems/notInterested.schema";
import Stream from "../../models/stream/stream.schema";
import { INotInterested } from "../../types/schema";
import { ApiResponse, ResultDB } from "../../utils/helper";

/**
 * Following service adds stream to not interested.
 * @param userId
 * @param streamId
 * @returns
 */
export const addNotInterestedStream = async (
  userId: string,
  streamId: string
): Promise<ApiResponse<INotInterested>> => {
  try {
    const stream = await Stream.findOne({ _id: streamId }).lean();
    if (!stream) {
      return ResultDB<INotInterested>(
        STATUS_CODES.NOT_FOUND,
        false,
        NOT_INTERESTED_MESSAGES.NOT_FOUND,
        null
      );
    }
    if (stream.creator?.toString() == userId) {
      return ResultDB<INotInterested>(
        STATUS_CODES.FORBIDDEN,
        false,
        NOT_INTERESTED_MESSAGES.CANNOT_ADD,
        null
      );
    }

    const exists = await NotInterested.findOne({ userId, streamId });
    if (exists) {
      return ResultDB<INotInterested>(
        STATUS_CODES.CONFLICT,
        false,
        NOT_INTERESTED_MESSAGES.ALREADY_EXISTS,
        null
      );
    }

    const notInterested = new NotInterested({ userId, streamId });
    await notInterested.save();

    return ResultDB<INotInterested>(
      STATUS_CODES.CREATED,
      true,
      NOT_INTERESTED_MESSAGES.SUCCESS,
      notInterested
    );
  } catch (error) {
    return ResultDB<INotInterested>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      true,
      NOT_INTERESTED_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Followins service function removes stream from not interested list.
 * currently no need of the following function but can be handy in future.
 * Skipping try-catch as controller function handle the errors.
 * @param userId
 * @param streamId
 * @returns
 */
export const removeNotInterestedStream = async (
  userId: string,
  streamId: string
): Promise<ApiResponse<INotInterested>> => {
  const deleted = await NotInterested.findOneAndDelete({ userId, streamId });
  if (!deleted) {
    return ResultDB<INotInterested>(
      STATUS_CODES.NOT_FOUND,
      false,
      NOT_INTERESTED_MESSAGES.NOT_FOUND,
      null
    );
  }

  return ResultDB<INotInterested>(
    STATUS_CODES.OK,
    true,
    NOT_INTERESTED_MESSAGES.DELETED,
    deleted
  );
};

/**
 * Following service function provides the list of streams which users are not interested in.
 * This can be handy in future.
 * also no need of try-catch as it is handled in controller.
 * @param userId
 * @returns List of streams which users are not interested in
 */
export const fetchNotInterestedStreams = async (
  userId: string
): Promise<ApiResponse<INotInterested[]>> => {
  const streams = await NotInterested.find({ userId })
    .select("streamId createdAt")
    .populate({
      path: "streamId",
      select: "title description cover",
    });
  return ResultDB<INotInterested[]>(
    STATUS_CODES.OK,
    true,
    NOT_INTERESTED_MESSAGES.SUCCESS,
    streams
  );
};

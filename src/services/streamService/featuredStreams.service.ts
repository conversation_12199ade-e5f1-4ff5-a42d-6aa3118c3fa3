import mongoose, { Types } from "mongoose";
import FeaturedStreams from "../../models/featuredStream/featuredStream.schema";
import { ApiResponse, ResultDB } from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  FEATURED_STREAM_MESSAGES,
  FeaturedStream,
} from "../../constants/responseMessage";
import { IFeaturedStream } from "../../types/schema";

/**
 * Service function for adding stream to featured videos.
 * @param userId ID of user
 * @param streamId ID of stream that needs to be added to the featured
 * @returns response
 */
export const addFeaturedStream = async (
  userId: string,
  streamId: string
): Promise<ApiResponse<IFeaturedStream>> => {
  try {
    const UserID = new mongoose.Types.ObjectId(userId);
    const StreamID = new mongoose.Types.ObjectId(streamId);

    const featured = await FeaturedStreams.findOne({ userId: userId });

    if (!featured) {
      const newFeatured = await FeaturedStreams.create({
        userId: userId,
        streams: [streamId],
      });
      return ResultDB<IFeaturedStream>(
        STATUS_CODES.CREATED,
        true,
        FEATURED_STREAM_MESSAGES.CREATED,
        newFeatured
      );
    }

    if (featured.streams.includes(StreamID)) {
      return ResultDB<IFeaturedStream>(
        STATUS_CODES.BAD_REQUEST,
        false,
        FEATURED_STREAM_MESSAGES.INVALID_INPUT,
        null
      );
    }

    featured.streams.push(StreamID);
    if (featured.streams.length > 5) {
      featured.streams.shift();
    }

    await featured.save();
    return ResultDB(
      STATUS_CODES.OK,
      true,
      FEATURED_STREAM_MESSAGES.SUCCESS,
      featured
    );
  } catch (error) {
    console.error("Error adding featured stream:", error);
    return ResultDB<IFeaturedStream>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      FEATURED_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Service to remove stream from featured
 * @param userId
 * @param streamId
 * @returns
 */
export const removeFeaturedStream = async (
  userId: string,
  streamId: string
): Promise<ApiResponse<IFeaturedStream>> => {
  try {
    const featured = await FeaturedStreams.findOne({ userId });

    if (!featured) {
      return ResultDB<IFeaturedStream>(
        STATUS_CODES.NOT_FOUND,
        false,
        FEATURED_STREAM_MESSAGES.NOT_FOUND,
        null
      );
    }

    // Remove the stream
    featured.streams = featured.streams.filter(
      (id) => id.toString() !== streamId
    );
    await featured.save();
    return ResultDB(
      STATUS_CODES.OK,
      true,
      FEATURED_STREAM_MESSAGES.REMOVED,
      featured
    );
  } catch (error) {
    console.log("Error removing featured stream:", error);
    return ResultDB<IFeaturedStream>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      FEATURED_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Service to fetch featured streams.
 * @param userId
 * @returns
 */
export const getFeaturedStreams = async (
  userId: string
): Promise<ApiResponse<IFeaturedStream>> => {
  try {
    const featured = await FeaturedStreams.findOne({ userId })
      .populate(
        "streams",
        "title cover description isLive tags category views createdAt"
      )
      .lean();

    if (!featured) {
      return ResultDB<IFeaturedStream>(
        STATUS_CODES.NOT_FOUND,
        false,
        FEATURED_STREAM_MESSAGES.NOT_FOUND,
        null
      );
    }

    return ResultDB<IFeaturedStream>(
      STATUS_CODES.OK,
      true,
      FEATURED_STREAM_MESSAGES.FETCHED,
      featured
    );
  } catch (error) {
    console.error("Error fetching featured streams:", error);
    return ResultDB<IFeaturedStream>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      FEATURED_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

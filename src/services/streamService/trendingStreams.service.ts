import Stream from "../../models/stream/stream.schema";
import { IStream, ITrendingStream } from "../../types/schema";
import mongoose, { PipelineStage } from "mongoose";
import TrendingScore from "../../models/trendingScore/trendingScore.schema";
import {
  ApiResponse,
  calculateTrendingScore,
  ResultDB,
} from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";
import { STREAM_MESSAGES } from "../../constants/responseMessage";

/**
 * This pipeline is responsible for getting streams based on the trending scores of stream.
 * @param userId function accepts the userId for checking isLiked and isFollowed
 * @returns
 */
export const fetchTrendingStreamsForUser = async (
  userId: string
): Promise<ApiResponse<ITrendingStream[]>> => {
  try {
    const trendingStreamsPipeline: PipelineStage[] = [
      {
        $match: {
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
        },
      },
      {
        $lookup: {
          from: "trendingscores",
          localField: "_id",
          foreignField: "streamId",
          as: "trendingData",
        },
      },
      {
        $unwind: {
          path: "$trendingData",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "notinteresteds",
          let: { streamId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$itemId", "$$streamId"] },
                    { $eq: ["$userId", new mongoose.Types.ObjectId(userId)] },
                    { $eq: ["$contentType", "stream"] },
                  ],
                },
              },
            },
          ],

          as: "notInterestedCheck",
        },
      },
      // Filter out streams that user has marked as not interested
      { $match: { notInterestedCheck: { $eq: [] } } },

      {
        $sort: {
          "trendingData.trendingScore": -1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          description: 1,
          url: 1,
          transcodedUrl: 1,
          category: 1,
          tags: 1,
          cover: 1,
          videoLength: 1,
          type: 1,
          status: 1,
          isLive: 1,
          sharesCount: 1,
          commentCount: { $size: { $ifNull: ["$comments", []] } },
          viewsCount: { $size: { $ifNull: ["$views", []] } },
          likesCount: { $size: { $ifNull: ["$likes", []] } },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id", // Fixed the *id typo
            bio: "$creatorDetails.bio",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            displayName: "$creatorDetails.displayName",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
    ];
    const result = await Stream.aggregate(trendingStreamsPipeline).exec();

    if (!result || result.length === 0) {
      return ResultDB<ITrendingStream[]>(
        STATUS_CODES.NOT_FOUND,
        false,
        STREAM_MESSAGES.NOT_FOUND,
        []
      );
    }

    return ResultDB<ITrendingStream[]>(
      STATUS_CODES.OK,
      true,
      STREAM_MESSAGES.FETCH_STREAMS,
      result
    );
  } catch (error) {
    console.log("Error fetching trending streams:", error);
    return ResultDB<ITrendingStream[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      []
    );
  }
};

export const bulkUpdateTrendingScores = async (updates: any[]) => {
  try {
    const result = await TrendingScore.bulkWrite(updates);
    return result;
  } catch (error) {
    console.error("Error in bulk updating trending scores:", error);
    throw error;
  }
};

/**
 * We update stream's trending scores in batches.
 * We set date for last 7 days
 * We update the likes, views etc in bulk by doing bulk write operation.
 * So, only streams created within the last 7 days are considered for updates.
 * Below function will run periodically within cron job
 */
export const updateTrendingScores = async () => {
  const STREAM_BATCH_LIMIT = 100;
  const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

  let lastId = null;
  let hasMore = true;

  while (hasMore) {
    const streams: IStream[] = await Stream.find(
      lastId
        ? { _id: { $gt: lastId }, createdAt: { $gte: cutoffDate } }
        : { createdAt: { $gte: cutoffDate } }
    )
      .sort({ _id: 1 })
      .limit(STREAM_BATCH_LIMIT);

    if (streams.length === 0) {
      hasMore = false;
      break;
    }

    const updates = streams.map(async (stream: IStream) => {
      try {
        const trendingScore = await calculateTrendingScore(
          stream.views.length,
          stream.likes.length,
          stream.sharesCount,
          stream.createdAt
        );

        return {
          updateOne: {
            filter: { streamId: stream._id },
            update: { streamId: stream._id, trendingScore },
            upsert: true,
          },
        };
      } catch (error) {
        console.error(
          `Error calculating trending score for stream ${stream._id}:`,
          error
        );
        return null;
      }
    });

    const bulkUpdates = await Promise.all(updates);
    await bulkUpdateTrendingScores(bulkUpdates);

    lastId = streams[streams.length - 1]._id;
  }
};

//Following snippet is useful for testing trending score locally
// const mockData = require("../../constants/MOCK_DATA.json");
// interface Stream {
//   id: string;
//   views: number;
//   likes: number;
//   shares: number;
//   createdAt: string; // or Date if you're converting it
// }
// export const calculateTrendingScoresForMockData = async () => {
//   const STREAM_BATCH_LIMIT = 100;
//   const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

//   // Filter mock data by cutoffDate
//   // const filteredStreams = mockData.filter(
//   //   (stream: any) => new Date(stream.createdAt) >= cutoffDate
//   // );
//   // console.log("===================");
//   // console.log("===================");
//   // console.log(filteredStreams);
//   // console.log("===================");
//   // console.log("===================");
//   let lastIndex = 0;

//   while (lastIndex < mockData.length) {
//     const batch = mockData.slice(lastIndex, lastIndex + STREAM_BATCH_LIMIT);

//     const scores = await Promise.all(
//       batch.map(async (stream: Stream) => {
//         try {
//           const trendingScore = await calculateTrendingScore(
//             stream.views,
//             stream.likes,
//             stream.shares,
//             new Date(stream.createdAt)
//           );

//           return {
//             streamId: stream.id,
//             trendingScore,
//           };
//         } catch (error) {
//           console.error(
//             `Error calculating trending score for stream ${stream.id}:`,
//             error
//           );
//           return null;
//         }
//       })
//     );

//     // Log or process the scores
//     scores.forEach((score) => {
//       if (score) {
//         console.log(
//           `Stream ID: ${score.streamId}, Trending Score: ${score.trendingScore}`
//         );
//       }
//     });

//     lastIndex += STREAM_BATCH_LIMIT;
//   }
// };

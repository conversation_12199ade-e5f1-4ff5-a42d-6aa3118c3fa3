import { PipelineStage } from "mongoose";
import Stream from "../../models/stream/stream.schema";
import { ResultDB } from "../../utils/helper";
import LiveChat from "../../models/liveChatSchema/liveChatSchema";
import Comment from "../../models/comment/comment.schema";

export const getCurrentLiveStreams = async (
  page: number = 1,
  limit: number = 10
) => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: PipelineStage[] = [
      {
        $match: { isLive: true }, // Fetch only live streams
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: "$creatorDetails",
      },
      {
        $project: {
          _id: 1,
          title: 1,
          description: 1,
          url: 1,
          liveUrl: 1,
          isLive: 1,
          cover: 1,
          viewsCount: {
            $size: {
              $cond: {
                if: { $isArray: "$views" },
                then: "$views",
                else: []
              }
            }
          },
          likes: {
            $size: {
              $cond: {
                if: { $isArray: "$likes" },
                then: "$likes",
                else: []
              }
            }
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
          },
          tags: 1,
          createdAt: 1,
        },
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ];

    const streams = await Stream.aggregate(pipeline);
    const total = await Stream.countDocuments({ isLive: true });
    const pagination = {
      total: total,
      page: page,
      limit: limit,
      totalPages: Math.ceil(total / limit),
    };

    return ResultDB(200, true, "Live streams fetched successfully", {
      streams,
      pagination,
    });
  } catch (error) {
    console.error("Error fetching live streams:", error);
    return ResultDB(500, false, "Failed to fetch live streams", null);
  }
};

/**
 * Function to dump all the live chats in comments after live stream ends.
 * @param streamId
 */
export const convertLiveChatToComments = async (streamId: string) => {
  const liveMessages = await LiveChat.find({ streamId: streamId });

  const comments = [];
  for (const msg of liveMessages) {
    comments.push({
      user: msg.user,
      stream: msg.streamId,
      content: msg.message,
      createdAt: msg.timestamp,
    });
  }

  await Comment.insertMany(comments);
  await LiveChat.deleteMany({ stream: streamId });
};

export const checkStreamIsLive = async (streamId: string) => {
  try {
    const stream = await Stream.findById(streamId).select("isLive").lean();
    if (!stream) {
      return ResultDB(404, false, "Stream not found");
    }
    return ResultDB(200, true, "Live-stream found", stream);
  } catch (error) {
    console.log(error);
    return ResultDB(500, false, "Internal server error");
  }
};

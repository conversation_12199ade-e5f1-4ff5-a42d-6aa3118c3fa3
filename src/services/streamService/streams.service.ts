import mongoose, { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>hema, Types } from "mongoose";
import {
  REPORT_STREAM_MESSAGES,
  STREAM_MESSAGES,
} from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import FeaturedStreams from "../../models/featuredStream/featuredStream.schema";
import Stream from "../../models/stream/stream.schema";
import { INotInterested, IReport, IStream } from "../../types/schema";
import { ApiResponse, ResultDB } from "../../utils/helper";
import { NotInterested } from "../../models/notInterestedItems/notInterested.schema";
import ReportStream from "../../models/ReportStream/reportStream.schema";
import { streamIO } from "../../sockets/stream";
import { getNotInterestedFilterStages } from "../../utils/pipelineStages";

/**
 * Following service function soft deletes the stream.
 * we make sure that stream gets deleted from featured list if exists by,
 * starting a transaction to ensure both operations succeed or fail together.
 * Inside we Check if user is the creator and Soft delete the stream by changing stream isDeleted from false to true.
 * @param streamId
 * @param userId
 * @returns
 */
export const deleteStreamService = async (
  streamId: string,
  userId: string
): Promise<ApiResponse<IStream>> => {
  try {
    const stream = await Stream.findOne({
      _id: streamId,
      isDeleted: false,
    });

    if (!stream) {
      return ResultDB<IStream>(
        STATUS_CODES.NOT_FOUND,
        false,
        STREAM_MESSAGES.NOT_FOUND,
        null
      );
    }

    if (stream.creator.toString() != userId) {
      return ResultDB<IStream>(
        STATUS_CODES.UNAUTHORIZED,
        false,
        STREAM_MESSAGES.UNAUTHORIZED,
        null
      );
    }

    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      stream.isDeleted = true;
      await stream.save({ session });

      await FeaturedStreams.updateMany(
        { streams: streamId },
        { $pull: { streams: streamId } },
        { session }
      );

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

    return ResultDB<IStream>(
      STATUS_CODES.OK,
      true,
      STREAM_MESSAGES.DELETED,
      stream
    );
  } catch (error) {
    console.error("Error deleting stream:", error);
    return ResultDB<IStream>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Service function to report stream
 * @param streamId
 * @param reporterId
 * @param reason
 * @returns
 */
export const createStreamReport = async (
  streamId: string,
  reporterId: string,
  reason: string
): Promise<ApiResponse<IReport>> => {
  try {
    const stream = await Stream.findById(streamId);
    if (!stream) {
      return ResultDB<IReport>(
        STATUS_CODES.NOT_FOUND,
        false,
        REPORT_STREAM_MESSAGES.NOT_FOUND,
        null
      );
    }

    if (stream.creator.toString() === reporterId) {
      return ResultDB<IReport>(
        STATUS_CODES.BAD_REQUEST,
        false,
        REPORT_STREAM_MESSAGES.CANNOT_REPORT,
        null
      );
    }

    const existingReport = await ReportStream.findOne({
      streamId: streamId,
      reporter: reporterId,
      status: { $in: ["pending", "reviewed"] },
    });

    if (existingReport) {
      return ResultDB<IReport>(
        STATUS_CODES.BAD_REQUEST,
        false,
        REPORT_STREAM_MESSAGES.ALREADY_REPORTED,
        null
      );
    }

    const report = await ReportStream.create({
      streamId: streamId,
      reporter: reporterId,
      reason,
    });

    return ResultDB<IReport>(
      STATUS_CODES.CREATED,
      true,
      REPORT_STREAM_MESSAGES.SUCCESS,
      report
    );
  } catch (error) {
    console.error("Error creating stream report:", error);
    return ResultDB<IReport>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      REPORT_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Service function to update report status
 * @param reportId
 * @param adminId
 * @param status
 * @param adminFeedback
 * @returns
 */
export const updateReportStatus = async (
  reportId: string,
  adminId: string,
  status: "reviewed" | "resolved" | "rejected",
  adminFeedback?: string
): Promise<ApiResponse<IReport>> => {
  try {
    const report = await ReportStream.findById(reportId);
    if (!report) {
      return ResultDB<IReport>(
        STATUS_CODES.NOT_FOUND,
        false,
        REPORT_STREAM_MESSAGES.NOT_FOUND,
        null
      );
    }

    report.status = status;
    report.reviewedBy = new Schema.Types.ObjectId(adminId);
    report.reviewedAt = new Date();
    if (adminFeedback) {
      report.adminFeedback = adminFeedback;
    }

    await report.save();

    return ResultDB<IReport>(
      STATUS_CODES.OK,
      true,
      REPORT_STREAM_MESSAGES.UPDATED,
      report
    );
  } catch (error) {
    console.error("Error updating report status:", error);
    return ResultDB<IReport>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      REPORT_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

interface Pagination {
  page?: string;
  limit?: string;
}
/**
 * Get popular streams (videos) for a user. "Popular" is defined by the number of views.
 * Excludes streams that the user marked as "not interested".
 *
 * @param userId The ID of the current user
 * @param pagination {page, limit} from query
 * @returns ApiResponse<object> containing the data and pagination info
 */
export const getPopularStreamsService = async (
  userId: string,
  { page, limit }: Pagination
): Promise<ApiResponse<any>> => {
  try {
    // parse pagination values
    const currentPage = parseInt(page || "1", 10) || 1;
    const pageLimit = parseInt(limit || "10", 10) || 10;

    if (currentPage < 1 || pageLimit < 1) {
      return ResultDB(
        400,
        false,
        "Pagination parameters must be positive integers.",
        null
      );
    }

    const pipeline: any[] = [
      {
        $match: {
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
          // If you only want "video" type, uncomment or adjust accordingly
          // type: "video",
        },
      },
      // Exclude streams that the user marked as "not interested"
      ...getNotInterestedFilterStages(userId, "stream"),

      // Calculate viewsCount to sort by
      {
        $addFields: {
          viewsCount: {
            $size: {
              $cond: {
                if: { $isArray: "$views" },
                then: "$views",
                else: []
              }
            }
          },
        },
      },
      // Sort by descending viewsCount (most viewed first)
      {
        $sort: { viewsCount: -1 },
      },
      // Optionally, you might further sort by createdAt if there's a tie in viewsCount:
      // { $sort: { viewsCount: -1, createdAt: -1 } },
      {
        $lookup: {
          from: "users",
          let: { creatorId: "$creator" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$_id", "$$creatorId"] },
                    { $ne: ["$isDeleted", true] }, // Ensure user is not deleted
                  ],
                },
              },
            },
          ],
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: 1,
          transcodedUrl: 1,
          url: 1,
          type: 1,
          status: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          cover: 1,
          createdAt: 1,
          videoLength: 1,
          isLive: 1,
          // If you need isLive or other fields, keep them
          commentCount: { $size: { $ifNull: ["$comments", []] } },
          viewsCount: 1, // newly added field
          likesCount: { $size: { $ifNull: ["$likes", []] } },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            displayName: "$creatorDetails.displayName",
            // If you track followers on "creatorDetails.followers", isFollowing:
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
      {
        $skip: (currentPage - 1) * pageLimit,
      },
      {
        $limit: pageLimit,
      },
    ];

    const totalPipeline: any[] = [
      {
        $match: {
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
          // type: "video",
        },
      },
      {
        $lookup: {
          from: "notinteresteds",
          let: { streamId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$streamId", "$$streamId"] },
                    { $eq: ["$userId", new mongoose.Types.ObjectId(userId)] },
                  ],
                },
              },
            },
          ],
          as: "notInterested",
        },
      },
      {
        $match: {
          notInterested: { $size: 0 },
        },
      },
      { $count: "total" },
    ];

    const [streamData, totalCount] = await Promise.all([
      Stream.aggregate(pipeline).exec(),
      Stream.aggregate(totalPipeline).exec(),
    ]);

    const totalRecords = totalCount?.[0]?.total || 0;
    const totalPages = Math.ceil(totalRecords / pageLimit);

    const responseData = {
      data: streamData,
      pagination: {
        totalRecords,
        totalPages,
        currentPage,
        limit: pageLimit,
      },
    };

    return ResultDB(
      200,
      true,
      "Popular streams fetched successfully",
      responseData
    );
  } catch (error: any) {
    console.error("Error in getPopularStreamsService:", error);

    return ResultDB(
      500,
      false,
      "An error occurred while fetching popular streams",
      null
    );
  }
};

export const streamsByCategory = async (
  category: string,
  page: number = 1,
  limit: number = 10
) => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          isDeleted: false,
          category: category,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: "$creatorDetails",
      },
      {
        $project: {
          _id: 1,
          title: 1,
          description: 1,
          url: 1,
          transcodedUrl: 1,
          liveUrl: 1,
          vodUrls: 1,
          viewsCount: {
            $size: {
              $cond: {
                if: { $isArray: "$views" },
                then: "$views",
                else: []
              }
            }
          }, // Count views
          likes: {
            $size: { $ifNull: ["$likes", []] }
          }, // Count likes
          category: 1,
          tags: 1,
          isLive: 1,
          cover: 1,
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
          },
          createdAt: 1,
        },
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ];

    const streams = await Stream.aggregate(pipeline);
    const total = await Stream.countDocuments({
      isDeleted: false,
      category,
      $or: [{ isLive: true }, { vodStatus: "ready" }],
    });
    const pagination = {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    return ResultDB(200, true, "Streams fetched successfully by category", {
      streams,
      pagination,
    });
  } catch (error) {
    console.error("Error fetching streams by category:", error);
    return ResultDB(500, false, "Failed to fetch streams by category", null);
  }
};

export const updateVodStatus = async (uniquekey: string) => {
  try {
    const updatedStream = await Stream.findOneAndUpdate(
      { liveStreamId: uniquekey },
      { $set: { vodStatus: "ready" } },
      { new: true }
    );
    if (!updatedStream) {
      return ResultDB(400, false, "Stream not found");
    }
    streamIO.emit("streamTranscoded", {
      message: "Video on demand ready",
      updatedStream,
    });

    return ResultDB(200, true, "Video on demand ready", updatedStream);
  } catch (error) {
    return ResultDB(500, false, "Internal server error");
  }
};

export const getStreamsByIds = async (userId: string, streamIds: string[]) => {
  try {
    const objectIds = streamIds.map((id) => new mongoose.Types.ObjectId(id));

    const pipeline: any[] = [
      {
        $match: {
          _id: { $in: objectIds },
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          videoLength: 1,
          transcodedUrl: 1,
          liveUrls: 1,
          vodUrls: 1,
          liveUrl: 1,
          url: 1,
          type: 1,
          status: 1,
          isLive: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          cover: 1,
          createdAt: 1,
          viewsCount: {
            $size: {
              $cond: {
                if: { $isArray: "$views" },
                then: "$views",
                else: []
              }
            }
          },
          likesCount: {
            $size: {
              $cond: {
                if: { $isArray: "$likes" },
                then: "$likes",
                else: []
              }
            }
          },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
          comments: {
            $size: { $ifNull: ["$comments", []] }
          },
        },
      },
    ];

    return await Stream.aggregate(pipeline).exec();
  } catch (error) {
    console.error("Error in getStreamsByIds:", error);
    return [];
  }
};

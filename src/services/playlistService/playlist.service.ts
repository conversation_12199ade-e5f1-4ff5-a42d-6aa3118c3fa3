// services/playlistService.ts
import mongoose, { Types } from "mongoose";
import { Playlist, IPlaylist } from "../../models/playlist/playlist.schema";

import { STATUS_CODES } from "../../constants/statusCodes";
import { PLAYLIST_MESSAGES } from "../../constants/responseMessage";
import { ApiResponse, ResultDB } from "../../utils/helper";
import { getStreamDetailsForUser } from "../../models/stream/stream.model";
import ShortsModel from "../../models/shorts/shorts.schema";
import { getStreamsByIds } from "../streamService/streams.service";

export const createPlaylist = async ({
  title,
  description,
  createdBy,
}: {
  title: string;
  description?: string;
  createdBy: Types.ObjectId;
}): Promise<ApiResponse<IPlaylist>> => {
  const playlist = await Playlist.create({ title, description, createdBy });
  return ResultDB(
    STATUS_CODES.CREATED,
    true,
    PLAYLIST_MESSAGES.CREATED,
    playlist
  );
};

export const deletePlaylist = async (
  playlistId: string,
  userId: string
): Promise<ApiResponse<null>> => {
  try {
    const playlist = await Playlist.findById(playlistId).select("createdBy");

    if (!playlist) {
      return ResultDB(
        STATUS_CODES.NOT_FOUND,
        false,
        PLAYLIST_MESSAGES.NOT_FOUND,
        null
      );
    }

    if (playlist.createdBy.toString() !== userId.toString()) {
      return ResultDB(
        STATUS_CODES.FORBIDDEN,
        false,
        PLAYLIST_MESSAGES.UNAUTHORIZED,
        null
      );
    }

    await Playlist.findByIdAndDelete(playlistId);

    return ResultDB(STATUS_CODES.OK, true, PLAYLIST_MESSAGES.DELETED, null);
  } catch (error) {
    console.error("Error in deletePlaylist:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.ERROR_FETCHING,
      null
    );
  }
};

export const addItemsToPlaylist = async ({
  playlistId,
  items,
}: {
  playlistId: string;
  items: { itemId: string; itemType: "stream" | "short" }[];
}): Promise<ApiResponse<IPlaylist>> => {
  const itemstoInsert = items.map((item) => ({
    itemId: new mongoose.Types.ObjectId(item.itemId),
    itemType: item.itemType,
  }));

  const playlist = await Playlist.findByIdAndUpdate(
    playlistId,
    {
      $addToSet: {
        items: { $each: itemstoInsert },
      },
    },
    { new: true }
  );

  if (!playlist) {
    return ResultDB(STATUS_CODES.NOT_FOUND, false, PLAYLIST_MESSAGES.NOT_FOUND);
  }

  return ResultDB(
    STATUS_CODES.OK,
    true,
    PLAYLIST_MESSAGES.ITEM_ADDED,
    playlist
  );
};

// export const addItemToPlaylist = async ({
//   playlistId,
//   itemId,
//   itemType,
// }: {
//   playlistId: string;
//   itemId: string;
//   itemType: "stream" | "short";
// }): Promise<ApiResponse<IPlaylist>> => {
//   const objectItemId = new mongoose.Types.ObjectId(itemId);
//   const playlist = await Playlist.findById(playlistId);

//   if (!playlist) {
//     return ResultDB(STATUS_CODES.NOT_FOUND, false, PLAYLIST_MESSAGES.NOT_FOUND);
//   }

//   const alreadyExists = playlist.items.some(
//     (item) => item.itemId.toString() === itemId && item.itemType === itemType
//   );
//   if (alreadyExists) {
//     return ResultDB(
//       STATUS_CODES.CONFLICT,
//       false,
//       PLAYLIST_MESSAGES.ITEM_EXISTS
//     );
//   }

//   playlist.items.push({ itemId: objectItemId, itemType });
//   await playlist.save();

//   return ResultDB(
//     STATUS_CODES.OK,
//     true,
//     PLAYLIST_MESSAGES.ITEM_ADDED,
//     playlist
//   );
// };

export const removeItemFromPlaylist = async ({
  playlistId,
  itemId,
  itemType,
}: {
  playlistId: string;
  itemId: string;
  itemType: "stream" | "short";
}): Promise<ApiResponse<IPlaylist>> => {
  const playlist = await Playlist.findById(playlistId);

  if (!playlist) {
    return ResultDB(STATUS_CODES.NOT_FOUND, false, PLAYLIST_MESSAGES.NOT_FOUND);
  }

  const index = playlist.items.findIndex(
    (item) => item.itemId.toString() === itemId && item.itemType === itemType
  );

  if (index === -1) {
    return ResultDB(
      STATUS_CODES.NOT_FOUND,
      false,
      PLAYLIST_MESSAGES.ITEM_NOT_FOUND
    );
  }

  playlist.items.splice(index, 1);
  await playlist.save();

  return ResultDB(
    STATUS_CODES.OK,
    true,
    PLAYLIST_MESSAGES.ITEM_REMOVED,
    playlist
  );
};

export const updatePlaylist = async ({
  playlistId,
  title,
  description,
}: {
  playlistId: string;
  title?: string;
  description?: string;
}): Promise<ApiResponse<IPlaylist>> => {
  const playlist = await Playlist.findById(playlistId);

  if (!playlist) {
    return ResultDB(STATUS_CODES.NOT_FOUND, false, PLAYLIST_MESSAGES.NOT_FOUND);
  }

  if (title) playlist.title = title;
  if (description) playlist.description = description;

  await playlist.save();

  return ResultDB(STATUS_CODES.OK, true, PLAYLIST_MESSAGES.UPDATED, playlist);
};

export const getPlaylistById = async ({
  playlistId,
  userId,
}: {
  playlistId: string;
  userId: string;
}): Promise<ApiResponse<any>> => {
  const playlist = await Playlist.findById(playlistId)
    .select("title description items createdBy createdAt")
    .lean();

  if (!playlist) {
    return ResultDB(STATUS_CODES.NOT_FOUND, false, PLAYLIST_MESSAGES.NOT_FOUND);
  }

  const streamIds = playlist.items
    .filter((item) => item.itemType === "stream")
    .map((item) => item.itemId.toString());

  const shortIds = playlist.items
    .filter((item) => item.itemType === "short")
    .map((item) => item.itemId);

  const [streamsResult, shorts] = await Promise.all([
    getStreamsByIds(userId, streamIds),
    ShortsModel.find({ _id: { $in: shortIds } })
      .populate("author", "username dp firstName lastName")
      .lean(),
  ]);

  const streamMap = new Map(
    streamsResult?.map((s: any) => [s._id.toString(), s])
  );
  const shortMap = new Map(shorts.map((s: any) => [s._id.toString(), s]));

  const enrichedItems = playlist.items.map((item) => {
    const itemIdStr = item.itemId.toString();
    if (item.itemType === "stream") {
      return {
        type: "stream",
        data: streamMap.get(itemIdStr) || null,
      };
    } else {
      return {
        type: "short",
        data: shortMap.get(itemIdStr) || null,
      };
    }
  });

  return ResultDB(STATUS_CODES.OK, true, PLAYLIST_MESSAGES.FETCHED, {
    _id: playlist._id,
    title: playlist.title,
    description: playlist.description,
    createdAt: playlist.createdAt,
    createdBy: playlist.createdBy,
    items: enrichedItems.filter((item) => item.data !== null),
  });
};

// export const getAllPlaylistsForUser = async ({
//   userId,
//   page = 1,
//   limit = 10,
// }: {
//   userId: string;
//   page?: number;
//   limit?: number;
// }) => {
//   try {
//     const skip = (page - 1) * limit;

//     const [playlists, totalCountArr] = await Promise.all([
//       Playlist.aggregate([
//         { $match: { createdBy: new Types.ObjectId(userId) } },
//         {
//           $project: {
//             _id: 1,
//             title: 1,
//             description: 1,
//             createdAt: 1,
//             updatedAt: 1,
//             itemsCount: { $size: "$items" },
//           },
//         },
//         { $sort: { createdAt: -1 } },
//         { $skip: skip },
//         { $limit: limit },
//       ]),
//       Playlist.aggregate([
//         { $match: { createdBy: new Types.ObjectId(userId) } },
//         { $count: "total" },
//       ]),
//     ]);

//     const totalRecords = totalCountArr[0]?.total || 0;
//     const totalPages = Math.ceil(totalRecords / limit);

//     return ResultDB(STATUS_CODES.OK, true, PLAYLIST_MESSAGES.FETCHED, {
//       playlists,
//       pagination: {
//         totalRecords,
//         totalPages,
//         currentPage: page,
//         limit,
//       },
//     });
//   } catch (error) {
//     console.error("Error in getAllPlaylistsForUser:", error);
//     return ResultDB(
//       STATUS_CODES.INTERNAL_SERVER_ERROR,
//       false,
//       PLAYLIST_MESSAGES.INTERNAL_ERROR,
//       null
//     );
//   }
// };

export const getAllPlaylistsForUser = async ({
  userId,
  page = 1,
  limit = 10,
  search = "",
}: {
  userId: string;
  page?: number;
  limit?: number;
  search?: string;
}) => {
  try {
    const skip = (page - 1) * limit;

    const matchCondition: any = {
      createdBy: new Types.ObjectId(userId),
    };

    if (search.trim()) {
      const regex = new RegExp(search, "i");
      matchCondition.$or = [{ title: regex }, { description: regex }];
    }

    const [playlists, totalCountArr] = await Promise.all([
      Playlist.aggregate([
        { $match: matchCondition },
        {
          $project: {
            _id: 1,
            title: 1,
            description: 1,
            createdAt: 1,
            updatedAt: 1,
            itemsCount: { $size: "$items" },
          },
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
      ]),
      Playlist.aggregate([{ $match: matchCondition }, { $count: "total" }]),
    ]);

    const totalRecords = totalCountArr[0]?.total || 0;
    const totalPages = Math.ceil(totalRecords / limit);

    return ResultDB(STATUS_CODES.OK, true, PLAYLIST_MESSAGES.FETCHED, {
      playlists,
      pagination: {
        totalRecords,
        totalPages,
        currentPage: page,
        limit,
      },
    });
  } catch (error) {
    console.error("Error in getAllPlaylistsForUser:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR,
      null
    );
  }
};

export const searchPlaylists = async ({
  userId,
  search,
  page = 1,
  limit = 10,
}: {
  userId: string;
  search: string;
  page?: number;
  limit?: number;
}) => {
  try {
    const skip = (page - 1) * limit;

    const regex = new RegExp(search, "i");

    const matchCondition = {
      createdBy: new Types.ObjectId(userId),
      $or: [{ title: regex }, { description: regex }],
    };

    const [playlists, totalArr] = await Promise.all([
      Playlist.aggregate([
        { $match: matchCondition },
        {
          $project: {
            _id: 1,
            title: 1,
            description: 1,
            createdAt: 1,
            updatedAt: 1,
            itemsCount: { $size: "$items" },
          },
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
      ]),
      Playlist.aggregate([{ $match: matchCondition }, { $count: "total" }]),
    ]);

    const totalRecords = totalArr[0]?.total || 0;
    const totalPages = Math.ceil(totalRecords / limit);

    return ResultDB(STATUS_CODES.OK, true, PLAYLIST_MESSAGES.FETCHED, {
      playlists,
      pagination: {
        totalRecords,
        totalPages,
        currentPage: page,
        limit,
      },
    });
  } catch (error) {
    console.error("Error in searchPlaylists:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR,
      null
    );
  }
};

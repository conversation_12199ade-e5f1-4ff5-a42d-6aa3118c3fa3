import mongoose, { PipelineStage, Types } from "mongoose";
import {
  SHORT_COMMENT_MESSAGES,
  SHORT_LIKES_MESSAGES,
  SHORTS_MESSAGES,
} from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import ShortsModel from "../../models/shorts/shorts.schema";
import { IShorts } from "../../types/schema";
import {
  ApiResponse,
  calculateTrendingScore,
  ResultDB,
} from "../../utils/helper";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { AWS_S3_TEMP_BUCKET_NAME } from "../../config/environment";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import s3Client from "../../config/s3";
import ShortComment from "../../models/shorts/shortComment.schema";
import { sendNotification } from "../../models/notification/notification.model";
import ShortLike from "../../models/shorts/shortLikes.schema";
import ShortView from "../../models/shorts/shortViews.schema";
import TrendingShortScore from "../../models/shortsTrendingScore/shortsTrendingScore.schema";
import { getNotInterestedFilterStages } from "../../utils/pipelineStages";
import { ENotificationContentModel, ENotificationType } from "../../types/enum";
/**
 * Service function for creating shorts
 */
export const createShorts = async (
  data: Partial<IShorts>
): Promise<ApiResponse<IShorts>> => {
  try {
    const newShort = await ShortsModel.create(data);

    return ResultDB<IShorts>(
      STATUS_CODES.CREATED,
      true,
      SHORTS_MESSAGES.CREATED,
      newShort
    );
  } catch (error) {
    console.error("Error creating short:", error);
    return ResultDB<IShorts>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_CREATING,
      null
    );
  }
};

export const getShortsById = async (
  shortId: string,
  userId: string
): Promise<ApiResponse<IShorts | null>> => {
  try {
    const shortObjId = new mongoose.Types.ObjectId(shortId);
    const userObjId = new mongoose.Types.ObjectId(userId);

    const pipeline = [
      // Match short by _id
      {
        $match: { _id: shortObjId },
      },

      // Join author details
      {
        $lookup: {
          from: "users",
          localField: "author",
          foreignField: "_id",
          as: "authorDetails",
        },
      },
      {
        $unwind: {
          path: "$authorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

      // Check if saved by current user
      {
        $lookup: {
          from: "saveditems",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$itemId", "$$shortId"] },
                    { $eq: ["$userId", userObjId] },
                    { $eq: ["$itemType", "short"] },
                  ],
                },
              },
            },
          ],
          as: "savedData",
        },
      },

      // Get all likes for this short
      {
        $lookup: {
          from: "shortlikes",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$shortId", "$$shortId"] },
              },
            },
          ],
          as: "likesData",
        },
      },

      // Get all views for this short
      {
        $lookup: {
          from: "shortviews",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$shortId", "$$shortId"] },
              },
            },
          ],
          as: "viewsData",
        },
      },

      // Add flags & counts
      {
        $addFields: {
          isLiked: {
            $in: [userObjId, "$likesData.userId"],
          },
          isViewed: {
            $in: [userObjId, "$viewsData.userId"],
          },
          viewsCount: {
            $size: "$viewsData",
          },
          isSaved: {
            $gt: [{ $size: "$savedData" }, 0],
          },

          isFollowing: {
            $in: [userObjId, { $ifNull: ["$authorDetails.followers", []] }],
          },
        },
      },

      // Final projection
      {
        $project: {
          description: 1,
          videoUrl: 1,
          thumbnailUrl: 1,
          audioDetails: 1,
          shortDuration: 1,
          tags: 1,
          location: 1,
          shares: 1,
          interactionScore: 1,
          createdAt: 1,
          updatedAt: 1,
          isLiked: 1,
          likesCount: 1,
          isViewed: 1,
          viewsCount: 1,
          isSaved: 1,
          commentCount: 1,
          isFollowing: 1,

          "authorDetails._id": 1,
          "authorDetails.username": 1,
          "authorDetails.displayname": 1,
          "authorDetails.profilePicture": 1,
        },
      },
    ];

    const result = await ShortsModel.aggregate(pipeline).exec();

    if (!result || result.length === 0) {
      return ResultDB(
        STATUS_CODES.NOT_FOUND,
        false,
        SHORTS_MESSAGES.NOT_FOUND,
        null
      );
    }

    return ResultDB(
      STATUS_CODES.OK,
      true,
      SHORTS_MESSAGES.FETCHED_BY_ID,
      result[0]
    );
  } catch (error) {
    console.error("Error fetching short by ID:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING_BY_ID,
      null
    );
  }
};

export const updateShorts = async (
  id: string,
  updateData: Partial<IShorts>
): Promise<ApiResponse<IShorts | null>> => {
  try {
    const updatedShort = await ShortsModel.findByIdAndUpdate(id, updateData, {
      new: true,
    });

    if (!updatedShort) {
      return ResultDB(
        STATUS_CODES.NOT_FOUND,
        false,
        SHORTS_MESSAGES.NOT_FOUND,
        null
      );
    }

    return ResultDB(
      STATUS_CODES.OK,
      true,
      SHORTS_MESSAGES.UPDATED,
      updatedShort
    );
  } catch (error) {
    console.error("Error updating short:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_UPDATING,
      null
    );
  }
};

/**
 * service function for deleting shorts.
 * @param id
 * @param userId
 * @returns
 */
export const deleteShorts = async (
  id: string,
  userId: string
): Promise<ApiResponse<IShorts | null>> => {
  try {
    const short = await ShortsModel.findById(id);
    if (!short) {
      return ResultDB(
        STATUS_CODES.NOT_FOUND,
        false,
        SHORTS_MESSAGES.NOT_FOUND,
        null
      );
    }
    if (short.author.toString() !== userId.toString()) {
      return ResultDB(
        STATUS_CODES.UNAUTHORIZED,
        false,
        SHORTS_MESSAGES.UNAUTHORIZED_DELETE,
        null
      );
    }

    const deleted = await ShortsModel.findByIdAndDelete(id);

    return ResultDB(STATUS_CODES.OK, true, SHORTS_MESSAGES.DELETED, deleted);
  } catch (error) {
    console.error("Error deleting short:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_DELETING,
      null
    );
  }
};

export const getShortsByAuthor = async (
  authorId: string,
  userId: string,
  skip: number,
  pageSize: number
): Promise<ApiResponse<IShorts[]>> => {
  try {
    const authorObjId = new mongoose.Types.ObjectId(authorId);
    const userObjId = new mongoose.Types.ObjectId(userId);

    const pipeline = [
      // Match shorts by author
      {
        $match: { author: authorObjId },
      },

      // Author details
      {
        $lookup: {
          from: "users",
          localField: "author",
          foreignField: "_id",
          as: "authorDetails",
        },
      },
      {
        $unwind: {
          path: "$authorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

      // Saved items lookup
      {
        $lookup: {
          from: "saveditems",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$itemId", "$$shortId"] },
                    { $eq: ["$userId", userObjId] },
                    { $eq: ["$itemType", "short"] },
                  ],
                },
              },
            },
          ],
          as: "savedData",
        },
      },

      // Likes lookup
      {
        $lookup: {
          from: "shortlikes",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$shortId", "$$shortId"] },
              },
            },
          ],
          as: "likesData",
        },
      },

      // Views lookup
      {
        $lookup: {
          from: "shortviews",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$shortId", "$$shortId"] },
              },
            },
          ],
          as: "viewsData",
        },
      },

      // Add fields: computed flags + counts
      {
        $addFields: {
          isLiked: {
            $in: [userObjId, "$likesData.userId"],
          },
          likesCount: {
            $size: "$likesData",
          },
          isViewed: {
            $in: [userObjId, "$viewsData.userId"],
          },
          viewsCount: {
            $size: "$viewsData",
          },
          isSaved: {
            $gt: [{ $size: "$savedData" }, 0],
          },
          isFollowing: {
            $in: [userObjId, { $ifNull: ["$authorDetails.followers", []] }],
          },
        },
      },

      // Final output projection
      {
        $project: {
          description: 1,
          videoUrl: 1,
          thumbnailUrl: 1,
          audioDetails: 1,
          shortDuration: 1,
          tags: 1,
          location: 1,
          shares: 1,
          interactionScore: 1,
          createdAt: 1,
          updatedAt: 1,

          // computed flags and counts
          isLiked: 1,
          likesCount: 1,
          isViewed: 1,
          viewsCount: 1,
          isSaved: 1,
          isFollowing: 1,
          commentCount: 1,

          // author
          "authorDetails.username": 1,
          "authorDetails.displayname": 1,
          "authorDetails.profilePicture": 1,
          "authorDetails._id": 1,
        },
      },

      // Pagination
      { $skip: skip },
      { $limit: pageSize },
    ];

    const result = await ShortsModel.aggregate(pipeline).exec();

    return ResultDB<IShorts[]>(
      STATUS_CODES.OK,
      true,
      SHORTS_MESSAGES.FETCHED_BY_AUTHOR,
      result
    );
  } catch (error) {
    console.error("Error fetching shorts by author:", error);
    return ResultDB<IShorts[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING_BY_AUTHOR,
      null
    );
  }
};

export const generatePreSignedUrl = async (userId: string) => {
  const key = `shorts/${userId}/${Date.now()}.mp4`;
  const command = new PutObjectCommand({
    Bucket: AWS_S3_TEMP_BUCKET_NAME,
    Key: key,
    ContentType: "video/mp4",
  });
  return getSignedUrl(s3Client, command, { expiresIn: 10 * 60 });
};

/**
 * service function for getting shorts feed
 * @param userId
 * @param page
 * @param limit
 * @returns
 */
export const getShortsFeed = async (
  userId: string,
  page: number = 1,
  limit: number = 20
): Promise<ApiResponse<IShorts[]>> => {
  try {
    const skip = (page - 1) * limit;
    const userObjId = new mongoose.Types.ObjectId(userId);

    const feedPipeline = [
      // 🔒 STEP 1: Filter out shorts the user marked as "not interested"
      ...getNotInterestedFilterStages(userId, "short"),

      // Author details
      {
        $lookup: {
          from: "users",
          localField: "author",
          foreignField: "_id",
          as: "authorDetails",
        },
      },
      {
        $unwind: {
          path: "$authorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

      // Saved items
      {
        $lookup: {
          from: "saveditems",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$itemId", "$$shortId"] },
                    { $eq: ["$userId", userObjId] },
                    { $eq: ["$itemType", "short"] },
                  ],
                },
              },
            },
          ],
          as: "savedData",
        },
      },

      // Likes lookup
      {
        $lookup: {
          from: "shortlikes",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$shortId", "$$shortId"] },
              },
            },
          ],
          as: "likesData",
        },
      },

      // Views lookup
      {
        $lookup: {
          from: "shortviews",
          let: { shortId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$shortId", "$$shortId"] },
              },
            },
          ],
          as: "viewsData",
        },
      },

      // Add fields: isLiked, likesCount, isViewed, viewsCount, isSaved
      {
        $addFields: {
          isLiked: {
            $in: [userObjId, "$likesData.userId"],
          },
          likesCount: {
            $size: "$likesData",
          },
          isViewed: {
            $in: [userObjId, "$viewsData.userId"],
          },
          viewsCount: {
            $size: "$viewsData",
          },
          isSaved: {
            $gt: [{ $size: "$savedData" }, 0],
          },
        },
      },

      // Final projection
      {
        $project: {
          description: 1,
          videoUrl: 1,
          thumbnailUrl: 1,
          audioDetails: 1,
          shortDuration: 1,
          tags: 1,
          location: 1,
          shares: 1,
          interactionScore: 1,
          createdAt: 1,
          updatedAt: 1,

          // dynamic data
          isLiked: 1,
          likesCount: 1,
          isViewed: 1,
          viewsCount: 1,
          isSaved: 1,
          commentCount: 1,

          // author
          "authorDetails.username": 1,
          "authorDetails.profilePicture": 1,
          "authorDetails._id": 1,

          // follow status
          isFollowing: {
            $in: [userObjId, { $ifNull: ["$authorDetails.followers", []] }],
          },
        },
      },

      // Pagination
      { $skip: skip },
      { $limit: limit },
    ];

    const feed = await ShortsModel.aggregate(feedPipeline).exec();
    return ResultDB<IShorts[]>(
      STATUS_CODES.OK,
      true,
      SHORTS_MESSAGES.FEED_FETCHED,
      feed
    );
  } catch (error) {
    console.error("Error fetching shorts feed:", error);
    return ResultDB<IShorts[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING_FEED,
      null
    );
  }
};

export const addShortComment = async (
  shortId: string,
  shortAuthorId: string,
  userId: string,
  content: string,
  replyTo?: string
): Promise<ApiResponse<any>> => {
  try {
    const shortObjectId = new Types.ObjectId(shortId);
    const userObjectId = new Types.ObjectId(userId);
    const shortAuthorObjectId = new Types.ObjectId(shortAuthorId);

    const newComment = await new ShortComment({
      shortId: shortObjectId,
      shortAuthorId: shortAuthorObjectId,
      user: userObjectId,
      content,
      parentId: replyTo ?? null,
    }).save();

    if (replyTo) {
      const replyUpdate = await ShortComment.findByIdAndUpdate(replyTo, {
        $addToSet: { replies: newComment._id },
      });

      if (replyUpdate && shortAuthorId !== userId) {
        const isSent = await sendNotification({
          userId: shortAuthorId,
          personId: userId,
          type: ENotificationType.commentReply,
          contentId: shortId,
          contentType: ENotificationContentModel.short,
          message: content,
          commentId: replyTo,
        });

        if (!isSent) console.error("Notification failed for reply comment.");
      }
    } else {
      await ShortsModel.findByIdAndUpdate(shortId, {
        $inc: { commentCount: 1 },
      });

      if (shortAuthorId !== userId) {
        const isSent = await sendNotification({
          userId: shortAuthorId,
          personId: userId,
          type: ENotificationType.commentReply,
          contentId: shortId,
          contentType: ENotificationContentModel.short,
          message: content,
          commentId: newComment._id.toString(),
        });

        if (!isSent) console.error("Notification failed for comment.");
      }
    }

    const populatedComment = await ShortComment.findById(newComment._id)
      .populate("user", "username displayName profilePicture")
      .lean();

    return ResultDB(
      STATUS_CODES.CREATED,
      true,
      SHORT_COMMENT_MESSAGES.ADDED,
      populatedComment
    );
  } catch (error) {
    console.error("Error in addShortComment:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR,
      null
    );
  }
};

export const deleteShortComment = async (
  commentId: string,
  userId: string
): Promise<ApiResponse<null>> => {
  try {
    const comment = await ShortComment.findById(commentId);

    if (!comment) {
      return ResultDB(
        STATUS_CODES.NOT_FOUND,
        false,
        SHORT_COMMENT_MESSAGES.NOT_FOUND,
        null
      );
    }

    if (comment.user.toString() !== userId.toString()) {
      return ResultDB(
        STATUS_CODES.FORBIDDEN,
        false,
        SHORT_COMMENT_MESSAGES.UNAUTHORIZED,
        null
      );
    }

    // Delete parent + replies in one query
    await ShortComment.deleteMany({
      $or: [{ _id: comment._id }, { parentId: comment._id }],
    });

    if (!comment.parentId) {
      await ShortsModel.findByIdAndUpdate(comment.shortId, {
        $inc: { commentCount: -1 },
      });
    } else {
      await ShortComment.findByIdAndUpdate(comment.parentId, {
        $pull: { replies: comment._id },
      });
    }

    return ResultDB(
      STATUS_CODES.OK,
      true,
      SHORT_COMMENT_MESSAGES.DELETED,
      null
    );
  } catch (error) {
    console.error("Error in deleteShortComment:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR,
      null
    );
  }
};

export const getShortComments = async (
  shortId: string,
  page: number,
  limit: number
): Promise<ApiResponse<any>> => {
  try {
    const skip = (page - 1) * limit;

    const comments = await ShortComment.aggregate([
      { $match: { shortId: new Types.ObjectId(shortId), parentId: null } },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "user",
        },
      },
      { $unwind: "$user" },
      {
        $addFields: {
          repliesCount: { $size: { $ifNull: ["$replies", []] } },
        },
      },
      {
        $project: {
          content: 1,
          createdAt: 1,
          repliesCount: 1,
          "user._id": 1,
          "user.username": 1,
          "user.displayName": 1,
          "user.profilePicture": 1,
        },
      },
    ]);

    const totalCount = await ShortComment.countDocuments({
      shortId,
      parentId: null,
    });

    return ResultDB(STATUS_CODES.OK, true, SHORT_COMMENT_MESSAGES.FETCHED, {
      comments,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
      },
    });
  } catch (error) {
    console.error("Error in getShortComments:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR,
      null
    );
  }
};

export const getCommentReplies = async (
  commentId: string,
  page: number,
  limit: number
): Promise<ApiResponse<any>> => {
  try {
    const skip = (page - 1) * limit;

    const replies = await ShortComment.find({ parentId: commentId })
      .populate("user", "username displayName profilePicture")
      .sort({ createdAt: 1 }) // chronological order
      .skip(skip)
      .limit(limit)
      .lean();

    const totalCount = await ShortComment.countDocuments({
      parentId: commentId,
    });

    return ResultDB(
      STATUS_CODES.OK,
      true,
      SHORT_COMMENT_MESSAGES.REPLIES_FETCHED,
      {
        replies,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
        },
      }
    );
  } catch (error) {
    console.error("Error in getCommentReplies:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR,
      null
    );
  }
};

export const likeOrUnlikeShort = async (
  shortId: string,
  userId: string,
  action: "like" | "unlike"
): Promise<ApiResponse<null>> => {
  try {
    const shortObjectId = new Types.ObjectId(shortId);
    const userObjectId = new Types.ObjectId(userId);

    if (action === "like") {
      const result = await ShortLike.updateOne(
        { shortId: shortObjectId, userId: userObjectId },
        { $setOnInsert: { shortId: shortObjectId, userId: userObjectId } },
        { upsert: true }
      );

      if (result.upsertedCount > 0) {
        // Increment likesCount and get updated short with author
        const short = await ShortsModel.findByIdAndUpdate(
          shortId,
          { $inc: { likesCount: 1 } },
          { new: true, select: "author" }
        );

        // Send notification if liker is not the author
        if (short && short.author.toString() !== userId.toString()) {
          const isSent = await sendNotification({
            userId: short.author.toString(), // recipient
            personId: userId, // sender (liker)
            type: ENotificationType.like,
            contentId: shortId,
            contentType: ENotificationContentModel.short,
          });

          if (!isSent) {
            console.error("Failed to send like notification");
          }
        }
      }
    } else if (action === "unlike") {
      const deleteResult = await ShortLike.deleteOne({
        shortId: shortObjectId,
        userId: userObjectId,
      });
      if (deleteResult.deletedCount > 0) {
        const updatedShort = await ShortsModel.findByIdAndUpdate(
          shortId,
          { $inc: { likesCount: -1 } },
          { new: true }
        );

        if (!updatedShort) {
          console.warn(`Short with ID ${shortId} not found while unliking`);
        }
      }
    } else {
      return ResultDB(
        STATUS_CODES.BAD_REQUEST,
        false,
        SHORT_LIKES_MESSAGES.INVALID_ACTION,
        null
      );
    }

    return ResultDB(
      STATUS_CODES.OK,
      true,
      action === "like"
        ? SHORT_LIKES_MESSAGES.LIKED
        : SHORT_LIKES_MESSAGES.UNLIKED,
      null
    );
  } catch (error) {
    console.error("Error in likeOrUnlikeShort:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_LIKES_MESSAGES.ERROR_LIKE_ACTION,
      null
    );
  }
};

export const markShortViewed = async (
  shortId: string,
  userId?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<ApiResponse<null>> => {
  try {
    const shortObjectId = new Types.ObjectId(shortId);

    const viewFilter: any = {
      shortId: shortObjectId,
    };

    if (userId) {
      viewFilter.userId = new Types.ObjectId(userId);
    }

    const existingView = await ShortView.findOne(viewFilter);

    if (!existingView) {
      await ShortView.create({
        shortId: shortObjectId,
        userId: userId ? new Types.ObjectId(userId) : undefined,
        ipAddress,
        userAgent,
      });

      await ShortsModel.findByIdAndUpdate(shortId, {
        $inc: { viewsCount: 1 },
      });
    }

    return ResultDB(STATUS_CODES.OK, true, SHORTS_MESSAGES.VIEW_ADDED, null);
  } catch (error) {
    console.error("Error in markShortViewed:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_ADDING_VIEW,
      null
    );
  }
};

export const getTrendingShorts = async (
  page = 1,
  limit = 10
): Promise<ApiResponse<IShorts[]>> => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: mongoose.PipelineStage[] = [
      {
        $lookup: {
          from: "trendingshortscores",
          localField: "_id",
          foreignField: "shortId",
          as: "trendingData",
        },
      },
      {
        $unwind: {
          path: "$trendingData",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $sort: {
          "trendingData.trendingScore": -1,
        },
      },
      {
        $skip: skip,
      },
      {
        $limit: limit,
      },
      {
        $project: {
          _id: 1,
          description: 1,
          videoUrl: 1,
          thumbnailUrl: 1,
          likesCount: 1,
          commentCount: 1,
          shares: 1,
          viewsCount: 1,
          shortDuration: 1,
          createdAt: 1,
          author: 1,
          tags: 1,
        },
      },
    ];

    const result = await ShortsModel.aggregate(pipeline);

    if (!result || result.length === 0) {
      STATUS_CODES.OK, true, SHORTS_MESSAGES.FETCHED, result;
    }

    return ResultDB<IShorts[]>(
      STATUS_CODES.OK,
      true,
      SHORTS_MESSAGES.FETCHED,
      result
    );
  } catch (error) {
    console.error("Error fetching trending shorts:", error);
    return ResultDB<IShorts[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING,
      []
    );
  }
};

export const fetchPersonalizedRecommendation = async (
  userId: string,
  page = 1,
  limit = 10
): Promise<ApiResponse<IShorts[]>> => {
  try {
    const userObjectId = new mongoose.Types.ObjectId(userId);
    const skip = (page - 1) * limit;

    const pipeline: PipelineStage[] = [
      // 1. Find shorts this user liked or viewed
      {
        $lookup: {
          from: "shortlikes",
          localField: "_id",
          foreignField: "shortId",
          as: "likedBy",
        },
      },
      {
        $lookup: {
          from: "shortviews",
          localField: "_id",
          foreignField: "shortId",
          as: "viewedBy",
        },
      },
      {
        $match: {
          $or: [
            { "likedBy.userId": userObjectId },
            { "viewedBy.userId": userObjectId },
          ],
        },
      },
      {
        $project: {
          _id: 1,
        },
      },

      // 2. Find users who liked these shorts
      {
        $lookup: {
          from: "shortlikes",
          localField: "_id",
          foreignField: "shortId",
          as: "similarLikes",
        },
      },
      {
        $unwind: "$similarLikes",
      },
      {
        $group: {
          _id: null,
          similarUserIds: { $addToSet: "$similarLikes.userId" },
        },
      },

      // 3. Find shorts those users liked
      {
        $lookup: {
          from: "shortlikes",
          let: { userIds: "$similarUserIds" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$userId", "$$userIds"],
                },
              },
            },
          ],
          as: "recommendedLikes",
        },
      },
      {
        $unwind: "$recommendedLikes",
      },

      // 4. Get recommended shorts with metadata
      {
        $replaceRoot: { newRoot: "$recommendedLikes" },
      },
      {
        $lookup: {
          from: "shorts",
          localField: "shortId",
          foreignField: "_id",
          as: "shortDetails",
        },
      },
      {
        $unwind: "$shortDetails",
      },

      {
        $replaceRoot: { newRoot: "$shortDetails" },
      },

      {
        $sort: { createdAt: -1 },
      },
      {
        $skip: skip,
      },
      {
        $limit: limit,
      },
    ];

    const results = await ShortsModel.aggregate(pipeline).exec();

    return ResultDB<IShorts[]>(
      STATUS_CODES.OK,
      true,
      SHORTS_MESSAGES.FETCHED,
      results
    );
  } catch (error) {
    console.error("Error in fetchPersonalizedRecommendation:", error);
    return ResultDB<IShorts[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING,
      []
    );
  }
};

const SHORTS_BATCH_LIMIT = 100;

export const bulkUpdateTrendingShortScores = async (updates: any[]) => {
  try {
    const result = await TrendingShortScore.bulkWrite(updates);
    return result;
  } catch (error) {
    console.error("Error in bulk updating trending short scores:", error);
    throw error;
  }
};

export const updateTrendingShortScores = async () => {
  const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

  let lastId = null;
  let hasMore = true;

  while (hasMore) {
    const shorts: IShorts[] = await ShortsModel.find(
      lastId
        ? { _id: { $gt: lastId }, createdAt: { $gte: cutoffDate } }
        : { createdAt: { $gte: cutoffDate } }
    )
      .sort({ _id: 1 })
      .limit(SHORTS_BATCH_LIMIT);

    if (shorts.length === 0) {
      hasMore = false;
      break;
    }

    const updates = await Promise.all(
      shorts.map(async (short) => {
        try {
          const score = await calculateTrendingScore(
            Number(short.viewsCount),
            Number(short.likesCount),
            Number(short.sharesCount),
            short.createdAt
          );

          return {
            updateOne: {
              filter: { shortId: short._id },
              update: { shortId: short._id, trendingScore: score },
              upsert: true,
            },
          };
        } catch (err) {
          console.error(`Error calculating score for short ${short._id}:`, err);
          return null;
        }
      })
    );

    const validUpdates = updates.filter(Boolean);
    await bulkUpdateTrendingShortScores(validUpdates);
    lastId = shorts[shorts.length - 1]._id;
  }

  return ResultDB(
    STATUS_CODES.OK,
    true,
    SHORTS_MESSAGES.TRENDING_SCORES_UPDATED,
    null
  );
};

export const shortsByCategory = async (
  category: string,
  page: number = 1,
  limit: number = 10
) => {
  try {
    const skip = (page - 1) * limit;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          category: category,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "author",
          foreignField: "_id",
          as: "authorDetails",
        },
      },
      {
        $unwind: "$authorDetails",
      },
      {
        $project: {
          _id: 1,
          description: 1,
          likesCount: 1,
          category: 1,
          tags: 1,
          author: {
            _id: "$authorDetails._id",
            username: "$authorDetails.username",
            profilePicture: "$authorDetails.profilePicture",
          },
          createdAt: 1,
        },
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ];

    const shorts = await ShortsModel.aggregate(pipeline);
    const total = await ShortsModel.countDocuments({
      category,
    });
    const pagination = {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    return ResultDB(200, true, "Shorts fetched successfully by category", {
      shorts,
      pagination,
    });
  } catch (error) {
    console.error("Error fetching shorts by category:", error);
    return ResultDB(500, false, "Failed to fetch shorts by category", null);
  }
};

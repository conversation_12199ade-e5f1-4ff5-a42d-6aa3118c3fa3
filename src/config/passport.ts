import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { Strategy as LocalStrategy } from "passport-local";

import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET } from "./environment";
import { UserService } from "../services";
import { generateBaseUsername, generateUniqueUsername } from "../lib/username";

// Only configure Google OAuth if credentials are available
if (GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET) {
  passport.use(
    "google",
    new GoogleStrategy(
      {
        clientID: GOOGLE_CLIENT_ID,
        clientSecret: GOOGLE_CLIENT_SECRET,
        callbackURL: "/v1/api/auth/google/callback",
      },
      async (accessToken, refreshToken, profile, done) => {
        const email = profile.emails?.[0].value ?? "";
        const user = await UserService.getUserByEmail(email);

        if (!user) {
          const baseUsername = generateBaseUsername(profile.displayName || email);
          const username = await generateUniqueUsername(baseUsername);

          const newUser = await UserService.createUser({
            username: username.slice(0, 30),
            displayName: profile.displayName ?? "",
            email,
            profilePicture: profile.photos?.[0].value ?? "",
            provider: {
              provider: "google",
              id: profile.id,
            },
          });

          if (newUser) {
            return done(null, newUser);
          }
        } else {
          return done(null, user);
        }
      }
    )
  );
  console.log('✅ Google OAuth strategy initialized');
} else {
  console.warn('⚠️  Google OAuth not configured - missing GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET');
}

passport.use(
  "email",
  new LocalStrategy(
    { usernameField: "email" },
    async (email, password, done) => {
      try {
        const user = await UserService.getUserByEmail(email, true);

        if (!user) return done(null, false);

        const passwordMatch = await user.verifyPassword(password);

        if (!passwordMatch) return done(null, false);

        return done(null, user);
      } catch (err) {
        return done(err);
      }
    }
  )
);

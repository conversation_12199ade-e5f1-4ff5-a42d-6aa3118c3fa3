import admin from "firebase-admin";

// Create service account object from environment variables
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID,
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${encodeURIComponent(process.env.FIREBASE_CLIENT_EMAIL || '')}`,
  universe_domain: "googleapis.com"
};

// Validate required environment variables
const requiredEnvVars = [
  'FIREBASE_PROJECT_ID',
  'FIREBASE_PRIVATE_KEY_ID', 
  'FIREBASE_PRIVATE_KEY',
  'FIREBASE_CLIENT_EMAIL',
  'FIREBASE_CLIENT_ID'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

// Initialize Firebase only if all variables are present or in production
let messaging: admin.messaging.Messaging | null = null;
let firebaseAdmin: typeof admin | null = null;

if (missingVars.length === 0) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
    });
    messaging = admin.messaging();
    firebaseAdmin = admin;
    console.log('✅ Firebase initialized successfully');
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    if (process.env.NODE_ENV === 'production') {
      throw error;
    }
  }
} else {
  const warningMessage = `⚠️  Firebase not initialized - missing variables: ${missingVars.join(', ')}`;
  
  if (process.env.NODE_ENV === 'production') {
    throw new Error(`Missing required Firebase environment variables: ${missingVars.join(', ')}`);
  } else {
    console.warn(warningMessage);
    console.warn('💡 Firebase features will be disabled. Add credentials to .env file to enable Firebase.');
  }
}

export { messaging };
export { firebaseAdmin as admin };

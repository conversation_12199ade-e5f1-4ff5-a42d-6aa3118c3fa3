import "dotenv/config";

export const PORT = parseInt(process.env.PORT || "8081", 10);
export const CORS_ORIGINS = process.env.CORS_ORIGINS ?? "";

export const MONGODB_URI = process.env.MONGODB_URI ?? "";

export const CLIENT_URL = process.env.CLIENT_URL ?? "";

export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID ?? "";
export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET ?? "";

export const APPLE_CLIENT_ID = process.env.APPLE_CLIENT_ID ?? "";
export const APPLE_PRIVATE_KEY = process.env.APPLE_PRIVATE_KEY ?? "";
export const APPLE_KEY_ID = process.env.APPLE_KEY_ID ?? "";
export const APPLE_TEAM_ID = process.env.APPLE_TEAM_ID ?? "";

export const EMAIL_HOST = process.env.EMAIL_HOST;
export const EMAIL_PORT = process.env.EMAIL_PORT ?? "";
export const EMAIL_SENDER_MAIL = process.env.EMAIL_SENDER_MAIL ?? "";
export const EMAIL_SENDER_PASSWORD = process.env.EMAIL_SENDER_PASSWORD ?? "";
export const EMAIL_USERNAME = process.env.EMAIL_USERNAME ?? "apikey";

export const COOKIE_SECRET = process.env.COOKIE_SECRET ?? "";
export const JWT_SECRET = process.env.JWT_SECRET ?? "";

// Updated for live2 container with LLHLS support
export const RTMP_BASE_URL = process.env.RTMP_BASE_URL ?? "rtmp://localhost:1935/app";
export const RTMP_LIVE_URL = process.env.RTMP_LIVE_URL ?? "rtmp://localhost:1935/app";
export const HLS_BASE_URL = process.env.HLS_BASE_URL ?? "http://localhost:8080/hls";
export const HLS_BUCKET = process.env.HLS_BUCKET ?? "";

// CloudFront URL for LLHLS playback
export const HLS_CLOUDFRONT_URL = process.env.HLS_CLOUDFRONT_URL ?? "http://**************:8080/app";
export const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID ?? "";
export const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY ?? "";
export const AWS_REGION = process.env.AWS_REGION ?? "";
export const AWS_S3_BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME ?? "";
export const AWS_S3_TEMP_BUCKET_NAME =
  process.env.AWS_S3_TEMP_BUCKET_NAME ?? "";
export const AWS_S3_TRANSCODED_BUCKET_NAME =
  process.env.AWS_S3_TRANSCODED_BUCKET_NAME ?? "";
export const AWS_S3_SQS_URL = process.env.AWS_S3_SQS_URL ?? "";
export const AWS_ECS_TASK_DEFINITION =
  process.env.AWS_ECS_TASK_DEFINITION ?? "";
export const AWS_ECS_CLUSTER_URL = process.env.AWS_ECS_CLUSTER_URL ?? "";
export const AWS_ECS_TASK_CONTAINER_NAME =
  process.env.AWS_ECS_TASK_CONTAINER_NAME ?? "";

export const STRIPE_API_KEY = process.env.STRIPE_API_KEY ?? "";
export const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET ?? "";

// Live2 container specific settings
export const LIVE2_RTMP_PORT = process.env.LIVE2_RTMP_PORT ?? "1935";
export const LIVE2_HTTP_PORT = process.env.LIVE2_HTTP_PORT ?? "8080";
export const LIVE2_CONTAINER_NAME = process.env.LIVE2_CONTAINER_NAME ?? "rtmp-hls-server";

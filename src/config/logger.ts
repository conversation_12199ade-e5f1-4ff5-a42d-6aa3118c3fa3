import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import { StreamOptions } from "morgan";
import fs from "fs";

// Create logs directory if it doesn't exist
if (!fs.existsSync("logs")) {
  fs.mkdirSync("logs");
}

const { combine, timestamp, printf, colorize, json, errors } = winston.format;

const customFormat = printf(({ level, message, timestamp, service, ...meta }) => {
  const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : "";
  return `${timestamp} [${service || "vyoo-api"}] ${level.toUpperCase()}: ${message}${metaStr}`;
});

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: combine(
    timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    errors({ stack: true }),
    customFormat
  ),
  defaultMeta: { 
    service: "vyoo-api",
    version: process.env.npm_package_version || "1.0.0",
    environment: process.env.NODE_ENV || "development"
  },
  transports: [
    // Error logs with daily rotation
    new DailyRotateFile({
      filename: "logs/error-%DATE%.log",
      datePattern: "YYYY-MM-DD",
      level: "error",
      maxSize: "20m",
      maxFiles: "14d",
      zippedArchive: true
    }),
    
    // Combined logs with daily rotation
    new DailyRotateFile({
      filename: "logs/combined-%DATE%.log",
      datePattern: "YYYY-MM-DD",
      maxSize: "20m",
      maxFiles: "30d",
      zippedArchive: true
    }),
    
    // Streaming specific logs
    new DailyRotateFile({
      filename: "logs/streaming-%DATE%.log",
      datePattern: "YYYY-MM-DD",
      level: "info",
      maxSize: "50m",
      maxFiles: "7d",
      zippedArchive: true,
      format: combine(timestamp(), json())
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: "logs/exceptions.log" })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: "logs/rejections.log" })
  ]
});

// Add console transport based on environment
if (process.env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console({
      format: combine(colorize(), customFormat),
    })
  );
} else {
  // In production, add console for critical errors only
  logger.add(
    new winston.transports.Console({
      level: "error",
      format: combine(timestamp(), json())
    })
  );
}

export const morganStream: StreamOptions = {
  write: (message) => logger.info(message.trim()),
};

// Streaming logger for Live2 specific events
export const streamingLogger = logger.child({ component: "live2-streaming" });

export default logger;

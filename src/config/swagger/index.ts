import { aboutPaths } from "./routes/about";
import { authenticationPaths } from "./routes/authentication";
import { notificationPaths } from "./routes/notification";
import { reportPaths } from "./routes/problemReport";
import { searchPaths } from "./routes/search";
import { streamPaths } from "./routes/stream";
import { stripePaths } from "./routes/stripe";
import { userPaths } from "./routes/user";
import { storyPaths } from "./routes/story";
import { shortsPaths } from "./routes/shorts";
import { playlistPaths } from "./routes/playlist";

export const swaggerOptions = {
  swaggerDefinition: {
    openapi: "3.0.1",
    info: {
      title: "API Documentation of Vyooo",
      version: "1.0.0",
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT", // Optional: JWT or other token types
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    paths: {
      ...authenticationPaths,
      ...userPaths,
      ...streamPaths,
      ...searchPaths,
      ...stripePaths,
      ...aboutPaths,
      ...reportPaths,
      ...notificationPaths,
      ...storyPaths,
      ...shortsPaths,
      ...playlistPaths,
    },
  },
  apis: ["src/routes/*.ts", "src/model/**/*.ts"],
};

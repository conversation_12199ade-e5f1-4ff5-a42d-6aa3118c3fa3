export const storyPaths = {
  "/v1/api/story/user-story": {
    get: {
      summary: "Get My Stories",
      description: "Retrieve all stories created by the logged-in user.",
      tags: ["Story"],
      security: [{ bearerAuth: [] }],
      responses: {
        "200": {
          description: "User stories retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "array", items: { type: "object" } },
                },
              },
            },
          },
        },
        "500": {
          description: "Error retrieving user stories.",
        },
      },
    },
  },
  "/v1/api/story/{storyId}": {
    get: {
      summary: "Get Stories by User ID",
      description: "Retrieve all stories of a specific user by their ID.",
      tags: ["Story"],
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          name: "storyId",
          in: "path",
          required: true,
          schema: { type: "string" },
          description: "The ID of the user whose stories are being retrieved.",
        },
      ],
      responses: {
        "200": {
          description: "User stories retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "array", items: { type: "object" } },
                },
              },
            },
          },
        },
        "500": {
          description: "Error retrieving user stories.",
        },
      },
    },
  },
  "/v1/api/story/view": {
    post: {
      summary: "Mark Story as Viewed",
      description: "Mark a story as viewed by the logged-in user.",
      tags: ["Story"],
      security: [{ bearerAuth: [] }],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              required: ["storyId"],
              properties: {
                storyId: {
                  type: "string",
                  description: "The ID of the story to mark as viewed.",
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Story marked as viewed or already viewed.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" },
                },
              },
            },
          },
        },
        "500": {
          description: "Error marking story as viewed.",
        },
      },
    },
  },
  "/v1/api/story/upload-story": {
    post: {
      summary: "Upload a Story",
      description:
        "Upload a new story with media, caption, and optional location.",
      tags: ["Story"],
      security: [{ bearerAuth: [] }],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              required: ["media"],
              properties: {
                media: {
                  type: "array",
                  items: { type: "string", format: "uri" },
                  minItems: 1,
                  maxItems: 10,
                },
                caption: {
                  type: "string",
                  maxLength: 300,
                },
                location: {
                  type: "object",
                  properties: {
                    coordinates: {
                      type: "object",
                      properties: {
                        lat: { type: "string" },
                        lng: { type: "string" },
                      },
                    },
                    address: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
      responses: {
        "201": {
          description: "Story created successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" },
                },
              },
            },
          },
        },
        "500": {
          description: "Error creating story.",
        },
      },
    },
  },
  "/v1/api/story/followings": {
    post: {
      summary: "Get Stories by Followings",
      description:
        "Retrieve stories posted by the users the current user is following.",
      tags: ["Story"],
      security: [{ bearerAuth: [] }],
      responses: {
        "200": {
          description: "Stories retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "array", items: { type: "object" } },
                },
              },
            },
          },
        },
        "500": {
          description: "Error retrieving stories by followings.",
        },
      },
    },
  },
};

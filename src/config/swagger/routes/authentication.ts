export const authenticationPaths = {
  "/v1/api/auth/email/signup": {
    post: {
      summary: "Sign up with email",
      description: "Signs up a user using email.",
      tags: ["Authentication"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                username: {
                  type: "string",
                  example: "nargis1",
                  minLength: 3,
                  maxLength: 30,
                },
                email: {
                  type: "string",
                  example: "<EMAIL>",
                },
                password: {
                  type: "string",
                  example: "nargis123",
                  pattern: "^[a-zA-Z0-9]{3,30}$",
                },
                deviceInfo: {
                  type: "object",
                  properties: {
                    platform: {
                      type: "string",
                      example: "ios",
                    },
                    fcmToken: {
                      type: "string",
                      example: "fgsgsgfdsgfgsdf",
                    },
                    OSVersion: {
                      type: "string",
                      example: "18.1",
                    },
                  },
                  required: ["platform", "fcmToken", "OSVersion"],
                },
                interests: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      category: {
                        type: "string",
                        example: "Entertainment",
                      },
                      subCategories: {
                        type: "array",
                        items: {
                          type: "string",
                          example: "Music",
                        },
                      },
                    },
                    required: ["category", "subCategories"],
                  },
                },
              },
              required: [
                "username",
                "email",
                "password",
                "deviceInfo",
                "interests",
              ],
            },
          },
        },
      },
      responses: {
        "201": {
          description: "User created successfully",
        },
        "400": {
          description: "Invalid input or user already exists",
        },
        "500": {
          description: "Error signing up",
        },
      },
    },
  },
  "/v1/api/auth/email": {
    post: {
      summary: "Log in with email",
      description:
        "Authenticates a user using their email and returns user information along with a token.",
      tags: ["Authentication"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                loginIdentifier: {
                  type: "string",
                  example: "<EMAIL> || username",
                },
                password: { type: "string", example: "securepassword123" },
                deviceInfo: {
                  type: "object",
                  properties: {
                    platform: {
                      type: "string",
                      example: "ios",
                    },
                    fcmToken: {
                      type: "string",
                      example: "fgsgsgfdsgfgsdf",
                    },
                    OSVersion: {
                      type: "string",
                      example: "18.1",
                    },
                  },
                  required: ["platform", "fcmToken", "OSVersion"],
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Successfully logged in",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: true },
                  message: { type: "string", example: "Login successful" },
                  user: { $ref: "#/components/schemas/User" },
                  token: { type: "string", example: "jwt_token_here" },
                },
              },
            },
          },
        },
        "401": {
          description: "Error logging in",
        },
      },
    },
  },
  "/v1/api/auth/forgot-password": {
    post: {
      summary: "Send forgot password OTP",
      description: "Sends an OTP to the user's email for password reset.",
      tags: ["Authentication"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: { type: "string", example: "<EMAIL>" },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "OTP sent successfully",
        },
        "404": {
          description: "User not found",
        },
        "500": {
          description: "Error sending OTP",
        },
      },
    },
  },
  "/v1/api/auth/verify-otp": {
    post: {
      summary: "Verify OTP",
      description: "Verifies the OTP sent to the user's email.",
      tags: ["Authentication"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: { type: "string", example: "<EMAIL>" },
                otp: { type: "string", example: "1234" },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "OTP verified successfully",
        },
        "400": {
          description: "Invalid or expired OTP",
        },
        "500": {
          description: "Error verifying OTP",
        },
      },
    },
  },
  "/v1/api/auth/reset-password": {
    post: {
      summary: "Reset password",
      description:
        "Resets the user's password using the OTP sent to their email.",
      tags: ["Authentication"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: { type: "string", example: "<EMAIL>" },
                otp: { type: "string", example: "1234" },
                newPassword: {
                  type: "string",
                  example: "newsecurepassword",
                  minLength: 8,
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Password reset successfully",
        },
        "400": {
          description: "Invalid or expired OTP",
        },
        "404": {
          description: "User not found",
        },
        "500": {
          description: "Error resetting password",
        },
      },
    },
  },
  "/v1/api/stream/share-stream": {
    post: {
      summary: "Share a Stream",
      description: "Allows a user to share a stream on a specified platform.",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                streamId: {
                  type: "string",
                  description: "The unique ID of the stream to be shared.",
                  example: "676a8be7e5e301efc25d7f7d",
                },
                platform: {
                  type: "string",
                  description: "The platform where the stream is shared.",
                  example: "whatsapp",
                },
              },
              required: ["streamId", "platform"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Stream shared successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: true,
                  },
                  message: {
                    type: "string",
                    example: "Stream shared successfully.",
                  },
                },
              },
            },
          },
        },
        "400": {
          description: "Invalid input data.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: false,
                  },
                  message: {
                    type: "string",
                    example: "Invalid input.",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Internal server error.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: false,
                  },
                  message: {
                    type: "string",
                    example: "Something went wrong.",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};

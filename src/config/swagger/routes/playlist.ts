export const playlistPaths = {
  "/v1/api/playlist": {
    get: {
      summary: "Get All Playlists for a Creator",
      description:
        "Retrieve paginated playlists created by a specific creator with optional search by title or description.",
      tags: ["Playlist"],
      parameters: [
        {
          name: "creatorId",
          in: "query",
          required: true,
          schema: { type: "string" },
          description:
            "The ID of the playlist creator (user whose profile is being viewed).",
        },
        {
          name: "search",
          in: "query",
          required: false,
          schema: { type: "string" },
          description:
            "Search term to filter playlists by title or description.",
        },
        {
          name: "page",
          in: "query",
          required: false,
          schema: { type: "integer", default: 1 },
          description: "Page number for pagination.",
        },
        {
          name: "limit",
          in: "query",
          required: false,
          schema: { type: "integer", default: 10 },
          description: "Number of items per page.",
        },
      ],
      responses: {
        "200": {
          description: "Playlists fetched successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "object",
                    properties: {
                      playlists: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            _id: { type: "string" },
                            title: { type: "string" },
                            description: { type: "string" },
                            createdAt: { type: "string", format: "date-time" },
                            updatedAt: { type: "string", format: "date-time" },
                            itemsCount: { type: "number" },
                          },
                        },
                      },
                      pagination: {
                        type: "object",
                        properties: {
                          totalRecords: { type: "number" },
                          totalPages: { type: "number" },
                          currentPage: { type: "number" },
                          limit: { type: "number" },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "500": { description: "Internal Server Error." },
      },
    },
  },

  "/v1/api/playlist/{id}": {
    get: {
      summary: "Get Playlist by ID",
      description: "Retrieve details of a single playlist by its ID.",
      tags: ["Playlist"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: { type: "string" },
          description: "ID of the playlist to fetch.",
        },
      ],
      responses: {
        "200": {
          description: "Playlist fetched successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" }, // Add detailed schema if available
                },
              },
            },
          },
        },
        "500": { description: "Internal Server Error." },
      },
    },
  },

  "/v1/api/playlist/create": {
    post: {
      summary: "Create New Playlist",
      description: "Create a new playlist for the authenticated user.",
      tags: ["Playlist"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                title: { type: "string" },
                description: { type: "string" },
              },
              required: ["title"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Playlist created successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" },
                },
              },
            },
          },
        },
        "500": { description: "Internal Server Error." },
      },
    },
  },

  "/v1/api/playlist/{id}/add": {
    post: {
      summary: "Add Items to Playlist",
      description: "Add items (videos/shorts) to a playlist by playlist ID.",
      tags: ["Playlist"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: { type: "string" },
          description: "ID of the playlist to add items to.",
        },
      ],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                items: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      itemId: { type: "string" },
                      itemType: { type: "string", enum: ["stream", "short"] },
                    },
                    required: ["itemId", "itemType"],
                  },
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Items added successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" },
                },
              },
            },
          },
        },
        "500": { description: "Internal Server Error." },
      },
    },
  },

  "/v1/api/playlist/{id}/remove": {
    delete: {
      summary: "Remove Item from Playlist",
      description: "Remove a specific item from a playlist.",
      tags: ["Playlist"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: { type: "string" },
          description: "ID of the playlist.",
        },
      ],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                itemId: { type: "string" },
                itemType: { type: "string", enum: ["stream", "short"] },
              },
              required: ["itemId", "itemType"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Item removed successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" },
                },
              },
            },
          },
        },
        "500": { description: "Internal Server Error." },
      },
    },
  },

  "/v1/api/playlist/{id}/update": {
    put: {
      summary: "Update Playlist",
      description: "Update the title or description of a playlist.",
      tags: ["Playlist"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: { type: "string" },
          description: "ID of the playlist to update.",
        },
      ],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                title: { type: "string" },
                description: { type: "string" },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Playlist updated successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "object" },
                },
              },
            },
          },
        },
        "500": { description: "Internal Server Error." },
      },
    },
  },
};

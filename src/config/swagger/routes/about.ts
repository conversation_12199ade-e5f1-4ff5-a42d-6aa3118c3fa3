export const aboutPaths = {
  "/v1/api/about/privacy-policy": {
    get: {
      summary: "Get Privacy Policy",
      description: "Retrieve the privacy policy of the application.",
      tags: ["About"],
      responses: {
        "200": {
          description: "Privacy policy retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                  },
                  data: {
                    type: "string",
                    description: "The privacy policy content",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Error retrieving privacy policy.",
        },
      },
    },
  },
  "/v1/api/about/terms-of-service": {
    get: {
      summary: "Get Terms of Service",
      description: "Retrieve the terms of service for the application.",
      tags: ["About"],
      responses: {
        "200": {
          description: "Terms of service retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                  },
                  data: {
                    type: "string",
                    description: "The terms of service content",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Error retrieving terms of service.",
        },
      },
    },
  },
};

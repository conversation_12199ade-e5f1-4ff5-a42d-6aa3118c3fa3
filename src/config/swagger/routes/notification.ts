export const notificationPaths = {
  "/v1/api/notification": {
    post: {
      summary: "Fetch user notifications",
      operationId: "fetchNotifications",
      tags: ["Notifications"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                type: {
                  type: "string",
                  enum: ["all", "unread"],
                  description: "Filter notifications by type",
                  example: "all",
                },
                page: {
                  type: "integer",
                  description: "Page number for pagination",
                  default: 1,
                },
                limit: {
                  type: "integer",
                  description: "Number of notifications per page",
                  default: 20,
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "List of user notifications",
          content: {
            "application/json": {
              schema: {
                type: "array",
                items: {
                  $ref: "#/components/schemas/Notification",
                },
              },
            },
          },
        },
        "500": {
          description: "Error fetching notifications",
        },
      },
    },
  },

  "/v1/api/notification/read": {
    post: {
      summary: "Mark notifications as read",
      operationId: "markNotificationsAsRead",
      tags: ["Notifications"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                ids: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  description: "Array of notification IDs to mark as read",
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Notifications marked as read successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                    example: "Notifications marked as read successfully",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Error marking notifications as read",
        },
      },
    },
  },

  "/v1/api/notification/unreadCount": {
    get: {
      summary: "Get unread notifications count",
      operationId: "getUnreadNotificationsCount",
      tags: ["Notifications"],
      responses: {
        "200": {
          description: "Unread notifications count",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  count: {
                    type: "integer",
                    description: "Count of unread notifications",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Error fetching unread notifications count",
        },
      },
    },
  },
};

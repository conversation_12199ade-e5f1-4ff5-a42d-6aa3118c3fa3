export const userPaths = {
  "/v1/api/user/verify": {
    get: {
      summary: "Verify user email",
      description: "Verify the user's email using a token.",
      tags: ["User"],
      parameters: [
        {
          name: "token",
          in: "query",
          required: true,
          schema: {
            type: "string",
          },
          description: "Verification token",
        },
      ],
      responses: {
        "200": {
          description: "Email verified successfully.",
        },
        "401": {
          description: "Error verifying user.",
        },
      },
    },
  },
  "/v1/api/user/send-verification-email": {
    get: {
      summary: "Send email verification mail",
      description: "Send a verification email to the user.",
      tags: ["User"],
      responses: {
        "200": {
          description: "Email sent successfully.",
        },
        "401": {
          description: "Unauthorized or error sending mail.",
        },
      },
    },
  },
  "/v1/api/user/content/{id}": {
    get: {
      summary: "Get user streams",
      description: "Retrieve streams created by a specific user.",
      tags: ["User"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: {
            type: "string",
          },
          description: "User ID",
        },
      ],
      responses: {
        "200": {
          description: "Streams retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  streams: {
                    type: "array",
                    items: {
                      type: "object",
                    },
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error.",
        },
      },
    },
  },
  "/v1/api/user": {
    put: {
      summary: "Update user details",
      description: "Update the user's personal details.",
      tags: ["User"],
      requestBody: {
        content: {
          "multipart/form-data": {
            schema: {
              type: "object",
              properties: {
                file: {
                  type: "string",
                  format: "binary",
                  description: "Profile picture file",
                },
                displayName: {
                  type: "string",
                  description: "Display name of the user",
                },
                bio: {
                  type: "string",
                  description: "User biography",
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "User details updated successfully.",
        },
        "401": {
          description: "Unauthorized.",
        },
        "500": {
          description: "Error updating user details.",
        },
      },
    },
  },
  "/v1/api/user/settings": {
    put: {
      summary: "Update user settings",
      description: "Update the user's settings like notifications and theme.",
      tags: ["User"],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                notifications: {
                  type: "object",
                  additionalProperties: {
                    type: "boolean",
                  },
                  description: "Notification settings",
                },
                theme: {
                  type: "string",
                  enum: ["light", "dark", "system"],
                  description: "User's theme preference",
                },
              },
            },
          },
        },
      },
      responses: {
        "200": {
          description: "User settings updated successfully.",
        },
        "401": {
          description: "Unauthorized.",
        },
        "500": {
          description: "Error updating user settings.",
        },
      },
    },
  },
  "/v1/api/user/follow": {
    post: {
      summary: "Follow or unfollow a user",
      description: "Follow or unfollow another user.",
      tags: ["User"],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                targetUserId: {
                  type: "string",
                  description: "ID of the user to follow/unfollow",
                },
              },
              required: ["targetUserId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Follow status updated.",
        },
        "400": {
          description: "Bad request.",
        },
        "404": {
          description: "User not found.",
        },
        "500": {
          description: "Server error.",
        },
      },
    },
  },
  "/v1/api/user/post-settings": {
    put: {
      summary: "Update post settings",
      description: "Update settings related to user's posts.",
      tags: ["User"],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                postVisibility: {
                  type: "string",
                  enum: ["public", "private"],
                  description: "Visibility of the posts",
                },
                liveVisibility: {
                  type: "string",
                  enum: ["public", "private"],
                  description: "Visibility of live streams",
                },
                liveVideoPricing: {
                  type: "number",
                  description: "Pricing for live video content",
                },
              },
              required: [
                "postVisibility",
                "liveVisibility",
                "liveVideoPricing",
              ],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Post settings updated successfully.",
        },
        "401": {
          description: "Unauthorized.",
        },
        "500": {
          description: "Error updating post settings.",
        },
      },
    },
  },
  "/v1/api/user/": {
    delete: {
      summary: "Delete user account",
      description: "Delete a user account with a reason.",
      tags: ["User"],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                category: {
                  type: "string",
                  description: "Category of account deletion",
                },
                reason: {
                  type: "string",
                  description: "Reason for account deletion",
                },
              },
              required: ["category", "reason"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "User account deleted successfully.",
        },
        "401": {
          description: "Unauthorized.",
        },
        "500": {
          description: "Error deleting user account.",
        },
      },
    },
  },

  "/v1/api/user/not-interested/add": {
    post: {
      summary: "Add a user to the not interested list",
      operationId: "addToNotInterested",
      tags: ["User"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                targetUserId: {
                  type: "string",
                  description:
                    "ID of the user to be added to the not interested list",
                },
              },
              required: ["targetUserId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "User added to not interested list successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: true },
                  message: {
                    type: "string",
                    example: "User added to not interested list",
                  },
                  data: {
                    type: "object",
                    properties: {
                      notInterestedCount: { type: "integer", example: 5 },
                    },
                  },
                },
              },
            },
          },
        },
        "400": {
          description: "Invalid user ID or other validation error",
        },
        "404": {
          description: "Target user not found or cannot add yourself",
        },
        "500": {
          description: "Internal server error",
        },
      },
    },
  },

  "/v1/api/user/not-interested/remove": {
    post: {
      summary: "Remove a user from the not interested list",
      operationId: "removeFromNotInterested",
      tags: ["User"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                targetUserId: {
                  type: "string",
                  description:
                    "ID of the user to remove from the not interested list",
                },
              },
              required: ["targetUserId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "User removed from not interested list successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: true },
                  message: {
                    type: "string",
                    example: "User removed from not interested list",
                  },
                  data: {
                    type: "object",
                    properties: {
                      notInterestedCount: { type: "integer", example: 4 },
                    },
                  },
                },
              },
            },
          },
        },
        "400": {
          description: "Invalid user ID",
        },
        "404": {
          description: "User not found",
        },
        "500": {
          description: "Internal server error",
        },
      },
    },
  },

  "/v1/api/user/not-interested": {
    get: {
      summary: "Get list of not interested users",
      operationId: "getNotInterestedUsers",
      tags: ["User"],
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          schema: {
            type: "integer",
            default: 1,
          },
          description: "Page number for pagination",
        },
        {
          name: "limit",
          in: "query",
          required: false,
          schema: {
            type: "integer",
            default: 20,
          },
          description: "Number of users per page",
        },
      ],
      responses: {
        "200": {
          description: "List of users in the not interested list",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: true },
                  data: {
                    type: "object",
                    properties: {
                      users: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            username: { type: "string" },
                            displayName: { type: "string" },
                            profilePicture: { type: "string" },
                            bio: { type: "string" },
                          },
                        },
                      },
                      pagination: {
                        type: "object",
                        properties: {
                          total: { type: "integer", example: 50 },
                          page: { type: "integer", example: 1 },
                          totalPages: { type: "integer", example: 3 },
                          hasMore: { type: "boolean", example: true },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "404": {
          description: "User not found",
        },
        "500": {
          description: "Internal server error",
        },
      },
    },
  },

  "/v1/api/user/not-interested/check": {
    post: {
      summary: "Check if a user is in the not interested list",
      operationId: "checkNotInterested",
      tags: ["User"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                targetUserId: {
                  type: "string",
                  description: "ID of the user to check",
                },
              },
              required: ["targetUserId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "User check in not interested list successful",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: true },
                  data: {
                    type: "object",
                    properties: {
                      isNotInterested: { type: "boolean", example: true },
                    },
                  },
                },
              },
            },
          },
        },
        "400": {
          description: "Invalid user ID",
        },
        "404": {
          description: "User not found",
        },
        "500": {
          description: "Internal server error",
        },
      },
    },
  },
  "/v1/api/user/check-username": {
    post: {
      summary: "Check Username Availability",
      description: "Checks if a given username is available for use.",
      tags: ["User"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                username: {
                  type: "string",
                  example: "newUsername123",
                  description: "Username to check for availability",
                },
              },
              required: ["username"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Username availability response",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: true },
                  message: { type: "string", example: "Username is available" },
                },
              },
            },
          },
        },
        "500": {
          description: "Internal server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", example: false },
                  message: {
                    type: "string",
                    example:
                      "An error occurred while checking username availability",
                  },
                  error: { type: "string", example: "Error details here" },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/user/profile/{userId}/followers": {
    get: {
      summary: "Get Followers of a User",
      description:
        "Returns a paginated list of followers for the specified user, with optional search by username or display name.",
      tags: ["User"],
      parameters: [
        {
          name: "userId",
          in: "path",
          required: true,
          description: "ID of the user whose followers you want to fetch.",
          schema: {
            type: "string",
            pattern: "^[0-9a-fA-F]{24}$",
          },
        },
        {
          name: "page",
          in: "query",
          required: false,
          description: "Page number for pagination (default: 1).",
          schema: {
            type: "integer",
            default: 1,
          },
        },
        {
          name: "limit",
          in: "query",
          required: false,
          description: "Number of results per page (default: 10).",
          schema: {
            type: "integer",
            default: 10,
          },
        },
        {
          name: "search",
          in: "query",
          required: false,
          description: "Search followers by username or display name.",
          schema: {
            type: "string",
            default: "",
          },
        },
      ],
      responses: {
        200: {
          description: "Followers fetched successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "object",
                    properties: {
                      followers: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            username: { type: "string" },
                            displayName: { type: "string" },
                            profilePicture: { type: "string" },
                            isFollowedByCurrentUser: { type: "boolean" },
                            followerCount: { type: "number" },
                            followingCount: { type: "number" },
                          },
                        },
                      },
                      pagination: {
                        type: "object",
                        properties: {
                          currentPage: { type: "integer" },
                          limit: { type: "integer" },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        400: { description: "Invalid user ID." },
        500: { description: "Internal server error." },
      },
    },
  },
  "/v1/api/user/profile/{userId}/following": {
    get: {
      summary: "Get Following of a User",
      description:
        "Returns a paginated list of users that the specified user is following, with optional search by username or display name.",
      tags: ["User"],
      parameters: [
        {
          name: "userId",
          in: "path",
          required: true,
          description: "ID of the user whose following list you want to fetch.",
          schema: {
            type: "string",
            pattern: "^[0-9a-fA-F]{24}$",
          },
        },
        {
          name: "page",
          in: "query",
          required: false,
          description: "Page number for pagination (default: 1).",
          schema: {
            type: "integer",
            default: 1,
          },
        },
        {
          name: "limit",
          in: "query",
          required: false,
          description: "Number of results per page (default: 10).",
          schema: {
            type: "integer",
            default: 10,
          },
        },
        {
          name: "search",
          in: "query",
          required: false,
          description: "Search following users by username or display name.",
          schema: {
            type: "string",
            default: "",
          },
        },
      ],
      responses: {
        200: {
          description: "Following list fetched successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "object",
                    properties: {
                      following: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            username: { type: "string" },
                            displayName: { type: "string" },
                            profilePicture: { type: "string" },
                            isFollowedByCurrentUser: { type: "boolean" },
                            followerCount: { type: "number" },
                            followingCount: { type: "number" },
                          },
                        },
                      },
                      pagination: {
                        type: "object",
                        properties: {
                          currentPage: { type: "integer" },
                          limit: { type: "integer" },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        400: { description: "Invalid user ID." },
        500: { description: "Internal server error." },
      },
    },
  },
  "/v1/api/user/profile/{creatorId}/view": {
    post: {
      summary: "Add Profile View",
      description:
        "Adds a profile view if the current user hasn't already viewed the specified creator's profile.",
      tags: ["User"],
      parameters: [
        {
          name: "creatorId",
          in: "path",
          required: true,
          description: "ID of the creator whose profile is being viewed.",
          schema: { type: "string" },
        },
      ],
      responses: {
        200: {
          description: "Profile view added or already recorded.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { type: "string", nullable: true },
                },
              },
            },
          },
        },
        400: {
          description:
            "Invalid operation. For example, a user cannot view their own profile.",
        },
        404: {
          description: "Creator not found.",
        },
        500: {
          description: "Internal server error.",
        },
      },
    },
  },
  "/v1/api/user/profile/{creatorId}/viewers": {
    get: {
      summary: "Get Profile Viewers",
      description:
        "Fetches a paginated list of users who have viewed the specified creator's profile, including viewer metadata.",
      tags: ["User"],
      parameters: [
        {
          name: "creatorId",
          in: "path",
          required: true,
          description:
            "ID of the creator whose profile viewers you want to fetch.",
          schema: { type: "string" },
        },
        {
          name: "page",
          in: "query",
          required: false,
          description: "Page number for pagination (default: 1).",
          schema: { type: "integer", default: 1 },
        },
        {
          name: "limit",
          in: "query",
          required: false,
          description: "Number of results per page (default: 10).",
          schema: { type: "integer", default: 10 },
        },
      ],
      responses: {
        200: {
          description: "Viewers fetched successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "object",
                    properties: {
                      total: { type: "integer" },
                      page: { type: "integer" },
                      totalPages: { type: "integer" },
                      data: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            viewerId: { type: "string" },
                            username: { type: "string" },
                            displayName: { type: "string" },
                            profilePicture: { type: "string" },
                            viewedAt: { type: "string", format: "date-time" },
                            isFollowing: { type: "boolean" },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        404: {
          description: "User not found.",
        },
        500: {
          description: "Internal server error. Please try again later.",
        },
      },
    },
  },
  "/v1/api/user/activity/saved-items": {
    get: {
      summary: "Get Saved Items by Type",
      description: "Returns a paginated list of saved streams or shorts.",
      tags: ["User Activity"],
      parameters: [
        {
          name: "type",
          in: "query",
          required: true,
          schema: {
            type: "string",
            enum: ["stream", "short"],
          },
          description: "Content type to fetch (stream or short).",
        },
        {
          name: "page",
          in: "query",
          required: false,
          schema: { type: "integer", default: 1 },
        },
        {
          name: "limit",
          in: "query",
          required: false,
          schema: { type: "integer", default: 10 },
        },
      ],
      responses: {
        200: {
          description: "Saved items fetched successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "array",
                    items: { type: "object" },
                  },
                },
              },
            },
          },
        },
        400: { description: "Invalid type value." },
        500: { description: "Internal server error." },
      },
    },
  },
  // "/v1/api/user/activity/saved-items/all": {
  //   get: {
  //     summary: "Get All Saved Items",
  //     description:
  //       "Returns a combined, paginated list of all saved streams and shorts sorted by savedAt.",
  //     tags: ["User Activity"],
  //     parameters: [
  //       {
  //         name: "page",
  //         in: "query",
  //         required: false,
  //         schema: { type: "integer", default: 1 },
  //       },
  //       {
  //         name: "limit",
  //         in: "query",
  //         required: false,
  //         schema: { type: "integer", default: 10 },
  //       },
  //     ],
  //     responses: {
  //       200: {
  //         description: "Combined saved items fetched successfully.",
  //         content: {
  //           "application/json": {
  //             schema: {
  //               type: "object",
  //               properties: {
  //                 success: { type: "boolean" },
  //                 message: { type: "string" },
  //                 data: {
  //                   type: "object",
  //                   properties: {
  //                     total: { type: "integer" },
  //                     page: { type: "integer" },
  //                     limit: { type: "integer" },
  //                     items: {
  //                       type: "array",
  //                       items: {
  //                         type: "object",
  //                         properties: {
  //                           type: { type: "string", enum: ["stream", "short"] },
  //                           savedAt: { type: "string", format: "date-time" },
  //                           itemDetails: { type: "object" },
  //                         },
  //                       },
  //                     },
  //                   },
  //                 },
  //               },
  //             },
  //           },
  //         },
  //       },
  //       500: { description: "Internal server error." },
  //     },
  //   },
  // },
  "/v1/api/user/activity/save-item": {
    post: {
      summary: "Save a Stream or Short",
      description: "Saves a stream or short for the current user.",
      tags: ["User Activity"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                itemId: {
                  type: "string",
                  pattern: "^[0-9a-fA-F]{24}$",
                },
                itemType: {
                  type: "string",
                  enum: ["stream", "short"],
                },
              },
              required: ["itemId", "itemType"],
            },
          },
        },
      },
      responses: {
        200: {
          description: "Item saved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                },
              },
            },
          },
        },
        "400": {
          description: "Invalid input.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  error: { type: "string" },
                },
              },
            },
          },
        },
        "500": {
          description: "Internal server error. Please try again later.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/user/activity/unsave-item": {
    post: {
      summary: "Unsave a Stream or Short",
      description:
        "Removes a previously saved stream or short for the current user.",
      tags: ["User Activity"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                itemId: {
                  type: "string",
                  pattern: "^[0-9a-fA-F]{24}$",
                },
                itemType: {
                  type: "string",
                  enum: ["stream", "short"],
                },
              },
              required: ["itemId", "itemType"],
            },
          },
        },
      },
      responses: {
        200: {
          description: "Item unsaved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                },
              },
            },
          },
        },
        400: { description: "Invalid input." },
        500: { description: "Internal server error." },
      },
    },
  },
};

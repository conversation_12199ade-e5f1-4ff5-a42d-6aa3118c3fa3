export const searchPaths = {
  "/v1/api/search": {
    get: {
      tags: ["Search"],
      summary: "Search for users and streams",
      description:
        "Searches for users and streams based on the provided keyword.",
      parameters: [
        {
          name: "keyword",
          in: "query",
          required: true,
          schema: {
            type: "string",
            example: "example",
          },
          description: "The keyword to search for.",
        },
      ],
      responses: {
        "200": {
          description: "Successful search",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  users: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        username: {
                          type: "string",
                        },
                        fullName: {
                          type: "string",
                        },
                        profilePicture: {
                          type: "string",
                        },
                      },
                    },
                  },
                  streams: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        title: {
                          type: "string",
                        },
                        description: {
                          type: "string",
                        },
                        cover: {
                          type: "string",
                        },
                        tags: {
                          type: "array",
                          items: {
                            type: "string",
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/search/trending": {
    get: {
      tags: ["Search"],
      summary: "Get trending searches",
      description: "Fetches the top 5 trending search keywords.",
      responses: {
        "200": {
          description: "Successful retrieval of trending searches",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  keywords: {
                    type: "array",
                    items: {
                      type: "string",
                    },
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};

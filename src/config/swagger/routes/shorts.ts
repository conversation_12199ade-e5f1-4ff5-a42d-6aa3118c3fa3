export const shortsPaths = {
  "/v1/api/shorts/short-feed": {
    get: {
      summary: "Get Shorts Feed",
      description:
        "Fetch the latest shorts feed sorted by interaction and time.",
      tags: ["Shorts"],
      responses: {
        "200": {
          description: "Shorts feed retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "array",
                    items: { $ref: "#/components/schemas/Short" },
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Error fetching shorts feed.",
        },
      },
    },
  },

  "/v1/api/shorts/{id}": {
    get: {
      summary: "Get Short By ID",
      description: "Retrieve a single short by its ID.",
      tags: ["Shorts"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: { type: "string" },
        },
      ],
      responses: {
        "200": {
          description: "Short retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { $ref: "#/components/schemas/Short" },
                },
              },
            },
          },
        },
        "404": {
          description: "Short not found.",
        },
        "500": {
          description: "Error fetching short.",
        },
      },
    },

    delete: {
      summary: "Delete Short",
      description: "Delete a short by its ID. Only the author can delete.",
      tags: ["Shorts"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: { type: "string" },
        },
      ],
      responses: {
        "200": {
          description: "Short deleted successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { $ref: "#/components/schemas/Short" },
                },
              },
            },
          },
        },
        "401": {
          description: "Unauthorized to delete this short.",
        },
        "404": {
          description: "Short not found.",
        },
        "500": {
          description: "Error deleting short.",
        },
      },
    },
  },

  "/v1/api/shorts/author/{authorId}": {
    get: {
      summary: "Get Shorts by Author",
      description: "Retrieve all shorts created by a specific author.",
      tags: ["Shorts"],
      parameters: [
        {
          name: "authorId",
          in: "path",
          required: true,
          schema: { type: "string" },
        },
        {
          name: "page",
          in: "query",
          required: false,
          schema: { type: "integer", default: 1 },
        },
        {
          name: "limit",
          in: "query",
          required: false,
          schema: { type: "integer", default: 10 },
        },
      ],
      responses: {
        "200": {
          description: "Shorts retrieved successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: {
                    type: "object",
                    properties: {
                      pagination: {
                        type: "object",
                        properties: {
                          page: { type: "integer" },
                          limit: { type: "integer" },
                          totalItems: { type: "integer" },
                          totalPages: { type: "integer" },
                          hasNextPage: { type: "boolean" },
                          hasPreviousPage: { type: "boolean" },
                        },
                      },
                      data: {
                        type: "array",
                        items: { $ref: "#/components/schemas/Short" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Error fetching shorts by author.",
        },
      },
    },
  },

  "/v1/api/shorts/create-short": {
    post: {
      summary: "Create a Short",
      description: "Create and upload a new short.",
      tags: ["Shorts"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                videoUrl: { type: "string" },
                description: { type: "string" },
                thumbnailUrl: { type: "string" },
              },
              required: ["videoUrl"],
            },
          },
        },
      },
      responses: {
        "201": {
          description: "Short created successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  message: { type: "string" },
                  data: { $ref: "#/components/schemas/Short" },
                },
              },
            },
          },
        },
        "500": {
          description: "Error creating short.",
        },
      },
    },
  },
};

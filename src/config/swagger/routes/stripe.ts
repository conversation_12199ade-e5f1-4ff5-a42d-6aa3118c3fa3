export const stripePaths = {
  "/v1/api/stripe/plan/platform": {
    post: {
      tags: ["Stripe"],
      summary: "Create platform subscription plans",
      description: "Creates predefined subscription plans for the platform.",
      responses: {
        "201": {
          description: "Platform subscription plans created successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                    example:
                      "Platform subscription plans created successfully.",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/stripe/plan/user": {
    post: {
      tags: ["Stripe"],
      summary: "Create a new subscription plan",
      description: "Creates a new subscription plan for a specific user.",
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                },
                description: {
                  type: "string",
                },
                billingInterval: {
                  type: "string",
                },
                price: {
                  type: "number",
                },
                currency: {
                  type: "string",
                },
                features: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                },
              },
              required: [
                "title",
                "description",
                "billingInterval",
                "price",
                "currency",
                "features",
              ],
            },
          },
        },
      },
      responses: {
        "201": {
          description: "Subscription plan created successfully",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/SubscriptionPlan",
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/stripe/plan": {
    get: {
      tags: ["Stripe"],
      summary: "Get subscription plans",
      description:
        "Fetches all subscription plans or those created by a specific user.",
      parameters: [
        {
          name: "creator",
          in: "query",
          schema: {
            type: "string",
          },
          description: "Filter plans by creator ID.",
        },
      ],
      responses: {
        "200": {
          description: "Subscription plans retrieved successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  plans: {
                    type: "array",
                    items: {
                      $ref: "#/components/schemas/SubscriptionPlan",
                    },
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/stripe/manage": {
    get: {
      tags: ["Stripe"],
      summary: "Manage subscription",
      description: "Fetches the billing portal URL for managing subscriptions.",
      responses: {
        "200": {
          description: "Billing portal URL retrieved successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  url: {
                    type: "string",
                    format: "uri",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/stripe/subscribe/platform": {
    post: {
      tags: ["Stripe"],
      summary: "Subscribe to a platform plan",
      description:
        "Initiates a subscription to a platform plan using Stripe Checkout.",
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                planId: {
                  type: "string",
                },
              },
              required: ["planId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Subscription session created successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: {
                    type: "string",
                  },
                  url: {
                    type: "string",
                    format: "uri",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/stripe/subscribe/user": {
    post: {
      tags: ["Stripe"],
      summary: "Subscribe to a user plan",
      description:
        "Initiates a subscription to a user-specific plan using Stripe Checkout.",
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                planId: {
                  type: "string",
                },
              },
              required: ["planId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Subscription session created successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: {
                    type: "string",
                  },
                  url: {
                    type: "string",
                    format: "uri",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/v1/api/stripe/onboard": {
    get: {
      tags: ["Stripe"],
      summary: "Onboard a customer",
      description: "Creates an account link for onboarding a Stripe customer.",
      responses: {
        "200": {
          description: "Onboarding link created successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  url: {
                    type: "string",
                    format: "uri",
                  },
                },
              },
            },
          },
        },
        "500": {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};

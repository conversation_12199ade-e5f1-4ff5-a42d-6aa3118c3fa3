export const streamPaths = {
  "/v1/api/stream": {
    get: {
      summary: "Fetch all streams",
      operationId: "fetchStreams",
      tags: ["Stream"],
      parameters: [
        {
          name: "status",
          in: "query",
          required: false,
          schema: {
            type: "string",
            enum: ["uploading", "draft", "published", "ended", "uploaded"],
          },
          description: "Filter streams by status",
        },
        {
          name: "type",
          in: "query",
          required: false,
          schema: {
            type: "string",
            enum: ["vr", "video", "video-live"],
          },
          description: "Filter streams by type",
        },
        {
          name: "page",
          in: "query",
          required: false,
          schema: {
            type: "integer",
            default: 1,
          },
          description: "Page number for pagination",
        },
        {
          name: "limit",
          in: "query",
          required: false,
          schema: {
            type: "integer",
            default: 10,
          },
          description: "Number of streams per page",
        },
      ],
      responses: {
        "200": {
          description: "List of streams",
          content: {
            "application/json": {
              schema: {
                type: "array",
                items: {
                  $ref: "#/components/schemas/Stream",
                },
              },
            },
          },
        },
        "500": {
          description: "Error fetching streams",
        },
      },
    },
    post: {
      summary: "Create a new stream",
      operationId: "createStream",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "multipart/form-data": {
            schema: {
              type: "object",
              properties: {
                file: {
                  type: "string",
                  format: "binary",
                  description: "Stream file to be uploaded",
                },
                type: {
                  type: "string",
                  enum: ["vr", "video", "video-live"],
                  description: "Type of the stream",
                },
                status: {
                  type: "string",
                  enum: [
                    "uploading",
                    "draft",
                    "published",
                    "ended",
                    "uploaded",
                  ],
                  description: "Status of the stream",
                },
                url: {
                  type: "string",
                  description: "video URL of the stream",
                },
                visibility: {
                  type: "string",
                  enum: ["everyone", "followers", "subscribers", "none"],
                  description: "Visibility settings of the stream",
                },
                title: {
                  type: "string",
                  description: "Title of the stream",
                },
                description: {
                  type: "string",
                  description: "Description of the stream",
                },
                category: {
                  type: "string",
                  description: "Category of the stream",
                },
                tags: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  description: "Tags for the stream",
                },
                cover: {
                  type: "string",
                  description: "Cover image URL",
                },
              },
            },
          },
        },
      },
      responses: {
        "201": {
          description: "Stream created successfully",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Stream",
              },
            },
          },
        },
        "400": {
          description: "Bad request - Missing required fields",
        },
        "500": {
          description: "Error creating stream",
        },
      },
    },
  },
  "/v1/api/stream/{id}": {
    get: {
      summary: "Get stream details",
      operationId: "getStreamDetails",
      tags: ["Stream"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: {
            type: "string",
          },
          description: "Stream ID",
        },
      ],
      responses: {
        "200": {
          description: "Stream details",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Stream",
              },
            },
          },
        },
        "404": {
          description: "Stream not found",
        },
        "500": {
          description: "Error fetching stream details",
        },
      },
    },
    put: {
      summary: "Update stream settings",
      operationId: "updateStreamSettings",
      tags: ["Stream"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: {
            type: "string",
          },
          description: "Stream ID",
        },
      ],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              $ref: "#/components/schemas/UpdateStreamSettings",
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Stream updated successfully",
        },
        "401": {
          description: "Unauthorized",
        },
        "404": {
          description: "Stream not found",
        },
        "500": {
          description: "Error updating stream",
        },
      },
    },
    delete: {
      summary: "Delete a stream",
      operationId: "deleteStream",
      tags: ["Stream"],
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          schema: {
            type: "string",
          },
          description: "Stream ID",
        },
      ],
      responses: {
        "200": {
          description: "Stream deleted successfully",
        },
        "401": {
          description: "Unauthorized",
        },
        "404": {
          description: "Stream not found",
        },
        "500": {
          description: "Error deleting stream",
        },
      },
    },
  },

  "/v1/api/stream/presigned-url": {
    get: {
      summary: "Generate pre-signed URL",
      operationId: "generatePreSignedUrl",
      tags: ["Stream"],
      responses: {
        "200": {
          description: "Pre-signed URL generated successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  url: {
                    type: "string",
                    description: "The generated pre-signed URL",
                  },
                },
              },
            },
          },
        },
        "401": {
          description: "Unauthorized",
        },
        "500": {
          description: "Error generating pre-signed URL",
        },
      },
    },
  },

  "/v1/api/stream/presigned-url/upload": {
    put: {
      summary: "Upload file to pre-signed URL (TEST)",
      operationId: "uploadToPreSignedUrl",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "multipart/form-data": {
            schema: {
              type: "object",
              properties: {
                file: {
                  type: "string",
                  format: "binary",
                  description: "The MP4 video file to upload",
                },
                presignedUrl: {
                  type: "string",
                  description: "Pre-signed URL for uploading the file",
                },
              },
              required: ["file", "presignedUrl"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "File uploaded successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                    example: "uploaded successfully",
                  },
                },
              },
            },
          },
        },
        "400": {
          description: "Bad request - File missing or invalid",
        },
        "403": {
          description: "Unauthorized - Presigned URL may have expired",
        },
        "500": {
          description: "Error uploading file",
        },
      },
    },
  },

  "/v1/api/stream/cover": {
    post: {
      summary: "Upload cover image",
      operationId: "uploadCover",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "multipart/form-data": {
            schema: {
              type: "object",
              properties: {
                file: {
                  type: "string",
                  format: "binary",
                  description: "Cover image file to be uploaded",
                },
              },
              required: ["file"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Cover image uploaded successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: true,
                  },
                  imageUrl: {
                    type: "string",
                    description: "Public URL of the uploaded cover image",
                  },
                },
              },
            },
          },
        },
        "400": {
          description: "No file uploaded",
        },
        "401": {
          description: "Unauthorized",
        },
        "500": {
          description: "Error uploading cover image",
        },
      },
    },
  },

  "/v1/api/stream/charge": {
    post: {
      summary: "Charge for viewing time",
      operationId: "chargeForViewingTime",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                userId: {
                  type: "string",
                  description: "User ID being charged",
                },
                streamId: {
                  type: "string",
                  description: "ID of the stream being viewed",
                },
              },
              required: ["userId", "streamId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Charge successful",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                    example: "Charge successful",
                  },
                  paymentIntentId: {
                    type: "string",
                    description: "ID of the Stripe payment intent",
                  },
                },
              },
            },
          },
        },
        "404": {
          description: "Viewing record not found",
        },
        "500": {
          description: "Error charging for viewing time",
        },
      },
    },
  },
  "/v1/api/stream/comments": {
    post: {
      summary: "Fetch all the comments for a stream",
      operationId: "fetchCommentsForStream",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                streamId: {
                  type: "string",
                  description: "ID of the stream being viewed",
                },
              },
              required: ["streamId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Comment list fetched successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: true,
                    description: "Indicates if the request was successful",
                  },
                  message: {
                    type: "string",
                    example: "Comment list fetched successfully",
                  },
                  comments: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        _id: {
                          type: "string",
                          description: "Unique ID of the comment",
                        },
                        user: {
                          type: "object",
                          properties: {
                            _id: {
                              type: "string",
                              description: "User ID",
                            },
                            username: {
                              type: "string",
                              description: "User's username",
                            },
                            displayName: {
                              type: "string",
                              description: "User's display name",
                            },
                            profilePicture: {
                              type: "string",
                              format: "url",
                              description: "URL to the user's profile picture",
                            },
                          },
                        },
                        stream: {
                          type: "string",
                          description: "Stream ID the comment belongs to",
                        },
                        content: {
                          type: "string",
                          description: "Content of the comment",
                        },
                        replies: {
                          type: "array",
                          items: {
                            type: "object",
                            description: "Replies to the comment (if any)",
                          },
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          description: "Timestamp of comment creation",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                          description: "Timestamp of last comment update",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "404": {
          description: "Stream not found",
        },
        "500": {
          description: "Server error occurred",
        },
      },
    },
  },
  "/v1/api/stream/share-stream": {
    post: {
      summary: "Share stream with other people",
      operationId: "shareStream",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                streamId: {
                  type: "string",
                  description: "ID of the stream need to be shared",
                },
                platform: {
                  type: "string",
                  description: "Whatsapp, facebook etc",
                },
              },
              required: ["streamId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Stream shared successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: true,
                    description: "Indicates if the request was successful",
                  },
                  message: {
                    type: "string",
                    example: "Stream shared successfully",
                  },
                },
              },
            },
          },
        },
        "404": {
          description: "Stream not found",
        },
        "500": {
          description: "Server error occurred",
        },
      },
    },
  },
  "/v1/api/stream/live/initiate": {
    post: {
      summary: "User initializes it's live stream",
      operationId: "initiateLiveStream",
      tags: ["Stream"],
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  description: "Ttile of stream",
                },
                description: {
                  type: "string",
                  description: "Latest tech news",
                },
                category: {
                  type: "string",
                  description: "Gaming",
                },
                tags: {
                  type: "string",
                  description: "live",
                },
                cover: {
                  type: "string",
                  description: "https://example.com/image.jpg",
                },
              },
              required: ["streamId"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Stream initiated",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: true,
                    description: "Indicates if the request was successful",
                  },
                  message: {
                    type: "string",
                    example: "Comment list fetched successfully",
                  },

                  data: {
                    type: "object",
                    properties: {
                      rtmpUrl: {
                        type: "string",
                        description: "rtmp://************:1935/src",
                      },
                      streamKey: {
                        type: "string",
                        description:
                          "6777ecdcd4e14c0a4e2f94e0_677aa7e0a7744e2642c7c3b6",
                      },
                      playBackUrl: {
                        type: "string",
                        format: "url",
                        description: "URL to the watch stream",
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "404": {
          description: "Stream not found",
        },
        "500": {
          description: "Server error occurred",
        },
      },
    },
  },
};

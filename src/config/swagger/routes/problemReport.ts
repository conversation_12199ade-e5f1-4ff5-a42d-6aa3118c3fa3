export const reportPaths = {
  "/v1/api/report-problem": {
    post: {
      summary: "Report a Problem",
      description:
        "Allows users to report a problem with an optional image upload.",
      tags: ["Report"],
      requestBody: {
        content: {
          "multipart/form-data": {
            schema: {
              type: "object",
              properties: {
                category: {
                  type: "string",
                  description: "Category of the problem being reported",
                },
                message: {
                  type: "string",
                  description: "Description of the problem",
                },
                files: {
                  type: "array",
                  items: {
                    type: "string",
                    format: "binary",
                    description: "Image files related to the problem report",
                  },
                },
              },
              required: ["category", "message"],
            },
          },
        },
      },
      responses: {
        "200": {
          description: "Problem reported successfully.",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                  },
                  report: {
                    type: "object",
                    description: "Details of the problem report",
                    additionalProperties: true,
                  },
                },
              },
            },
          },
        },
        "401": {
          description: "Unauthorized. User must be logged in.",
        },
        "500": {
          description: "Server error while reporting the problem.",
        },
      },
    },
  },
};

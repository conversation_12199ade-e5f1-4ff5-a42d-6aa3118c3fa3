/**
 * like stream socket emit
 */
export const LIKE_EMITS = Object.freeze({
  SUCCESS: "Operation successful.",
  INVALID_INPUT: "Invalid input provided.",
  NOT_FOUND: "The requested resource was not found.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
  LIKE_EMIT: "likeStream",
  LIKE_RESPONSE_EMIT: "likedStream",
  LIKE_ACTION: "like",
  UNLIKE_ACTION: "unlike",
  ERROR: "error",
  NOTIFICATION_ERROR: "Error sending notification",
} as const);

export type LikeEmit = keyof typeof LIKE_EMITS;

/**
 * Comment emits
 */
export const COMMENT_EMITS = Object.freeze({
  ADD_COMMENT: "commentStream",
  ADD_COMMENT_RESPONSE_EMIT: "commentedStream",
  DELETE_COMMENT: "deleteComment",
  DELETED_COMMENT: "deletedComment",
  ERROR: "error",
  NOTIFICATION_ERROR: "Error sending notification",
} as const);

export type CommentEmit = keyof typeof COMMENT_EMITS;

/**
 * Response Comment messages
 */
export const COMMENT_MESSAGES = Object.freeze({
  SUCCESS: "Comment added successfully.",
  FETCH_COMMENTS: "Comment list fetched successfully",
  INVALID_INPUT: "Invalid streamId provided.",
  NOT_FOUND: "The requested stream was not found.",
  COMMENT_NOT_FOUND: "Comment not found",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type CommentMessageKey = keyof typeof COMMENT_MESSAGES;

/**
 * like stream socket emit
 * follow action is for follow and unfollow
 * follow response s the response emit.
 * follow update will be for updating the follow count for targeted user
 * And finally error
 */
export const FOLLOW_EMITS = Object.freeze({
  FOLLOW_ACTION: "followAction",
  FOLLOW_RESPONSE: "followResponse",
  FOLLOW_UPDATE: "followUpdate",
  ERROR: "error",
} as const);

export type FollowEmits = keyof typeof FOLLOW_EMITS;

/**
 * Follow constant message
 */
export const FOLLOW_MESSAGES = Object.freeze({
  SUCCESS: "Operation successful.",
  INVALID_INPUT: "Invalid input provided.",
  INVALID_ACTION: "You cannot follow/unfollow yourself.",
  NOT_FOUND: "The requested resource was not found.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
  BAD_REQUEST: "Invalid request or missing parameters.",
  ERROR: "error",
  NOTIFICATION_ERROR: "Error sending notification",
  SERVER_ERROR: "An error occurred on the server.",
} as const);

export type FollowMessage = keyof typeof FOLLOW_MESSAGES;

/**
 * View emits
 */
export const VIEW_EMITS = Object.freeze({
  ERROR: "viewError",
  VIEW_UPDATED: "viewUpdated",
  VIEW_STREAM: "viewStream",
} as const);

export type ViewEmits = keyof typeof VIEW_EMITS;

export const VIEW_MESSAGES = Object.freeze({
  INVALID_STREAM_ID: "Invalid stream ID",
  STREAM_NOT_FOUND: "Stream not found",
  VIEW_UPDATE_ERROR: "Error updating views",
} as const);

export type ViewMessage = keyof typeof VIEW_MESSAGES;

/**
 * Socket messages
 */
export const SOCKET_EMIT_MESSAGES = Object.freeze({
  SUCCESS: "Operation successful.",
  INVALID_INPUT: "Invalid input provided.",
  NOT_FOUND: "The requested resource was not found.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
  BAD_REQUEST: "Invalid request or missing parameters.",
  ERROR: "error",
  NOTIFICATION_ERROR: "Error sending notification",
  SERVER_ERROR: "An error occurred on the server.",
} as const);

export type SocketEmitMessage = keyof typeof SOCKET_EMIT_MESSAGES;

/**
 * emits for Join, leave and get views for live stream
 */

export const LIVE_STREAM_VIEWS = Object.freeze({
  VIEW: "viewLiveStream",
  LEAVE: "leaveLiveStream",
  GET_VIEWS: "getViews",
});

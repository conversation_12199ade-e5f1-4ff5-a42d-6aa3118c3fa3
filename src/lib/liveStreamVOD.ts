import {
  DeleteMessageCommand,
  ReceiveMessageCommand,
  SQSClient,
} from "@aws-sdk/client-sqs";

import {
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  AWS_ECS_CLUSTER_URL,
  AWS_ECS_TASK_CONTAINER_NAME,
  AWS_ECS_TASK_DEFINITION,
  AWS_S3_SQS_URL,
  AWS_S3_TRANSCODED_BUCKET_NAME,
} from "../config/environment";

import type { S3Event } from "aws-lambda";
import { ECSClient, RunTaskCommand } from "@aws-sdk/client-ecs";
import { ecsClient, sqsClient } from "../config/s3";
import Stream from "../models/stream/stream.schema";

/**
 * Initializes the AWS SQS Listener for processing uploaded VOD files.
 * The function continuously polls the SQS queue for new video upload events.
 * When a message is received, it triggers an AWS Fargate task to transcode the video into HLS.
 */
async function initializeVODProcessor() {
  const command = new ReceiveMessageCommand({
    QueueUrl: AWS_S3_SQS_URL, // SQS Queue URL where S3 sends new file notifications
    MaxNumberOfMessages: 1, // Process one message at a time for efficiency
    WaitTimeSeconds: 20, // Long polling to reduce API calls
  });

  while (true) {
    try {
      // 🔹 Receive messages from the SQS queue
      const { Messages } = await sqsClient.send(command);

      if (!Messages) {
        console.log("No messages in the queue, waiting...");
        continue;
      }

      for (const message of Messages) {
        const { MessageId, Body, ReceiptHandle } = message;
        console.log("📩 Message Received", { MessageId, Body });

        if (!Body) continue;

        const event = JSON.parse(Body) as S3Event;

        //Ignore test events sent by AWS
        if ("Service" in event && "Event" in event) {
          if (event.Event === "s3:TestEvent") {
            await sqsClient.send(
              new DeleteMessageCommand({
                QueueUrl: AWS_S3_SQS_URL,
                ReceiptHandle: ReceiptHandle!,
              })
            );
            continue;
          }
        }

        for (const record of event.Records) {
          const { s3 } = record;
          const { bucket, object } = s3;
          const { key } = object;

          // Extract user ID & video ID from the S3 key (e.g., "vod/userId/videoId.mp4")
          const keyParts = key.split("/");
          const userId = keyParts[1];
          const videoId = keyParts[2].replace(".mp4", "");

          console.log(`🔹 User ID: ${userId}, Video ID: ${videoId}`);

          // **Trigger Fargate Task for Video Transcoding**
          const runTaskCommand = new RunTaskCommand({
            taskDefinition: AWS_ECS_TASK_DEFINITION,
            cluster: AWS_ECS_CLUSTER_URL,
            launchType: "FARGATE",
            networkConfiguration: {
              awsvpcConfiguration: {
                subnets: [
                  "subnet-0685e54f8177c544c",
                  "subnet-0ef36fafbb5a0e34d",
                  "subnet-036aeb50a68803220",
                ],
                securityGroups: ["sg-0f6ad52945ba19e3d"],
                assignPublicIp: "ENABLED",
              },
            },
            overrides: {
              containerOverrides: [
                {
                  name: AWS_ECS_TASK_CONTAINER_NAME,
                  environment: [
                    { name: "BUCKET_NAME", value: bucket.name },
                    { name: "KEY", value: key },
                    { name: "USER", value: userId },
                    { name: "FOLDER_NAME", value: videoId },
                  ],
                },
              ],
            },
          });

          console.log(`Triggering Fargate task for transcoding ${videoId}.mp4`);
          await ecsClient.send(runTaskCommand);

          await sqsClient.send(
            new DeleteMessageCommand({
              QueueUrl: AWS_S3_SQS_URL,
              ReceiptHandle: ReceiptHandle!,
            })
          );

          const masterKey = `transcoded/${userId}/${videoId}/master.m3u8`;

          await Stream.findOneAndUpdate(
            {
              creator: userId,
              url: `https://s3.ap-south-1.amazonaws.com/${bucket.name}/${key}`,
            },
            {
              $set: {
                transcodedUrl: `https://s3.ap-south-1.amazonaws.com/${AWS_S3_TRANSCODED_BUCKET_NAME}/${masterKey}`,
                status: "uploaded",
              },
            }
          );

          console.log(`Stream ${videoId} successfully queued for processing.`);
        }
      }
    } catch (err) {
      console.error("Error in SQS listener:", err);
    }
  }
}

export default initializeVODProcessor;

import os from "os";
import fs from "fs";
import path from "path";
import { spawn } from "child_process";

import { generateFFMPEGArgs, generateMasterPlaylistFile } from "../config/hls";
import { getPublicUrlFromS3, uploadFileToS3 } from "./s3";

import { resolutions } from "../constants/hls";
import logger from "../config/logger";

export const uploadHLSFilesToS3 = async (
  filesDirectoryPath: string,
  outputPath: string,
  bucketName: string
) => {
  const files = fs.readdirSync(filesDirectoryPath);

  for (const file of files) {
    const filePath = path.join(filesDirectoryPath, file);

    if (file.startsWith("playlist_") || file.split("_").includes("segment")) {
      const key = `${outputPath}${file}`;
      const fileContent = fs.createReadStream(filePath);
      const contentType = file.endsWith(".m3u8")
        ? "application/vnd.apple.mpegurl"
        : "video/MP2T";

      await uploadFileToS3(fileContent, bucketName, key, contentType);

      logger.info(`Uploaded file to S3: ${outputPath}${file}`);
    }
  }
};

export const uploadHLSStreamToS3 = async (
  inputFile: Express.Multer.File,
  bucketName: string,
  s3Folder: string,
  user: string
): Promise<string> => {
  const tempDir = path.join(os.tmpdir(), "hls");
  const outputPath = `${s3Folder}/${user}/`;
  const masterPlaylistPath = path.join(tempDir, "master.m3u8");

  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  const variantPlaylists: any = [];

  for (const { resolution, videoBitrate, audioBitrate } of resolutions) {
    const variantPlaylistPath = path.join(
      tempDir,
      `playlist_${resolution}.m3u8`
    );
    const segmentFilename = `${resolution}_segment_%03d.ts`;
    const segmentFilePath = path.join(tempDir, segmentFilename);

    const ffmpegArgs = generateFFMPEGArgs(
      videoBitrate,
      audioBitrate,
      resolution,
      segmentFilePath,
      variantPlaylistPath
    );

    await new Promise<void>((resolve, reject) => {
      const ffmpeg = spawn("ffmpeg", ffmpegArgs);

      ffmpeg.stdin.write(inputFile.buffer);
      ffmpeg.stdin.end();

      ffmpeg.stderr.on("data", (data) => {
        console.error("ffmpeg stderr:", data.toString());
      });

      ffmpeg.on("close", (code) => {
        if (code !== 0) {
          return reject(new Error(`ffmpeg process exited with code ${code}`));
        }

        variantPlaylists.push({
          resolution,
          outputFileName: `playlist_${resolution}.m3u8`,
        });

        resolve();
      });
    });
  }

  const masterPlaylist = generateMasterPlaylistFile(variantPlaylists);
  fs.writeFileSync(masterPlaylistPath, masterPlaylist);

  await uploadHLSFilesToS3(tempDir, outputPath, bucketName);

  const masterKey = `${outputPath}master.m3u8`;
  const masterFileContent = fs.createReadStream(masterPlaylistPath);
  const masterContentType = "application/vnd.apple.mpegurl";

  await uploadFileToS3(
    masterFileContent,
    bucketName,
    masterKey,
    masterContentType
  );

  logger.info(`Uploaded master playlist to S3: ${outputPath}master.m3u8`);

  const url = getPublicUrlFromS3(bucketName, masterKey);

  fs.readdir(tempDir, (err, files) => {
    if (err) {
      console.error("Error reading temp directory:", err);
      return;
    }
    files.forEach((file) => {
      fs.unlink(path.join(tempDir, file), () => {});
    });
  });

  return url;
};

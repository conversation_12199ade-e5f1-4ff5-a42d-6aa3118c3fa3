import {
  SQSClient,
  ReceiveMessageCommand,
  DeleteMessageCommand,
} from "@aws-sdk/client-sqs";
import Stream from "../models/stream/stream.schema";

const sqsClient = new SQSClient({ region: "ap-south-1" });

async function listenForTranscodedMessages() {
  console.log("🚀 Listening for Transcoded Video Messages...");

  const command = new ReceiveMessageCommand({
    QueueUrl: process.env.AWS_VIDEO_TRANSCODED_QUEUE, // ✅ Get queue URL from env
    MaxNumberOfMessages: 1,
    WaitTimeSeconds: 20,
  });

  while (true) {
    try {
      const { Messages } = await sqsClient.send(command);

      if (!Messages) {
        console.log("No messages in Transcoding queue...");
        continue;
      }

      for (const message of Messages) {
        const { Body, ReceiptHandle } = message;
        if (!Body) continue;

        const event = JSON.parse(Body);
        if (event.status === "completed") {
          const { streamKey, transcodedUrl } = event;

          console.log(`✅ Updating MongoDB for streamKey: ${streamKey}`);

          await Stream.findOneAndUpdate(
            { streamKey },
            { $set: { transcodedUrl, vodStatus: "ready" } }
          );

          console.log(`🎬 VOD for ${streamKey} is ready!`);

          // ✅ Delete processed message
          await sqsClient.send(
            new DeleteMessageCommand({
              QueueUrl: process.env.AWS_S3_TRANSCODED_SQS_URL,
              ReceiptHandle,
            })
          );
        }
      }
    } catch (err) {
      console.error("❌ Error in SQS Listener:", err);
    }
  }
}

export default listenForTranscodedMessages;

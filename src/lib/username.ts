import { UserService } from "../services";
import { v4 as uuidv4 } from "uuid";

export const generateBaseUsername = (input: string): string => {
  return input
    .split("@")[0]
    .replace(/[^a-zA-Z0-9]/g, "")
    .toLowerCase();
};

const checkUsernameExists = async (username: string): Promise<boolean> => {
  const existingUser = await UserService.getUserByUsername(username);
  return !!existingUser;
};

export const generateUniqueUsername = async (
  baseUsername: string
): Promise<string> => {
  let username = baseUsername;
  let isUnique = !(await checkUsernameExists(username));

  while (!isUnique) {
    const uuidSuffix = uuidv4().split("-")[0];
    username = `${baseUsername}${uuidSuffix}`;
    isUnique = !(await checkUsernameExists(username));
  }

  return username;
};

import cron from "node-cron";
import { updateTrendingScores } from "../services/streamService/trendingStreams.service";

cron.schedule("*/30 * * * *", async () => {
  try {
    console.log("Starting trending score update job...");
    await updateTrendingScores();

    console.log("Trending score update job completed.");
  } catch (error) {
    console.log("Error during trending score update job:", error);
  }
});

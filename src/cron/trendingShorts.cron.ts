import { updateTrendingShortScores } from "../services/shortsService/shorts.service";
import cron from "node-cron";


cron.schedule("*/30 * * * *", async () => {
    try {
        console.log(" Trending Shorts Score Job Started...");
        await updateTrendingShortScores();
        console.log("Trending Shorts Score Job Completed.");
    } catch (error) {
        console.error("Trending Shorts Score Job Failed:", error);
    }
});
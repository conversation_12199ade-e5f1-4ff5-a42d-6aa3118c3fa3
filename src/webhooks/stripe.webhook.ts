import { Request, Response } from "express";
import stripe from "../config/stripe";
import User from "../models/user/user.schema";
import SubscriptionPlan from "../models/plan/plan.schema";
// import { createOrRetrieveStripeAccount } from "../lib/stripe";
import { STRIPE_WEBHOOK_SECRET } from "../config/environment";

export const stripeWebhook = async (req: Request, res: Response) => {
  const sig = req.headers["stripe-signature"] as string;

  let event;
  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      STRIPE_WEBHOOK_SECRET
    );
  } catch (err: any) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  switch (event.type) {
    case "checkout.session.completed":
      {
        const session = event.data.object;
        const customerId = session.customer;
        const planId = session.metadata?.planId;
        const isPlatformPlan = session.metadata?.isPlatformPlan === "true";

        const user = await User.findOne({ stripeCustomerId: customerId });
        if (!user) {
          return res.status(404).json({ error: "User not found" });
        }
        const plan = await SubscriptionPlan.findById(planId);
        if (!plan) {
          return res.status(404).json({ error: "Plan not found" });
        }

        if (isPlatformPlan) {
          user.platformSubscription = {
            plan: plan._id,
            status: "active",
          };
        } else {
          const creator = await User.findById(plan.creator);
          if (!creator) {
            return res
              .status(404)
              .json({ error: "Creator not found or no Stripe account" });
          }

          // const accountId = await createOrRetrieveStripeAccount(creator);

          user.creatorSubscriptions.push({
            plan: plan._id,
            status: "active",
          });

          // try {
          //   const transfer = await stripe.transfers.create({
          //     amount: session.amount_total ?? undefined,
          //     currency: session.currency ?? "USD",
          //     destination: accountId,
          //     transfer_group: `subscription_${planId}`,
          //   });

          //   console.log(`Transfer successful: ${transfer.id}`);
          // } catch (error: any) {
          //   console.error(`Transfer failed: ${error.message}`);
          // }
        }

        await user.save();
      }
      break;

    case "invoice.payment_succeeded":
      {
        const invoice = event.data.object;
        const subscriptionId = invoice.subscription;
        const customerId = invoice.customer;

        const user = await User.findOne({ stripeCustomerId: customerId });
        if (!user) {
          return res.status(404).json({ error: "User not found" });
        }

        if (
          user.platformSubscription &&
          user.platformSubscription.plan.toString() === subscriptionId
        ) {
          user.platformSubscription.status = "active";
        } else {
          const subscription = user.creatorSubscriptions.find(
            (sub) => sub.plan.toString() === subscriptionId
          );
          if (subscription) {
            subscription.status = "active";
          }
        }

        await user.save();
      }
      break;

    case "customer.subscription.deleted":
      {
        const subscription = event.data.object;
        const customerId = subscription.customer;
        const planId = subscription.items.data[0].price.product;

        const user = await User.findOne({ stripeCustomerId: customerId });
        if (!user) {
          return res.status(404).json({ error: "User not found" });
        }

        if (
          user.platformSubscription &&
          user.platformSubscription.plan.toString() === planId
        ) {
          user.platformSubscription.status = "expired";
        } else {
          const subscription = user.creatorSubscriptions.find(
            (sub) => sub.plan.toString() === planId
          );
          if (subscription) {
            subscription.status = "expired";
          }
        }

        await user.save();
      }
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.status(200).json({ received: true });
};

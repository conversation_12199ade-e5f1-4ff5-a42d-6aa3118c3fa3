import { Router } from "express";

import authRouter from "./auth.router";
import userRouter from "./user.router";
import streamRouter from "./stream.router";
import searchRouter from "./search.router";
import stripeRouter from "./stripe.router";
import aboutRouter from "./about.router";
import rtmpAuthRouter from "./rtmp-auth.router";

import notificationRouter from "./notification.router";
import storyRouter from "./story.router";
import shortsRouter from "./shorts.router";
import playlistRouter from "./playlist.router";
import shareRouter from "./share.router";

import notInterestedRouter from "./notInterested.router"
import problemReportRouter from "./problemReport.router";
import reportRouter from "./report.router";
const router = Router();

router.use("/auth", authRouter);
router.use("/users", userRouter);
router.use("/stream", streamRouter);
router.use("/rtmp-auth", rtmpAuthRouter);
router.use("/search", searchRouter);
router.use("/stripe", stripeRouter);
router.use("/about", aboutRouter);
router.use("/notification", notificationRouter);
router.use("/story", storyRouter);
router.use("/shorts", shortsRouter);
router.use("/playlist", playlistRouter);
router.use("/share", shareRouter);
router.use("/not-interested", notInterestedRouter);
router.use("/problem-report", problemReportRouter);
router.use("/report", reportRouter);

export default router;

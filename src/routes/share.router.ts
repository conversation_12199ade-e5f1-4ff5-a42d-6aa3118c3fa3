import { Router } from "express";
import authenticate from "../middlewares/authenticate";
import { validator } from "../middlewares/validator";
import { ShareController } from "../controllers";
import { addShareRecordSchema } from "../validators/share.validator";

const shareRouter = Router();

shareRouter.use(authenticate);
shareRouter.post(
  "/create",
  validator.body(addShareRecordSchema),
  ShareController.httpCreateShareRecord
);

export default shareRouter;

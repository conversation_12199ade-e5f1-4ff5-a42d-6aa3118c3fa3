import { Router } from "express";
import { validator } from "../middlewares/validator";
import {
  AddFeaturedStreamSchema,
  createStreamSchema,
  deleteStreamSchema,
  fetchStreamsSchema,
  getStreamDetailsSchema,
  getStreamUrlsSchema,
  isStreamLiveSchema,
  NotInterestedStreamSchema,
  ReportStreamSchema,
  UpdateReportStreamStatus,
  updateStreamSettingsSchema,
} from "../validators/stream.validator";
import upload from "../middlewares/upload";
import authenticate from "../middlewares/authenticate";
import { checkStreamAccess } from "../middlewares/check-stream-access";
import {
  authenticateEndStream,
  authenticateStartStream,
} from "../middlewares/restrict-ip";

import {
  FeaturedStreamController,
  LiveStreamController,
  TrendingStreamController,
  StreamController,
  NotInterestedStreamController,
} from "../controllers";

// import authenticateStartAndEndStream from "../middlewares/restrict-ip";

const streamRouter = Router();

streamRouter.post(
  "/live/start",
  authenticateStartStream,
  LiveStreamController.handleStreamStart
);
streamRouter.post(
  "/live/end",
  authenticateEndStream,
  LiveStreamController.handleStreamEnd
);

// OvenMediaEngine AdmissionWebhooks endpoint
// Single endpoint that handles both opening/closing events based on request.status
streamRouter.post(
  "/live",
  LiveStreamController.handleOmeWebhook
);

streamRouter.post("/live/user-join", LiveStreamController.recordComplete);
streamRouter.post("/live/user-left", LiveStreamController.recordReady);
streamRouter.post(
  "/transcoding-complete",
  StreamController.httpUpdateVodStatus
);
streamRouter.use(authenticate);
streamRouter.get(
  "/global-trending",
  TrendingStreamController.httpGlobalTrendingStreams
);
streamRouter.get(
  "/recommendation",
  TrendingStreamController.httpPersonalizedStreamRecommendation
);
streamRouter.get(
  "/hybrid-recommendation",
  TrendingStreamController.httpPersonalizedStreamWithHybridRecommendation
);
streamRouter.get(
  "/popular-streams",
  TrendingStreamController.getPopularStreamsController
);
//GET route for not interested list
streamRouter.get(
  "/get-not-interested",
  NotInterestedStreamController.httpFetchNotInterestedStreams
);
streamRouter.get(
  "/fetch-live-streams",
  LiveStreamController.httpfetchLiveStreams
);
streamRouter.get(
  "/fetch-streams-category",
  StreamController.httpfetchStreamsByCategory
);

streamRouter.get(
  "/fetch-featured",
  FeaturedStreamController.httpGetFeaturedStreams
);
streamRouter.get(
  "/",
  validator.query(fetchStreamsSchema),
  StreamController.httpFetchStreams
);
streamRouter.get("/presigned-url", StreamController.httpGeneratePreSignedUrl);

streamRouter.put(
  "/presigned-url/upload",
  upload.single("file"),
  StreamController.httpTestUploadToPresignedUrl
);
streamRouter.get(
  "/is-live/:streamId?",
  validator.params(isStreamLiveSchema),
  LiveStreamController.isStreamLive
);
streamRouter.get(
  "/data/:id",
  validator.params(getStreamDetailsSchema),
  checkStreamAccess,
  StreamController.httpGetStreamDetails
);
// Live2 streaming endpoints (authenticated)
streamRouter.get(
  "/urls/:streamKey",
  authenticate,
  validator.params(getStreamUrlsSchema),
  LiveStreamController.getStreamUrls
);
streamRouter.get(
  "/stats",
  authenticate,
  LiveStreamController.getStreamingStats
);

// Health check endpoint (public - no authentication required)
streamRouter.get(
  "/health",
  LiveStreamController.healthCheck
);

streamRouter.get(
  "/:id",
  validator.params(getStreamDetailsSchema),
  checkStreamAccess,
  StreamController.httpGetStreamDetails
);
streamRouter.put(
  "/:id",
  validator.params(getStreamDetailsSchema),
  validator.body(updateStreamSettingsSchema),
  StreamController.httpUpdateStreamSettings
);
streamRouter.post(
  "/",
  // upload.single("file"),
  // validator.body(createStreamSchema),
  StreamController.httpCreateStream
);
streamRouter.delete(
  "/:id",
  validator.params(deleteStreamSchema),
  StreamController.httpDeleteStream
);
streamRouter.post(
  "/cover",
  upload.single("file"),
  StreamController.httpUploadCover
);
streamRouter.post("/comments", StreamController.httpFetchStreamComments);
streamRouter.post(
  "/live/start-channel",
  authenticate,
  LiveStreamController.httpUserLiveStreaming
);
streamRouter.post(
  "/live/stop-channel",
  LiveStreamController.httpCancelLivesStream
);
streamRouter.post(
  "/add-featured",
  validator.body(AddFeaturedStreamSchema),
  FeaturedStreamController.httpAddFeaturedStreams
);
streamRouter.post(
  "/remove-featured",
  validator.body(AddFeaturedStreamSchema),
  FeaturedStreamController.httpRemoveFeaturedStream
);

/**
 * POST API Routes for not interested streams.
 */
streamRouter.post(
  "/add-not-Interested",
  validator.body(NotInterestedStreamSchema),
  NotInterestedStreamController.httpAddStreamToNotInterested
);
streamRouter.post(
  "/remove-not-Interested",
  validator.body(NotInterestedStreamSchema),
  NotInterestedStreamController.httpRemoveStreamFromNotinterested
);
/**
 * POST API Routes for Reporting streams.
 */
streamRouter.post(
  "/report-stream",
  validator.body(ReportStreamSchema),
  StreamController.httpReportStream
);
streamRouter.post(
  "/update-report",
  validator.body(UpdateReportStreamStatus),
  StreamController.httpUpdateReportStatus
);

export default streamRouter;

import { Router } from "express";
import authenticate from "../middlewares/authenticate";
import {
  ProfileController,
  UserActivityController,
  UserController,
} from "../controllers";
import { validator } from "../middlewares/validator";
import {
  addProfileViewSchema,
  checkUsernameValidator,
  deleteUserSchema,
  followUserSchema,
  getAllSavedItemsSchema,
  getFollowersSchema,
  getLikedStreamsSchema,
  getProfileViewersSchema,
  getSavedItemsSchema,
  getTotalLikesSchema,
  getUserStreamsSchema,
  saveUnsaveItemSchema,
  updatePostSettingsSchema,
  updateUserDetailsSchema,
  updateUserSettingsSchema,
  userIdCheckMiddleware,
} from "../validators/user.validator";
import upload from "../middlewares/upload";

const userRouter = Router();

userRouter.get("/verify", UserController.httpVerifyUserEmail);
userRouter.post(
  "/check-username",
  validator.body(checkUsernameValidator),
  UserController.httpCheckUsernameExists
);
userRouter.use(authenticate);

userRouter.get(
  "/content/:id",
  validator.params(getUserStreamsSchema),
  UserController.httpGetUserStreams
);
userRouter.get(
  "/creator/:creatorId",
  validator.params(userIdCheckMiddleware),
  UserController.httpGetCreatorProfile
);

userRouter.get(
  "/profile/recommended-users",
  UserController.httpGetRecommendedUsers
);
userRouter.get(
  "/profile/viewers",
  validator.query(getProfileViewersSchema.query),
  ProfileController.httpGetProfileViewers
);

userRouter.get(
  "/profile/:userId/followers",
  validator.params(getFollowersSchema.params),
  validator.query(getFollowersSchema.query),
  ProfileController.httpGetUserFollowers
);
userRouter.get(
  "/profile/:userId/following",
  validator.params(getFollowersSchema.params),
  validator.query(getFollowersSchema.query),
  ProfileController.httpGetUserFollowing
);

userRouter.get(
  "/profile/:userId/likes",
  validator.params(getTotalLikesSchema.params),
  ProfileController.httpGetTotalLikesOfUser
);

userRouter.get(
  "/profile/liked-streams",
  validator.query(getLikedStreamsSchema.query),
  ProfileController.httpGetLikedStreams
);

userRouter.get(
  "/activity/saved-items",
  validator.query(getSavedItemsSchema.query),
  UserActivityController.httpGetSavedItems
);

// userRouter.get(
//   "/activity/saved-items/all",
//   validator.query(getAllSavedItemsSchema.query),
//   UserActivityController.httpGetAllSavedItems
// );

userRouter.get(
  "/send-verification-email",
  UserController.httpSendEmailVerificationMail
);
userRouter.get("/not-interested", UserController.httpGetNotInterestedUsers);

userRouter.put(
  "/",
  upload.single("file"),
  validator.body(updateUserDetailsSchema),
  UserController.httpUpdateUserDetails
);
userRouter.put(
  "/settings",
  validator.body(updateUserSettingsSchema),
  UserController.httpUpdateUserSettings
);
userRouter.put(
  "/post-settings",
  validator.body(updatePostSettingsSchema),
  UserController.httpUpdatePostSettings
);

userRouter.post(
  "/follow",
  validator.body(followUserSchema),
  UserController.httpFollowUser
);
userRouter.delete(
  "/",
  validator.body(deleteUserSchema),
  UserController.httpDeleteAccount
);
userRouter.post(
  "/not-interested/add",
  UserController.httpPostAddToNotInterested
);
userRouter.post(
  "/not-interested/remove",
  UserController.httpPostRemoveFromNotInterested
);
userRouter.post(
  "/not-interested/check",
  UserController.httpPostCheckNotInterested
);

userRouter.post(
  "/not-interested/check",
  UserController.httpPostCheckNotInterested
);

userRouter.post(
  "/profile/:creatorId/view-profile",
  validator.params(addProfileViewSchema),
  ProfileController.httpMarkProfileViewed
);

userRouter.post(
  "/activity/save-item",
  validator.body(saveUnsaveItemSchema.body),
  UserActivityController.httpSaveItem
);

userRouter.post(
  "/activity/unsave-item",
  validator.body(saveUnsaveItemSchema.body),
  UserActivityController.httpUnsaveItem
);

export default userRouter;

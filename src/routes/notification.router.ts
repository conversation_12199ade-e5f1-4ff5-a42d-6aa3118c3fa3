import { Router } from "express";
import {
  httpPostFetchNotifications,
  httpPostMarkReadNotifications,
  httpGetUnreadNotificationsCount,
} from "../controllers/notification.controller";
import authenticate from "../middlewares/authenticate";

const notificationRouter = Router();

notificationRouter.use(authenticate);
notificationRouter.get("/unreadCount", httpGetUnreadNotificationsCount);
notificationRouter.post("/", httpPostFetchNotifications);
notificationRouter.post("/read", httpPostMarkReadNotifications);

export default notificationRouter;

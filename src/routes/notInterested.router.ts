import { Router } from "express";


import { validator } from "../middlewares/validator";
import authenticate from "../middlewares/authenticate";
import { NotInterestedController } from "../controllers";
import { addToNotInterestedValidator, removeFromNotInterestedValidator } from "../validators/notInterested.validator";

const notInterestedRouter = Router();
notInterestedRouter.use(authenticate);

notInterestedRouter.post("/add", validator.body(addToNotInterestedValidator), NotInterestedController.httpAddToNotInterested);
notInterestedRouter.delete("/remove", validator.body(removeFromNotInterestedValidator), NotInterestedController.httpRemoveFromNotInterested);

export default notInterestedRouter;

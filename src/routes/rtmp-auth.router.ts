import express from 'express';
import { Request, Response } from 'express';
import { UserService } from '../services';
import { live2Service } from '../services/streamService/live2.service';
import Stream from '../models/stream/stream.schema';
import logger from '../config/logger';
import * as jwt from "jsonwebtoken";
import { JWT_SECRET } from '../config/environment';

const router = express.Router();

// RTMP Authentication endpoints

/**
 * Authenticate stream key
 * Called by NGINX RTMP when a stream starts publishing
 */
router.post('/publish', async (req: Request, res: Response) => {
  try {
    const { name: streamKey } = req.body;
    logger.info('Stream publish request:', { body: req.body });
    
    if (!streamKey) {
      logger.warn('Stream publish failed: missing stream key');
      return res.status(403).send('FORBIDDEN');
    }

    // Verify the JWT stream key
    let decoded: { userId: string; liveStreamId: string; };
    try {
      decoded = jwt.verify(streamKey, JWT_SECRET) as { userId: string; liveStreamId: string; };
    } catch (error) {
      logger.warn('Stream publish failed: invalid stream key', { streamKey, error });
      return res.status(403).send('FORBIDDEN');
    }

    const { userId, liveStreamId } = decoded;

    // Validate stream exists and belongs to user
    const stream = await Stream.findOne({
      liveStreamId: liveStreamId,
      creator: userId,
      status: { $in: ['draft', 'pending'] } // Assuming 'pending' is a valid state before publishing
    });

    if (!stream) {
      logger.warn('Stream publish failed: stream not found or not in a valid state', { liveStreamId, userId });
      return res.status(403).send('FORBIDDEN');
    }

    // Update stream status to indicate it's live
    await Stream.findByIdAndUpdate(stream.id, {
      isLive: true,
      status: 'live'
    });

    logger.info('Stream authenticated and set to live', { userId, liveStreamId, streamTitle: stream.title });
    return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in stream publish authentication:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * Handle stream done notification
 * Called by NGINX RTMP when a stream ends
 */
router.post('/publish_done', async (req: Request, res: Response) => {
  try {
    const { name: streamKey } = req.body;
    logger.info('Stream ended, deauth user request:', { body: req.body });
    
    if (streamKey) {
        try {
            const decoded = jwt.verify(streamKey, JWT_SECRET) as { userId: string, liveStreamId: string };
            const { userId, liveStreamId } = decoded;

            // Update stream status to ended
            await Stream.findOneAndUpdate({liveStreamId}, {
                isLive: false,
                status: 'ended',
                endedAt: new Date()
            });

            logger.info('Stream ended and status updated', { userId, liveStreamId });
        } catch (error) {
            logger.warn('Stream done failed: invalid stream key', { streamKey, error });
        }
    }
  
  return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in user deauth:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * Authenticate user for streaming
 * Called by NGINX RTMP when a stream starts
 */
router.post('/auth-user', async (req: Request, res: Response) => {
  try {
    const { name, addr, app } = req.body;
    logger.info('User auth request for streaming:', { body: req.body });
    
    if (!name) {
      logger.warn('User auth failed: missing stream name');
      return res.status(403).send('FORBIDDEN');
    }

    // Parse stream key to get user ID
    const streamKeyParts = name.split('_');
    if (streamKeyParts.length < 2) {
      logger.warn('User auth failed: invalid stream key format');
      return res.status(403).send('FORBIDDEN');
    }

    const [userId] = streamKeyParts;

    // Validate user exists and is verified
    const user = await UserService.getUser(userId);
    if (!user || !user.verified) {
      logger.warn('User auth failed: user not found or not verified', userId);
      return res.status(403).send('FORBIDDEN');
    }

    // Log streaming attempt
    logger.info('User authenticated for streaming', { 
      userId, 
      username: user.username, 
      addr, 
      app 
    });
  
  return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in user authentication:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * Authenticate recording
 * Called by NGINX RTMP when a recording starts
 */
router.post('/auth-record', async (req: Request, res: Response) => {
  try {
    const { name, path } = req.body;
    logger.info('Record auth request:', { body: req.body });
    
    if (!name) {
      logger.warn('Record auth failed: missing stream name');
      return res.status(403).send('FORBIDDEN');
    }

    // Parse stream key to validate user
    const streamKeyParts = name.split('_');
    if (streamKeyParts.length < 2) {
      logger.warn('Record auth failed: invalid stream key format');
      return res.status(403).send('FORBIDDEN');
    }

    const [userId, streamId] = streamKeyParts;

    // Validate stream exists and user has recording permissions
    const stream = await Stream.findOne({
      _id: streamId,
      creator: userId,
      isLive: true
    });

    if (!stream) {
      logger.warn('Record auth failed: stream not found or not live', { streamId, userId });
      return res.status(403).send('FORBIDDEN');
    }

    logger.info('Recording authenticated', { userId, streamId, recordPath: path });
  return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in recording authentication:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * Deauthenticate user
 * Called by NGINX RTMP when a stream ends
 */
router.post('/deauth-user', async (req: Request, res: Response) => {
  try {
    const { name } = req.body;
    logger.info('Stream ended, deauth user request:', { body: req.body });
    
    if (name) {
      const streamKeyParts = name.split('_');
      if (streamKeyParts.length >= 2) {
        const [userId, streamId] = streamKeyParts;

        // Update stream status to ended
        await Stream.findByIdAndUpdate(streamId, {
          isLive: false,
          status: 'ended',
          endedAt: new Date()
        });

        logger.info('Stream ended and status updated', { userId, streamId });
      }
    }
  
  return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in user deauth:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * Deauthenticate recording
 * Called by NGINX RTMP when a recording ends
 */
router.post('/deauth-record', async (req: Request, res: Response) => {
  try {
    const { name, path } = req.body;
    logger.info('Recording ended, deauth record request:', { body: req.body });
    
    if (name && path) {
      const streamKeyParts = name.split('_');
      if (streamKeyParts.length >= 2) {
        const [userId, streamId] = streamKeyParts;

        // Update stream with recording information
        await Stream.findByIdAndUpdate(streamId, {
          vodUrls: { recording: path },
          vodStatus: 'processing'
        });

        logger.info('Recording ended and stream updated', { 
          userId, 
          streamId, 
          recordingPath: path 
        });
      }
    }
  
  return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in recording deauth:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * Authenticate playback
 * Called by NGINX RTMP when a viewer starts watching
 */
router.post('/playbackAuth', async (req: Request, res: Response) => {
  try {
    const { name, addr } = req.body;
    logger.info('Playback auth request:', { body: req.body });
    
    if (!name) {
      logger.warn('Playback auth failed: missing stream name');
      return res.status(403).send('FORBIDDEN');
    }

    // Parse stream key to get stream info
    const streamKeyParts = name.split('_');
    if (streamKeyParts.length < 2) {
      logger.warn('Playback auth failed: invalid stream key format');
      return res.status(403).send('FORBIDDEN');
    }

    const [userId, streamId] = streamKeyParts;

    // Check if stream exists and is live
    const stream = await Stream.findOne({
      _id: streamId,
      creator: userId,
      isLive: true,
      status: 'published'
    }).populate('creator', 'username');

    if (!stream) {
      logger.warn('Playback auth failed: stream not found or not available', { streamId, userId });
      return res.status(403).send('FORBIDDEN');
    }

    // TODO: Add additional access control based on stream visibility settings
    // For now, allow all playback for published live streams
    
    logger.info('Playback authenticated', { 
      userId, 
      streamId, 
      streamTitle: stream.title,
      viewerAddr: addr
    });
  
  return res.status(200).send('OK');
    
  } catch (error) {
    logger.error('Error in playback authentication:', error);
    return res.status(500).send('ERROR');
  }
});

export default router;
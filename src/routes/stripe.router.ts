import { Router } from "express";
import {
  httpCreatePlatformSubscriptionPlans,
  httpCreateSubscriptionPlan,
  httpGetSubscriptionPlans,
  httpManageSubscription,
  httpOnboardCustomer,
  httpSubscribeToPlan,
  httpSubscribeToPlatformPlan,
} from "../controllers/stripe.controller";
import authenticate from "../middlewares/authenticate";

const stripeRouter = Router();

stripeRouter.use(authenticate);

stripeRouter.post("/plan/platform", httpCreatePlatformSubscriptionPlans);
stripeRouter.post("/plan/user", httpCreateSubscriptionPlan);
stripeRouter.get("/plan", httpGetSubscriptionPlans);
stripeRouter.get("/manage", httpManageSubscription);
stripeRouter.post("/subscribe/platform", httpSubscribeToPlatformPlan);
stripeRouter.post("/subscribe/user", httpSubscribeToPlan);
stripeRouter.get("/onboard", httpOnboardCustomer);

export default stripeRouter;

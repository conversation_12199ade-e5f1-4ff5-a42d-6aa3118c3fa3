import { Router } from "express";
import authenticate from "../middlewares/authenticate";
import { validator } from "../middlewares/validator";
import { createReportValidator } from "../validators/report.validator";
import { ReportController } from "../controllers";

const reportRouter = Router();

reportRouter.use(authenticate);

reportRouter.post("/create", validator.body(createReportValidator), ReportController.httpcreateReport);

export default reportRouter;

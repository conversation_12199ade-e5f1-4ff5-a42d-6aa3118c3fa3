import { Router } from "express";
import {
  httpGetGlobalTrendingSearches,
  httpGetTrendingSearches,
  httpOptimizedSearch,
  httpSearch,
} from "../controllers/search.controller";
import { validator } from "../middlewares/validator";
import { globalSearchSchema } from "../validators/search.validator";
import authenticate from "../middlewares/authenticate";

const searchRouter = Router();
searchRouter.use(authenticate);

searchRouter.get("/", validator.query(globalSearchSchema), httpOptimizedSearch);
searchRouter.get("/trending", httpGetTrendingSearches);
searchRouter.get("/global-trending-searches", httpGetGlobalTrendingSearches);

export default searchRouter;

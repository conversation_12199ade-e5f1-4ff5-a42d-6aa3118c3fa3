import { Router } from "express";
import passport from "passport";
import jwt from "jsonwebtoken";
import { validator } from "../middlewares/validator";
import {
  addInterestSchema,
  completeProfileSchema,
  forgotPasswordSchema,
  resendOTPSchema,
  resetPasswordSchema,
  signupSchema,
  verifyOTPSchema,
} from "../validators/auth.validator";
import { CLIENT_URL, JWT_SECRET } from "../config/environment";
import {
  SignupController,
  InterestController,
  AuthController,
  PhoneAuthController,
  SSOController,
} from "../controllers";

const authRouter = Router();

// Signup
authRouter.post(
  "/email/signup",
  validator.body(signupSchema),
  SignupController.httpSignupWithEmail
);
authRouter.post(
  "/verify-signup-otp",
  validator.body(verifyOTPSchema),
  SignupController.httpVerifySignupOTP
);
authRouter.post("/phone/signup", PhoneAuthController.httpSignUpWithNumber);
authRouter.post("/email/otp", AuthController.httpSendOTPandUpdateEmail);
authRouter.post(
  "/add-interests",
  validator.body(addInterestSchema),
  InterestController.httpAddUserInterests
);

authRouter.post(
  "/login/complete-profile",
  validator.body(completeProfileSchema.body),
  AuthController.httpCompleteUserProfile
);

// login
authRouter.post("/login", AuthController.httpLogin); //login via email or username
authRouter.post("/phone/login", PhoneAuthController.httpSignInWithNumber); //login via phoneNumber

// Social logins
authRouter.post("/login/google", SSOController.httpLoginWithGoogle);
authRouter.post("/login/apple", SSOController.httpAppleSignInController);

authRouter.get(
  "/google",
  passport.authenticate("google", {
    scope: ["email", "profile"],
    session: false,
  })
);

authRouter.get(
  "/google/callback",
  passport.authenticate("google", { session: false }),
  (req, res) => {
    const token = jwt.sign({ id: req.user!._id }, JWT_SECRET, {
      expiresIn: "1h",
    });
    res.redirect(`${CLIENT_URL}/profile?token=${token}`);
  }
);

// Reset password flow
authRouter.post(
  "/resend-otp",
  validator.body(resendOTPSchema),
  SignupController.httpResendSignupOTP
);

authRouter.post(
  "/forgot-password",
  validator.body(forgotPasswordSchema),
  AuthController.httpSendForgotPasswordOTP
);

authRouter.post(
  "/verify-otp",
  validator.body(verifyOTPSchema),
  AuthController.httpVerifyForgotPasswordOTP
);

authRouter.post(
  "/reset-password",
  validator.body(resetPasswordSchema),
  AuthController.httpResetPassword
);

// logout
authRouter.post("/logout", AuthController.httpLogout);

// interests
authRouter.get("/interests", InterestController.httpFetchInterestList);

export default authRouter;

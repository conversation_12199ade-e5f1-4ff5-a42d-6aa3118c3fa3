import { Router } from "express";
import authenticate from "../middlewares/authenticate";
import { ShortsController } from "../controllers";
import {
  addShortCommentSchema,
  createShortSchema,
  deleteShortCommentSchema,
  getRecommendationValidator,
  getRepliesSchema,
  getShortCommentsSchema,
  getTrendingShortsValidator,
  likeShortSchema,
  viewShortSchema,
} from "../validators/shorts.validator";
import { validator } from "../middlewares/validator";

const shortsRouter = Router();

shortsRouter.use(authenticate);
shortsRouter.get(
  "/trending",
  validator.query(getTrendingShortsValidator),
  ShortsController.httpGetTrendingShorts
);

shortsRouter.get(
  "/recommendation",
  validator.query(getRecommendationValidator),
  ShortsController.httpGetPersonalizedRecommendations
);

shortsRouter.get("/short-feed", ShortsController.httpGetShortsFeed);
shortsRouter.get(
  "/shorts-presigned-url",
  ShortsController.httpGeneratePreSignedUrlForShorts
);
shortsRouter.get("/:id", ShortsController.httpGetShortsById);
shortsRouter.get("/author/:authorId", ShortsController.httpGetShortsByAuthor);

shortsRouter.get(
  "/:shortId/comments",
  validator.params(getShortCommentsSchema.params),
  validator.query(getShortCommentsSchema.query),
  ShortsController.httpGetShortComments
);

shortsRouter.get(
  "/comments/:commentId/replies",
  validator.params(getRepliesSchema.params),
  validator.query(getRepliesSchema.query),
  ShortsController.httpGetCommentReplies
);




shortsRouter.post(
  "/create-short",
  validator.body(createShortSchema),
  ShortsController.httpCreateShorts
);

shortsRouter.post(
  "/:id/comments",
  validator.params(addShortCommentSchema.params),
  validator.body(addShortCommentSchema.body),
  ShortsController.httpAddShortComment
);

shortsRouter.post(
  "/:id/like",
  validator.params(likeShortSchema.params),
  validator.body(likeShortSchema.body),
  ShortsController.httpLikeOrUnlikeShort
);

shortsRouter.post(
  "/:id/view",
  validator.params(viewShortSchema.params),
  ShortsController.httpMarkShortViewed
);

shortsRouter.delete("/:id", ShortsController.httpDeleteShorts);
shortsRouter.delete(
  "/comment/:commentId",
  validator.params(deleteShortCommentSchema),
  ShortsController.httpDeleteShortComment
);

export default shortsRouter;

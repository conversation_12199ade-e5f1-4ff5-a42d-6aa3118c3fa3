import { Router } from "express";

import { reportProblemSchema } from "../validators/problemReport.validator";

import { validator } from "../middlewares/validator";
import { uploadReportImages } from "../middlewares/upload";
import authenticate from "../middlewares/authenticate";

import { httpReportProblem } from "../controllers/problemReport.controller";

const problemReportRouter = Router();

problemReportRouter.use(authenticate);

problemReportRouter.post(
  "/",
  uploadReportImages.array("files", 5),
  validator.body(reportProblemSchema),
  httpReportProblem
);

export default problemReportRouter;

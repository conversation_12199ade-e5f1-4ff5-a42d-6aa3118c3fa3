import { Request, Response, Router } from "express";
import { StoryController } from "../controllers";
import authenticate from "../middlewares/authenticate";
import { validator } from "../middlewares/validator";
import {
  createStoryValidator,
  generateStoryPresignSchema,
} from "../validators/story.validator";
import {
  httpGetStoriesByFollowings,
  httpGetUserStoriesById,
} from "../controllers/storyControllers/story.controller";
const storyRouter = Router();

storyRouter.use(authenticate);

storyRouter.get("/user-story", StoryController.httpGetUserStories);
storyRouter.get("/:userId", httpGetUserStoriesById);
storyRouter.post("/view", StoryController.httpMarkStoryAsViewed);
storyRouter.post(
  "/upload-story",
  validator.body(createStoryValidator),
  StoryController.httpUploadStory
);

storyRouter.post(
  "/generate-presign-url",
  validator.body(generateStoryPresignSchema),
  StoryController.httpGeneratePreSignedUrlForStories
);

storyRouter.post("/followings", httpGetStoriesByFollowings);

export default storyRouter;

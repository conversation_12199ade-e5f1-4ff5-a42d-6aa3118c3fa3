import { Request, Response } from "express";
import {
  getUnreadCount,
  getUserNotifications,
  markAsRead,
} from "../models/notification/notification.model";

export const httpPostFetchNotifications = async (
  req: Request,
  res: Response,
) => {
  try {
    const { type = "all", page = 1, limit = 20 } = req.body;
    const userId = req.user?._id;

    const nots = await getUserNotifications(
      userId,
      type === "unread",
      page,
      limit,
    );

    return res.status(200).json(nots);
  } catch (err) {
    console.log(err);
    res.status(500).send("Error fetching notifications");
  }
};

export const httpPostMarkReadNotifications = async (
  req: Request,
  res: Response,
) => {
  try {
    const { ids } = req.body;
    const userId = req.user?._id;

    await markAsRead(userId, ids);

    return res.status(200).json({
      message: "Notifications marked as read successfully",
    });
  } catch (err) {
    console.log(err);
    res.status(500).send("Error fetching notifications");
  }
};

export const httpGetUnreadNotificationsCount = async (
  req: Request,
  res: Response,
) => {
  try {
    const userId = req.user?._id;

    const count = await getUnreadCount(userId);

    return res.status(200).json({
      count,
    });
  } catch (err) {
    console.log(err);
    res.status(500).send("Error fetching notifications");
  }
};

import { Request, Response } from "express";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { ProfileService } from "../../services";
import { STATUS_CODES } from "../../constants/statusCodes";
import { MESSAGES, USER_MESSAGES } from "../../constants/responseMessage";

export const httpGetProfileViewers = async (req: Request, res: Response) => {
  const currentUserId = req.user?.id;
  // const { creatorId } = req.params;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  try {
    const result = await ProfileService.getProfileViewersWithMetaData(
      currentUserId,
      page,
      limit
    );
    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error in httpGetProfileViewers:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpMarkProfileViewed = async (req: Request, res: Response) => {
  try {
    const viewerId = req.user?._id;
    const { creatorId: targetUserId } = req.params;

    const result = await ProfileService.markProfileViewed(
      viewerId,
      targetUserId
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpMarkProfileViewed:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.PROFILE_VIEW_ERROR
    );
  }
};

export const httpGetTotalLikesOfUser = async (req: Request, res: Response) => {
  const { userId } = req.params;

  try {
    const result = await ProfileService.getTotalLikesByCreator(userId);

    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error in httpGetTotalLikes:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpGetLikedStreams = async (req: Request, res: Response) => {
  const userId = req.user?._id;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  try {
    const result = await ProfileService.getStreamsLikedByUser(
      userId,
      page,
      limit
    );

    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error in httpGetLikedStreams:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpGetUserFollowers = async (req: Request, res: Response) => {
  try {
    const targetUserId = req.params.userId;
    const currentUserId = req.user?._id;

    const page = req.query.page as unknown as number;
    const limit = req.query.limit as unknown as number;
    const search = (req.query.search as string) ?? "";

    const result = await ProfileService.getFollowersService(
      targetUserId,
      currentUserId,
      page,
      limit,
      search
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpGetUserFollowers:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

export const httpGetUserFollowing = async (req: Request, res: Response) => {
  try {
    const targetUserId = req.params.userId;
    const currentUserId = req.user?._id;

    const page = req.query.page as unknown as number;
    const limit = req.query.limit as unknown as number;
    const search = (req.query.search as string) ?? "";

    const result = await ProfileService.getFollowingService(
      targetUserId,
      currentUserId,
      page,
      limit,
      search
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpGetUserFollowing:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

import { Request, Response } from "express";
import { UserActivityService } from "../../services";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { SAVE_ITEMS, USER_MESSAGES } from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";

export const httpSaveItem = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { itemId, itemType } = req.body;

    const result = await UserActivityService.saveItem(userId, itemId, itemType);

    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error in httpSaveItem:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpUnsaveItem = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { itemId, itemType } = req.body;

    const result = await UserActivityService.unsaveItem(
      userId,
      itemId,
      itemType
    );

    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error in httpUnsaveItem:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpGetSavedItems = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const type = req.query.type as "stream" | "short";
    const page = req.query.page as unknown as number;
    const limit = req.query.limit as unknown as number;

    const result = await UserActivityService.getSavedItemsService(
      userId,
      type as "stream" | "short",
      page,
      limit
    );

    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error in httpGetSavedItems:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SAVE_ITEMS.ERROR_FETCHING,
      null
    );
  }
};

// export const httpGetAllSavedItems = async (req: Request, res: Response) => {
//   try {

//     const userId = req.user?._id;
//     const page = req.query.page as unknown as number;
//     const limit = req.query.limit as unknown as number;

//     const result = await UserActivityService.getAllSavedItemsService(
//       userId,
//       page,
//       limit
//     );

//     return result.success
//       ? SuccessResponse(
//           res,
//           result.statusCode,
//           true,
//           result.message,
//           result.data
//         )
//       : ErrorResponse(
//           res,
//           result.statusCode,
//           false,
//           result.message,
//           result.data
//         );
//   } catch (error) {
//     console.error("Error in httpGetAllSavedItems:", error);
//     return ErrorResponse(
//       res,
//       STATUS_CODES.INTERNAL_SERVER_ERROR,
//       false,
//       SAVE_ITEMS.ERROR_FETCHING,
//       null
//     );
//   }
// };

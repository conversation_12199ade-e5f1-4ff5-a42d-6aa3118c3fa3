import { Request, Response } from "express";
import {
  createEmailVerificationToken,
  getUserByToken,
} from "../../models/token/token.model";
import { EMAIL_VERIFICATION_MAIl } from "../../constants/email";
import { sendMail } from "../../lib/mailer";
import { uploadFileToS3 } from "../../lib/s3";
import { AWS_S3_BUCKET_NAME } from "../../config/environment";
import { UserService } from "../../services/index";
import { sendNotification } from "../../models/notification/notification.model";
import { Types } from "mongoose";
import User from "../../models/user/user.schema";
import { Schema } from "mongoose";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  MESSAGES,
  STREAM_MESSAGES,
  USER_MESSAGES,
  USERNAME,
} from "../../constants/responseMessage";
import { getStreamsPostedByUser } from "../../models/stream/stream.model";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { ENotificationType } from "../../types/enum";

export const httpSendEmailVerificationMail = async (
  req: Request,
  res: Response
) => {
  try {
    const user = req.user;

    if (!user) {
      return res.status(401).send("Unauthorized");
    }

    const token = await createEmailVerificationToken(user._id);

    const email = EMAIL_VERIFICATION_MAIl(token);

    sendMail(user.email, email.subject, email.body);

    res.status(200).send("Email sent successfully");
  } catch (err) {
    res.statusMessage = "Error sending mail";
    res.status(401).send("Error sending mail");
  }
};

export const httpVerifyUserEmail = async (req: Request, res: Response) => {
  try {
    const { token } = req.query;

    if (!token) {
      throw Error();
    }

    const user = await getUserByToken(token as string);

    if (!user) {
      throw new Error("Invalid verification token");
    }

    user.verified = true;
    await user.save();

    res.status(201).send("Email verified successfully");
  } catch (err) {
    res.statusMessage = "Error verifying user";
    res.status(401).send("Error verifying user");
  }
};

/**
 * Foollowing controller function is responsible for updating user details.
 * @param req request object
 * @param res response object
 * @returns response
 */
export const httpUpdateUserDetails = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const {
      displayName,
      bio,
      username,
      userInstagram,
      userFacebook,
      userYoutube,
    } = req.body;

    const file = req.file;

    if (!user) {
      return res.status(STATUS_CODES.UNAUTHORIZED).json({
        success: false,
        message: USER_MESSAGES.UNAUTHORIZED,
        data: null,
      });
    }

    if (username) {
      const existingUser = await User.findOne({ username });
      if (existingUser && existingUser._id.toString() !== user._id.toString()) {
        return res.status(STATUS_CODES.BAD_REQUEST).json({
          success: false,
          message: "Username already taken",
          data: null,
        });
      }
      user.username = username;
    }

    if (file) {
      const key = `profile-pictures/${user._id}/${Date.now()}/${
        file.originalname
      }`;
      const contentType = file.mimetype;
      const { url, success, error } = await uploadFileToS3(
        file.buffer,
        AWS_S3_BUCKET_NAME,
        key,
        contentType
      );

      if (!success) {
        return res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: USER_MESSAGES.INTERNAL_SERVER_ERROR,
          data: null,
        });
      }

      user.profilePicture = url;
    }

    user.displayName = displayName || user.displayName;
    user.bio = bio || user.bio;

    if (userInstagram !== undefined) user.userInstagram = userInstagram;
    if (userFacebook !== undefined) user.userFacebook = userFacebook;
    if (userYoutube !== undefined) user.userYoutube = userYoutube;

    await user.save();

    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: USER_MESSAGES.UPDATED, user });
  } catch (err) {
    res.statusMessage = "Error updating user details";
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: USER_MESSAGES.INTERNAL_SERVER_ERROR,
      data: null,
    });
  }
};

export const httpUpdateUserSettings = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const { notifications, theme } = req.body;

    if (!user) {
      return res.status(401).send("Unauthorized");
    }

    user.profileSettings.notifications =
      notifications || user.profileSettings.notifications;
    user.profileSettings.theme = theme || user.profileSettings.theme;

    await user.save();

    res
      .status(200)
      .json({ message: "User settings updated successfully", user });
  } catch (err) {
    res.statusMessage = "Error updating user settings";
    res.status(500).send("Error updating user settings");
  }
};

export const httpUpdatePostSettings = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const { postVisibility, liveVisibility, liveVideoPricing } = req.body;

    if (!user) {
      return res.status(401).send("Unauthorized");
    }

    user.postSettings.postVisibility =
      postVisibility || user.postSettings.postVisibility;
    user.postSettings.liveVisibility =
      liveVisibility || user.postSettings.liveVisibility;
    user.postSettings.liveVideoPricing =
      liveVideoPricing || user.postSettings.liveVideoPricing;

    await user.save();

    res
      .status(200)
      .json({ message: "Post settings updated successfully", user });
  } catch (err) {
    res.statusMessage = "Error updating post settings";
    res.status(500).send("Error updating post settings");
  }
};

export const httpFollowUser = async (req: Request, res: Response) => {
  try {
    const user = req.user!;

    const { targetUserId } = req.body;

    if (!targetUserId) {
      return res.status(400).json({ message: "Target user ID is required." });
    }

    if (targetUserId === user._id.toString()) {
      return res
        .status(400)
        .json({ message: "You cannot follow/unfollow yourself." });
    }
    const targetUser = await UserService.getUser(targetUserId);

    if (!targetUser) {
      return res.status(404).json({ message: "User not found." });
    }

    if (user.following.includes(targetUserId)) {
      user.following = user.following.filter(
        (id) => id.toString() !== targetUserId
      );
      targetUser.followers = targetUser.followers.filter(
        (id) => id.toString() !== user._id.toString()
      );
    } else {
      user.following.push(targetUserId);
      targetUser.followers.push(user._id);

      const isSent = await sendNotification({
        userId: targetUserId,
        personId: user._id,
        type: ENotificationType.newFollower,
        contentId: null,
        message: "", //TODO : add message
        commentId: undefined,
        contentType: null,
      });

      if (!isSent) {
        console.error("Error Sending Notification");
      }
    }

    await user.save();
    await targetUser.save();

    res.status(200).json({ message: "Follow status updated." });
  } catch (err) {
    console.error("Error following user:", err);
    res.status(500).send("Server error.");
  }
};

/**
 * Following function get streams posted by user
 * @param req User id
 * @param res streams
 */
export const httpGetUserStreams = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status, type, page, limit, sort }: any = req.query;

    const data = await getStreamsPostedByUser(id, {
      status,
      type,
      page,
      limit,
      sort,
    });

    if (!data) {
      res.status(STATUS_CODES.NOT_FOUND).json({
        success: false,
        message: STREAM_MESSAGES.USER_NOT_FOUND,
        streams: [],
      });
    }

    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: STREAM_MESSAGES.FETCH_STREAMS, data });
  } catch (error) {
    console.error("Error fetching followed user streams:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      streams: [],
    });
  }
};

export const httpDeleteAccount = async (req: Request, res: Response) => {
  try {
    const user = req.user!;

    const { category, reason } = req.body;

    user.isDeleted = true;
    user.deletedReason.push({
      category,
      reason,
      deletedAt: new Date(),
    });

    await user.save();

    return res
      .status(200)
      .json({ message: "User account deleted successfully." });
  } catch (error) {
    console.error("Error deleting user account:", error);
    return res.status(500).json({ message: "Server error." });
  }
};

/**
 * Add a user to not interested list
 */
export const httpPostAddToNotInterested = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id; // Logged in user
    const { targetUserId } = req.body; // User to be added to not interested list

    // Validate target user ID
    if (!Types.ObjectId.isValid(targetUserId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    // Check if target user exists
    const targetUser = await UserService.getUser(targetUserId);
    if (!targetUser) {
      return res.status(404).json({ error: "Target user not found" });
    }

    // Check if user is trying to add themselves
    if (userId.toString() === targetUserId) {
      return res
        .status(400)
        .json({ error: "Cannot add yourself to not interested list" });
    }

    // Add to not interested list if not already added
    const updated = await User.findByIdAndUpdate(
      userId,
      {
        $addToSet: { notInterestedIn: targetUserId },
      },
      { new: true }
    );

    if (!updated) {
      return res.status(404).json({ error: "User not found" });
    }

    return res.status(200).json({
      success: true,
      message: "User added to not interested list",
      data: {
        notInterestedCount: updated.notInterestedIn.length,
      },
    });
  } catch (error) {
    console.error("Add to not interested error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Remove a user from not interested list
 */
export const httpPostRemoveFromNotInterested = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const { targetUserId } = req.body;

    // Validate target user ID
    if (!Types.ObjectId.isValid(targetUserId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    // Remove from not interested list
    const updated = await User.findByIdAndUpdate(
      userId,
      {
        $pull: { notInterestedIn: targetUserId },
      },
      { new: true }
    );

    if (!updated) {
      return res.status(404).json({ error: "User not found" });
    }

    return res.status(200).json({
      success: true,
      message: "User removed from not interested list",
      data: {
        notInterestedCount: updated.notInterestedIn.length,
      },
    });
  } catch (error) {
    console.error("Remove from not interested error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Get not interested users list
 */
export const httpGetNotInterestedUsers = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const user = await User.findById(userId)
      .select("notInterestedIn")
      .populate({
        path: "notInterestedIn",
        select: "username displayName profilePicture bio",
        match: { isDeleted: false },
      })
      .skip(skip)
      .limit(limit);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const total = user.notInterestedIn.length;

    return res.status(200).json({
      success: true,
      data: {
        users: user.notInterestedIn,
        pagination: {
          total,
          page,
          totalPages: Math.ceil(total / limit),
          hasMore: skip + limit < total,
        },
      },
    });
  } catch (error) {
    console.error("Get not interested users error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Check if a user is in not interested list
 */
export const httpPostCheckNotInterested = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const { targetUserId } = req.body;

    // Validate target user ID
    if (!Types.ObjectId.isValid(targetUserId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    const user = await User.findById(userId).select("notInterestedIn");

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const isNotInterested = user.notInterestedIn.includes(
      new Schema.Types.ObjectId(targetUserId)
    );

    return res.status(200).json({
      success: true,
      data: {
        isNotInterested,
      },
    });
  } catch (error) {
    console.error("Check not interested error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

/**
 * Helper function to exclude not interested users from query
 * Use this in other controllers when fetching user lists/feeds
 */
export const excludeNotInterestedUsers = async (userId: string) => {
  try {
    const user = await User.findById(userId).select("notInterestedIn");
    if (!user) return [];

    return user.notInterestedIn;
  } catch (error) {
    console.error("Exclude not interested users error:", error);
    return [];
  }
};

/**
 * Following function is responsible for Getting users and stream.
 * @param req we accept user Id in params.
 * @param res User response object.
 */
export const httpGetCreatorProfile = async (req: Request, res: Response) => {
  const { creatorId } = req.params;
  const userId = req.user?._id;
  try {
    const user = await UserService.getCreatorProfile(userId, creatorId);
    if (!user) {
      res
        .status(STATUS_CODES.NOT_FOUND)
        .json({ success: false, message: MESSAGES.NOT_FOUND, user: [] });
    }

    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: MESSAGES.SUCCESS, user });
  } catch (error) {
    console.error(error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: MESSAGES.INTERNAL_SERVER_ERROR,
      user: [],
    });
  }
};

/**
 * Controller function to check username availability.
 * @param req
 * @param res
 * @returns
 */
export const httpCheckUsernameExists = async (req: Request, res: Response) => {
  const { username } = req.body;
  try {
    const exists = await UserService.checkUsernameExists(username);
    if (exists) {
      return SuccessResponse<boolean>(
        res,
        STATUS_CODES.OK,
        false,
        USERNAME.TAKEN,
        false
      );
    }
    return SuccessResponse<boolean>(
      res,
      STATUS_CODES.OK,
      true,
      USERNAME.AVAILABLE,
      true
    );
  } catch (error: any) {
    return ErrorResponse<boolean>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USERNAME.INTERNAL_SERVER_ERROR
    );
  }
};

export const httpGetRecommendedUsers = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await UserService.getRecommendedUsersService(
      userId,
      page,
      limit
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error(err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      USER_MESSAGES.RECOMMENDATION_FAILED
    );
  }
};

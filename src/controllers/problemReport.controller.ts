import { Request, Response } from "express";
import { createReport } from "../models/problemReport/problemReport.model";
import { uploadFileToS3 } from "../lib/s3";
import { AWS_S3_BUCKET_NAME } from "../config/environment";

export const httpReportProblem = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const { category, message } = req.body;

    if (!user) {
      return res.status(401).send("Unauthorized");
    }

    const imageUrls: string[] = [];

    if (req.files) {
      const files = req.files as Express.Multer.File[];

      for (const file of files) {
        const key = `problem-reports/${user._id}/${Date.now()}/${
          file.originalname
        }`;
        const contentType = file.mimetype;

        const { url, success, error } = await uploadFileToS3(
          file.buffer,
          AWS_S3_BUCKET_NAME,
          key,
          contentType
        );

        if (!success) {
          return res.status(500).send("Error uploading file to S3: " + error);
        }

        imageUrls.push(url ?? "");
      }
    }

    const problemReport = await createReport({
      user: user._id,
      category,
      message,
      images: imageUrls,
      timestamp: new Date(),
    });

    res.status(200).json({
      message: "Problem reported successfully.",
      report: problemReport,
    });
  } catch (error) {
    console.error("Error reporting problem:", error);
    res.status(500).json({ message: "Server error." });
  }
};

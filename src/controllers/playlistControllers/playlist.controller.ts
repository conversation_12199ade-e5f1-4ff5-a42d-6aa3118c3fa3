// controllers/playlistController.ts
import { Request, Response } from "express";

import { STATUS_CODES } from "../../constants/statusCodes";
import { PLAYLIST_MESSAGES } from "../../constants/responseMessage";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { PlaylistService } from "../../services";

export const httpCreatePlaylist = async (req: Request, res: Response) => {
  try {
    const { title, description } = req.body;
    const createdBy = req.user?._id;

    const result = await PlaylistService.createPlaylist({
      title,
      description,
      createdBy,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpDeletePlaylist = async (req: Request, res: Response) => {
  try {
    const playlistId = req.params.id;
    const userId = req.user?._id;

    const result = await PlaylistService.deletePlaylist(playlistId, userId);

    return result.success
      ? SuccessResponse(
          res,
          result.statusCode,
          true,
          result.message,
          result.data
        )
      : ErrorResponse(
          res,
          result.statusCode,
          false,
          result.message,
          result.data
        );
  } catch (error) {
    console.error("Error deleting playlist:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpAddItemToPlaylist = async (req: Request, res: Response) => {
  try {
    const { id: playlistId } = req.params;
    const { items } = req.body;

    const result = await PlaylistService.addItemsToPlaylist({
      playlistId,
      items,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpRemoveItemFromPlaylist = async (
  req: Request,
  res: Response
) => {
  try {
    const { id: playlistId } = req.params;
    const { itemId, itemType } = req.body;

    const result = await PlaylistService.removeItemFromPlaylist({
      playlistId,
      itemId,
      itemType,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpUpdatePlaylist = async (req: Request, res: Response) => {
  try {
    const { id: playlistId } = req.params;
    const { title, description } = req.body;

    const result = await PlaylistService.updatePlaylist({
      playlistId,
      title,
      description,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpGetPlaylistById = async (req: Request, res: Response) => {
  try {
    const { id: playlistId } = req.params;
    const userId = req.user?._id;

    const result = await PlaylistService.getPlaylistById({
      playlistId,
      userId,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpGetPlaylistById:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

// export const httpGetAllPlaylistsForUser = async (
//   req: Request,
//   res: Response
// ) => {
//   try {
//     const userId = req.user?._id;

//     const result = await PlaylistService.getAllPlaylistsForUser(userId);

//     return SuccessResponse(
//       res,
//       result.statusCode,
//       result.success,
//       result.message,
//       result.data
//     );
//   } catch (err) {
//     console.error("Error in httpGetAllPlaylistsForUser:", err);
//     return ErrorResponse(
//       res,
//       STATUS_CODES.INTERNAL_SERVER_ERROR,
//       false,
//       PLAYLIST_MESSAGES.INTERNAL_ERROR
//     );
//   }
// };

// export const httpGetAllPlaylistsForUser = async (
//   req: Request,
//   res: Response
// ) => {
//   try {
//     const userId = req.user?._id;
//     const page = parseInt(req.query.page as string) || 1;
//     const limit = parseInt(req.query.limit as string) || 10;

//     const result = await PlaylistService.getAllPlaylistsForUser({
//       userId,
//       page,
//       limit,
//     });

//     return SuccessResponse(
//       res,
//       result.statusCode,
//       result.success,
//       result.message,
//       result.data
//     );
//   } catch (err) {
//     console.error("Error in httpGetAllPlaylistsForUser:", err);
//     return ErrorResponse(
//       res,
//       STATUS_CODES.INTERNAL_SERVER_ERROR,
//       false,
//       PLAYLIST_MESSAGES.INTERNAL_ERROR
//     );
//   }
// };

export const httpGetAllPlaylistsForUser = async (
  req: Request,
  res: Response
) => {
  try {
    const creatorId = req.query.creatorId as string;
    const page = req.query.page as unknown as number;
    const limit = req.query.limit as unknown as number;
    const search = (req.query.search as string) ?? "";

    const result = await PlaylistService.getAllPlaylistsForUser({
      userId: creatorId,
      page,
      limit,
      search,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpGetAllPlaylistsForUser:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpSearchPlaylists = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const search = (req.query.search as string) || "";
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await PlaylistService.searchPlaylists({
      userId,
      search,
      page,
      limit,
    });

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpSearchPlaylists:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      PLAYLIST_MESSAGES.INTERNAL_ERROR
    );
  }
};

import { Request, Response } from "express";
import { NotInterestedStream } from "../../services";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { INotInterested } from "../../types/schema";
import { STATUS_CODES } from "../../constants/statusCodes";
import { NOT_INTERESTED_MESSAGES } from "../../constants/responseMessage";

/**
 * Following Ccontroller adds stream to not interestsed
 * @param req Id of user and stream that needs to be added to not interested.
 * @param res response for operation success and error.
 * @returns Success and error handlers.
 */
export const httpAddStreamToNotInterested = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const { streamId } = req.body;

    const result = await NotInterestedStream.addNotInterestedStream(
      userId,
      streamId
    );
    return SuccessResponse<INotInterested>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err: any) {
    console.error(err);
    return ErrorResponse<INotInterested>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      NOT_INTERESTED_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Controller function to remove stream from not interested list
 * @param req user Id and stream Id
 * @param res
 * @returns success and error
 */
export const httpRemoveStreamFromNotinterested = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const { streamId } = req.body;

    const result = await NotInterestedStream.removeNotInterestedStream(
      userId,
      streamId
    );
    return SuccessResponse<INotInterested>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error(error);
    return ErrorResponse<INotInterested>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      NOT_INTERESTED_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Controller function to fetch not-interested streams of a user.
 * @param req userId
 * @param res response object.
 * @returns response handlers success and errors.
 */
export const httpFetchNotInterestedStreams = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;

    const result = await NotInterestedStream.fetchNotInterestedStreams(userId);
    return SuccessResponse<INotInterested[]>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error(error);
    return ErrorResponse<INotInterested>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      NOT_INTERESTED_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

import { Request, Response } from "express";
import Stream from "../../models/stream/stream.schema";
import {
  AWS_S3_BUCKET_NAME,
  AWS_S3_TEMP_BUCKET_NAME,
} from "../../config/environment";
import stripe from "../../config/stripe";
import StreamView from "../../models/streamView/streamView.schema";
import Payment from "../../models/payment/payment.schema";
import s3Client from "../../config/s3";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from "uuid";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { extractKeyFromPresignedUrl, getPublicUrlFromS3 } from "../../lib/s3";
import axios from "axios";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  STREAM_MESSAGES,
  SHARE_MESSAGES,
} from "../../constants/responseMessage";
import { getCommentsByStream } from "../../models/comment/comment.model";
import {
  getAllStreamsForUser,
  getStreamDetailsForUser,
} from "../../models/stream/stream.model";
import { COMMENT_MESSAGES } from "../../constants/socketMessage";
import { IDeleteStreamResult, IStream } from "../../types/schema";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";

import { ShortsService, StreamService } from "../../services";

export const httpCreateStream = async (req: Request, res: Response) => {
  try {
    if (req.body.type !== "video-live") {
      const url = req.body.url;
      const key = extractKeyFromPresignedUrl(url);
      const publicUrl = getPublicUrlFromS3(AWS_S3_TEMP_BUCKET_NAME, key ?? "");
      const videoId = publicUrl.match(/(\d+)\.mp4$/)?.[1] ?? "";

      const newStream = new Stream({
        ...req.body,
        status: "uploading",
        vodStatus: "processing",
        url: publicUrl,
        creator: req.user?._id,
        liveStreamId: videoId,
      });
      await newStream.save();
      const stream = await newStream.populate("creator", "-_id -__v -password");
      return res.status(STATUS_CODES.CREATED).json(stream);
    } else {
      const newStream = new Stream({
        ...req.body,
        url: null,
        creator: req.user?._id,
      });

      await newStream.save();

      const stream = await newStream.populate("creator", "-_id -__v -password");

      return res.status(STATUS_CODES.CREATED).json(stream);
    }
  } catch (err: any) {
    console.log(err);
    res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json("Error creating stream" + err.message);
  }
};

export const httpGetStreamDetails = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { _id } = req.user?._id;
    const streamId = id;
    const userId = _id;

    if (!id && userId) {
      res.status(STATUS_CODES.BAD_REQUEST).json({
        success: false,
        message: STREAM_MESSAGES.INVALID_INPUT,
        stream: null,
      });
    }
    const streamDetails = await getStreamDetailsForUser(userId, streamId);
    if (!streamDetails) {
      res.status(STATUS_CODES.NOT_FOUND).json({
        success: false,
        message: STREAM_MESSAGES.NOT_FOUND,
        stream: null,
      });
    }

    res.status(STATUS_CODES.OK).json({
      success: true,
      message: STREAM_MESSAGES.FETCH_STREAMS,
      stream: streamDetails,
    });
  } catch (error) {
    console.error("Error fetching stream details:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).send({
      success: false,
      message: STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      stream: null,
    });
  }
};

export const httpFetchStreams = async (req: Request, res: Response) => {
  try {
    const { _id } = req.user?._id;
    const userId = _id;

    if (!userId) {
      res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ message: STREAM_MESSAGES.INVALID_INPUT, success: false });
    }
    const { status, type, page, limit }: any = req.query;

    const streams = await getAllStreamsForUser(userId, {
      status,
      type,
      page,
      limit,
    });

    // if (!data && !pagination) {
    //   res.status(STATUS_CODES.BAD_REQUEST).json({
    //     message: STREAM_MESSAGES.INVALID_INPUT,
    //     success: false,
    //     stream: null,
    //     pagination: null,
    //   });
    // }

    res.status(STATUS_CODES.OK).json({
      streams,
    });
  } catch (err) {
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).send({
      message: STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      success: false,
      stream: null,
      pagination: null,
    });
  }
};

export const httpfetchStreamsByCategory = async (
  req: Request,
  res: Response
) => {
  try {
    const { category, type } = req.query;
    if (!category || typeof category !== "string") {
      return ErrorResponse(res, 400, false, "Category is required", null);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result =
      type === "short"
        ? await ShortsService.shortsByCategory(category, page, limit)
        : await StreamService.streamsByCategory(category, page, limit);

    if (result.success) {
      return SuccessResponse(
        res,
        result.statusCode,
        result.success,
        result.message,
        result.data
      );
    } else {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message,
        null
      );
    }
  } catch (error) {
    console.error("Error in getStreamsByCategory:", error);
    return ErrorResponse(res, 500, false, "Internal Server Error", null);
  }
};

/**
 * Following controller function is responsible for deleting stream.
 * @param req we accept streamId and userId.
 * @param res Success and error responses.
 * @returns response object
 */
export const httpDeleteStream = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const streamId = req.params.id;

    if (!userId || !streamId) {
      return ErrorResponse<IDeleteStreamResult>(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        STREAM_MESSAGES.INVALID_INPUT,
        { stream: null }
      );
    }

    const result = await StreamService.deleteStreamService(streamId, userId);

    return SuccessResponse<IStream>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpDeleteStream:", error);
    return ErrorResponse<IStream>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpUpdateStreamSettings = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const result = await Stream.findById(req.params.id, {
      isDeleted: false,
    });
    if (!result) return res.status(404).send("Stream not found");
    if (user?._id.toString() !== result.creator.toString()) {
      return res.status(401).send("Unauthorized");
    }
    const { visibility } = req.body;
    result.settings.visibility = visibility || result.settings.visibility;

    await result.save();
    res.status(200).send("Stream updated successfully");
  } catch (err) {
    res.status(500).send("Error deleting stream");
  }
};

export const httpChargeForViewingTime = async (req: Request, res: Response) => {
  try {
    const { userId, streamId } = req.body;

    const viewRecord = await StreamView.findOne({
      user: userId,
      stream: streamId,
    });
    if (!viewRecord) {
      return res.status(404).json({ error: "Viewing record not found" });
    }

    const costPerMinute = 0.1;

    const amountToCharge = viewRecord.minutesWatched * costPerMinute;

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amountToCharge * 100),
      currency: "usd",
      customer: userId,
      payment_method_types: ["card"],
      description: `Charge for ${viewRecord.minutesWatched} minutes of stream viewing`,
    });

    const paymentRecord = new Payment({
      user: userId,
      amount: amountToCharge,
      currency: "usd",
      paymentMethod: "stripe",
      status: paymentIntent.status,
      stripePaymentIntentId: paymentIntent.id,
    });

    await paymentRecord.save();

    res.json({
      message: "Charge successful",
      paymentIntentId: paymentIntent.id,
    });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpGeneratePreSignedUrl = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    const key = `videos/${userId}/${Date.now()}.mp4`;
    const command = new PutObjectCommand({
      Bucket: AWS_S3_TEMP_BUCKET_NAME,
      Key: key,
      ContentType: "video/mp4",
    });
    const url = await getSignedUrl(s3Client, command, {
      expiresIn: 10 * 60, //10 minutes
    });
    res.status(200).json({ url });
  } catch (err: any) {
    res.status(500).json({ error: err.message });
  }
};

export const httpTestUploadToPresignedUrl = async (
  req: Request,
  res: Response
) => {
  try {
    const { presignedUrl } = req.body;

    const inputFile = req.file as Express.Multer.File;
    if (!inputFile) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ error: "No file provided" });
    }

    // Verify it's an MP4 file
    if (!inputFile.mimetype.includes("video/mp4")) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ error: "File must be an MP4 video" });
    }

    await axios.put(presignedUrl, inputFile.buffer, {
      headers: {
        "Content-Type": "video/mp4", // Explicitly set for MP4
        "Content-Length": inputFile.size,
      },
      maxBodyLength: Infinity,
      maxContentLength: Infinity,
      timeout: 60000, // Increased timeout for video files (60 seconds)
    });

    res
      .status(STATUS_CODES.BAD_REQUEST)
      .json({ message: "uploaded successfully" });
  } catch (err: any) {
    console.log("err", err.message);

    if (err.response) {
      console.log("Response data:", err.response.data);
      console.log("Response status:", err.response.status);
      console.log("Response headers:", err.response.headers);

      // More specific error messages for common S3 errors
      if (err.response.status === STATUS_CODES.FORBIDDEN) {
        return res.status(STATUS_CODES.FORBIDDEN).json({
          error: "Upload not authorized. Presigned URL may have expired.",
        });
      }
      if (err.response.status === STATUS_CODES.BAD_REQUEST) {
        return res.status(STATUS_CODES.BAD_REQUEST).json({
          error:
            "Bad request. Check if presigned URL was generated for video/mp4 content type.",
        });
      }
    } else if (err.request) {
      console.log("Request error:", err.request);
      return res
        .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
        .json({ error: "Upload failed. Network error or timeout." });
    }

    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({ error: err.message });
  }
};

export const httpUploadCover = async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res
        .status(400)
        .json({ success: false, message: "No file uploaded" });
    }

    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const file = req.file;
    const fileExtension = file.originalname.split(".").pop();
    const fileName = `covers/${userId}/${uuidv4()}.${fileExtension}`;

    const params = {
      Bucket: AWS_S3_BUCKET_NAME,
      Key: fileName,
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    await s3Client.send(new PutObjectCommand(params));

    const imageUrl = getPublicUrlFromS3(AWS_S3_BUCKET_NAME, fileName);

    res.json({
      success: true,
      imageUrl,
    });
  } catch (error) {
    console.error("Upload error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to upload image",
    });
  }
};

/**
 * Following controller is for fetching comment list for a stream
 * @param {Object} req Accepting streamId from req.body
 * @param {Object} res returning response object including comment list and count.
 * @returns
 */
export const httpFetchStreamComments = async (req: Request, res: Response) => {
  try {
    const { streamId } = req.body;

    const { page = 1, limit = 10 }: { page?: number; limit?: number } =
      req.query;

    const { comments, totalComments } = await getCommentsByStream(
      streamId,
      Number(page),
      Number(limit)
    );

    if (!comments && !totalComments) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: COMMENT_MESSAGES.INVALID_INPUT });
    }

    res.status(STATUS_CODES.OK).json({
      success: true,
      message: COMMENT_MESSAGES.FETCH_COMMENTS,
      comments,
      currentPage: Number(page),
      totalPages: Math.ceil(totalComments / Number(limit)),
      totalComments,
    });
  } catch (err) {
    console.error("Error fetching comments:", err);
    res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json({ message: COMMENT_MESSAGES.INTERNAL_SERVER_ERROR });
  }
};

/**
 * Controller function for user to report Stream
 * @param req
 * @param res
 * @returns
 */
export const httpReportStream = async (req: Request, res: Response) => {
  try {
    const reporterId = req.user?._id;
    const { streamId, reason } = req.body;

    const result = await StreamService.createStreamReport(
      streamId,
      reporterId,
      reason
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error(err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      "Internal server error",
      null
    );
  }
};

/**
 * Controller function for updating report status
 * @param req
 * @param res
 * @returns
 */
export const httpUpdateReportStatus = async (req: Request, res: Response) => {
  try {
    const adminId = req.user?._id;
    const { reportId } = req.params;
    const { status, adminFeedback } = req.body;

    const result = await StreamService.updateReportStatus(
      reportId,
      adminId,
      status,
      adminFeedback
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error(err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      "Internal server error",
      null
    );
  }
};

export const httpUpdateVodStatus = async (req: Request, res: Response) => {
  try {
    const { uniqueKey } = req.body;

    const result = await StreamService.updateVodStatus(uniqueKey);
    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.log(error);
    return ErrorResponse(res, 500, false, "Internal server error", null);
  }
};

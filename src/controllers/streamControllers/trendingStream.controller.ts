import { Request, Response } from "express";

import { STATUS_CODES } from "../../constants/statusCodes";
import { STREAM_MESSAGES } from "../../constants/responseMessage";
import {
  fetchHybridRecommendations,
  fetchPersonalizedRecommendation,
} from "../../models/stream/stream.model";
import { ITrendingStream } from "../../types/schema";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { fetchTrendingStreamsForUser } from "../../services/streamService/trendingStreams.service";
import { StreamService } from "../../services";

// export const httpGlobalTrendingStreams = async (
//   req: Request,
//   res: Response
// ) => {
//   const { _id } = req.user?._id;
//   const userId = _id;

//   try {
//     const trendingStreamResult = await fetchTrendingStreamsForUser(userId);
//     if (!trendingStreamResult)
//       return res.status(STATUS_CODES.BAD_REQUEST).json({
//         message: STREAM_MESSAGES.NOT_FOUND,
//         success: false,
//         data: [],
//       });

//     return res.status(STATUS_CODES.OK).json({
//       message: STREAM_MESSAGES.FETCH_STREAMS,
//       success: true,
//       data: trendingStreamResult,
//     });
//   } catch (error) {
//     console.log(error);
//     return res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
//       message: STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
//       success: false,
//       data: [],
//     });
//   }
// };

export const httpGlobalTrendingStreams = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return ErrorResponse<ITrendingStream[]>(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        STREAM_MESSAGES.INVALID_INPUT,
        []
      );
    }

    const result = await fetchTrendingStreamsForUser(userId);
    return SuccessResponse<ITrendingStream[]>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGlobalTrendingStreams:", error);
    return ErrorResponse<ITrendingStream[]>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      []
    );
  }
};
/**
 * 1. Following function uses aggregation pipeline for fetching recommendation
 * @param req we accept user Id for personalized recommendation
 * @param res we return personalized streams.
 * @returns response
 */
export const httpPersonalizedStreamRecommendation = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(STATUS_CODES.BAD_REQUEST).json({
        success: false,
        message: STREAM_MESSAGES.UNAUTHORIZED,
        streams: [],
      });
    }

    const recommendations = await fetchPersonalizedRecommendation(userId);
    return res.status(STATUS_CODES.OK).json({
      success: true,
      message: STREAM_MESSAGES.FETCH_STREAMS,
      data: recommendations,
    });
  } catch (error) {
    console.error("Error in fetching recommendations:", error);
    return res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

/**
 * 1. Following function uses hybrid recommendation system for fetching recommendations
 * @param req we accept user Id for personalized recommendation
 * @param res we return personalized streams.
 * @returns response
 */
export const httpPersonalizedStreamWithHybridRecommendation = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(STATUS_CODES.BAD_REQUEST).json({
        success: false,
        message: STREAM_MESSAGES.UNAUTHORIZED,
        streams: [],
      });
    }

    const recommendations = await fetchHybridRecommendations(userId);
    return res.status(STATUS_CODES.OK).json({
      success: true,
      message: STREAM_MESSAGES.FETCH_STREAMS,
      data: recommendations,
    });
  } catch (error) {
    console.error("Error in fetching hybrid recommendations:", error);
    return res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

/**
 * 1. Following function fetches popular streams
 * @param req we accept user Id for personalized recommendation
 * @param res we return personalized streams.
 * @returns response
 */
export const getPopularStreamsController = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const { page, limit } = req.query as { page?: string; limit?: string };

    const serviceResponse = await StreamService.getPopularStreamsService(
      userId,
      { page, limit }
    );

    // If you prefer your own success/err responses:
    if (!serviceResponse.success) {
      return ErrorResponse(
        res,
        serviceResponse.statusCode,
        false,
        serviceResponse.message
      );
    }

    return SuccessResponse(
      res,
      serviceResponse.statusCode,
      serviceResponse.success,
      serviceResponse.message,
      serviceResponse.data
    );
  } catch (error) {
    console.error("Error fetching popular streams: ", error);
    return ErrorResponse(
      res,
      500,
      false,
      "An error occurred while fetching popular streams."
    );
  }
};

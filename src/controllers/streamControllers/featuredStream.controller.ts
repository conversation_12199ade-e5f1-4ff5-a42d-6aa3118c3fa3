import { Request, Response } from "express";
import { FEATURED_STREAM_MESSAGES } from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import { featuredStreamService } from "../../services";
import { IFeaturedStream } from "../../types/schema";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";

/**
 * Controller function to add streams to feature list.
 * @param req
 * @param res
 * @returns
 */
export const httpAddFeaturedStreams = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { streamId } = req.body;
    const result = await featuredStreamService.addFeaturedStream(
      userId,
      streamId
    );
    return SuccessResponse<IFeaturedStream>(
      res,
      result.statusCode,
      true,
      result.message,
      result.data
    );
  } catch (err: any) {
    console.log(err);

    return ErrorResponse<IFeaturedStream>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      FEATURED_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

/**
 * Controller function to remove stream from user's featured list.
 * @param req
 * @param res
 * @returns
 */
export const httpRemoveFeaturedStream = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { streamId } = req.body;

    if (!userId || !streamId) {
      return ErrorResponse<IFeaturedStream>(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        FEATURED_STREAM_MESSAGES.INVALID_INPUT,
        null
      );
    }

    const result = await featuredStreamService.removeFeaturedStream(
      userId,
      streamId
    );
    return SuccessResponse<IFeaturedStream>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.log("Error in httpRemoveFeaturedStream:", err);
    return ErrorResponse<IFeaturedStream>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      FEATURED_STREAM_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Following controller is responsible for streams which are featured.
 * @param req
 * @param res
 * @returns
 */
export const httpGetFeaturedStreams = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return ErrorResponse<IFeaturedStream>(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        FEATURED_STREAM_MESSAGES.INVALID_INPUT
      );
    }

    const result = await featuredStreamService.getFeaturedStreams(userId);
    return SuccessResponse<IFeaturedStream>(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpGetFeaturedStreams:", err);
    return ErrorResponse<IFeaturedStream>(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      FEATURED_STREAM_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

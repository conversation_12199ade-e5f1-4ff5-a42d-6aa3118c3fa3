import { Request, Response } from "express";
import { createReportService } from "../../services/reportService/report.service";
import { SuccessResponse, ErrorResponse } from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";


export const httpcreateReport = async (req: Request, res: Response) => {
    try {
        const reporter = req.user?._id;
        const { entityId, entityType, reason, description } = req.body;
        const result = await createReportService({ entityId, entityType, reason, description, reporter });
        return SuccessResponse(res, STATUS_CODES.CREATED, true, "Report submitted successfully", result);
    } catch (error) {
        return ErrorResponse(res, STATUS_CODES.INTERNAL_SERVER_ERROR, false, "Failed to submit report");
    }
}


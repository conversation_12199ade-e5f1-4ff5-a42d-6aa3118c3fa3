import { Request, Response } from "express";
import { PRIVACY_POLICY, TOS } from "../constants/about";

export const httpGetPrivacyPolicy = async (req: Request, res: Response) => {
  try {
    res.status(200).json({ success: true, data: PRIVACY_POLICY });
  } catch (e) {
    res.statusMessage = "Failed to get privacy policy";
    res.status(500).send("Failed to get privacy policy");
  }
};

export const httpGetTOS = async (req: Request, res: Response) => {
  try {
    res.status(200).json({ success: true, data: TOS });
  } catch (e) {
    res.statusMessage = "Failed to get TOS";
    res.status(500).send("Failed to get TOS");
  }
};

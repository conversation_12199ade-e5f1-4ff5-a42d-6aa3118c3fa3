import { Request, Response } from "express";
import { createShare } from "../../services/shareService/share.service";

export const httpCreateShareRecord = async (req: Request, res: Response) => {
  try {
    const sharedBy = req.user?._id;
    const { url, platform, contentType, contentId } = req.body;
    const newShare = await createShare({
      sharedBy,
      url,
      platform,
      contentId,
      contentType,
    });
    if (!newShare) {
      throw new Error("Failed to create share record");
    }

    res.status(201).json({
      success: true,
      message: "Share record created",
      data: newShare,
    });
  } catch (error: any) {
    console.error("Error creating share record:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Internal server error",
    });
  }
};

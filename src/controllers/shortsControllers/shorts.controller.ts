import { Request, Response } from "express";
import { ShortsService } from "../../services";
import { IShorts } from "../../types/schema";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  SHORT_COMMENT_MESSAGES,
  SHORT_LIKES_MESSAGES,
  SHORTS_MESSAGES,
} from "../../constants/responseMessage";
import { extractKeyFromPresignedUrl, getPublicUrlFromS3 } from "../../lib/s3";
import { AWS_S3_TEMP_BUCKET_NAME } from "../../config/environment";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import s3Client from "../../config/s3";
import {
  sendNewPostNotification,
  sendRecommendedContentNotification,
} from "../../models/notification/notification.model";

/**
 * Controller function for creating shorts
 * @param req
 * @param res
 * @returns
 */
export const httpCreateShorts = async (req: Request, res: Response) => {
  try {
    const authorId = req.user?._id;
    const url = req.body?.videoUrl;
    const key = extractKeyFromPresignedUrl(url);
    const publicUrl = getPublicUrlFromS3(AWS_S3_TEMP_BUCKET_NAME, key ?? "");
    const data = {
      ...req.body,
      author: authorId,
      videoUrl: publicUrl,
    };
    const result = await ShortsService.createShorts(data);
    await sendRecommendedContentNotification(
      authorId,
      String(result.data?._id),
      "short"
    );
    await sendNewPostNotification(authorId, String(result.data?._id), "short");

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpCreateShorts:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_CREATING
    );
  }
};

export const httpGeneratePreSignedUrlForShorts = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .json({ error: "Unauthorized" });
    }

    const url = await ShortsService.generatePreSignedUrl(userId);
    return res.status(STATUS_CODES.OK).json({ url });
  } catch (err: any) {
    console.error("Presign error:", err);
    return res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json({ error: err.message });
  }
};

/**
 * Controller to get shorts by id.
 * @param req
 * @param res
 * @returns
 */
export const httpGetShortsById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?._id;

    const result = await ShortsService.getShortsById(id, userId);

    if (!result.success) {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message
      );
    }

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetShortsById:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING_BY_ID
    );
  }
};

/**
 * controller function for updating shorts.
 * @param req
 * @param res
 * @returns
 */
export const httpDeleteShorts = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { id } = req.params;

    const result = await ShortsService.deleteShorts(id, userId);

    if (!result.success) {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message
      );
    }

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpDeleteShorts:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_DELETING
    );
  }
};

/**
 * controller function for getting shorts by author.
 * @param req
 * @param res
 * @returns
 */
export const httpGetShortsByAuthor = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNumber = Math.max(Number(page), 1);
    const pageSize = Math.max(Number(limit), 1);
    const skip = (pageNumber - 1) * pageSize;
    const { authorId } = req.params;
    const userId = req.user?._id;

    const result = await ShortsService.getShortsByAuthor(
      authorId,
      userId,
      skip,
      pageSize
    );

    if (!result.success) {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message
      );
    }

    const pagination = {
      page: pageNumber,
      limit: pageSize,
      totalItems: result.data?.length || 0,
      totalPages: Math.ceil((result.data?.length || 0) / pageSize),
      hasNextPage: pageNumber * pageSize < (result.data?.length || 0),
      hasPreviousPage: pageNumber > 1,
    };

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      { pagination, data: result.data }
    );
  } catch (error) {
    console.error("Error in httpGetShortsByAuthor:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING_BY_AUTHOR
    );
  }
};

/**
 * Controller function for getting shorts feed.
 * @param req
 * @param res
 * @returns
 */
export const httpGetShortsFeed = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 5;

    const result = await ShortsService.getShortsFeed(userId, page, limit);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetShortsFeed:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING_FEED
    );
  }
};

export const httpUpdateShorts = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const result = await ShortsService.updateShorts(id, updateData);

    if (!result.success) {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message
      );
    }

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpUpdateShorts:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_UPDATING
    );
  }
};

export const httpAddShortComment = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const shortId = req.params.id;
    const { shortAuthorId, content, replyTo } = req.body;

    const result = await ShortsService.addShortComment(
      shortId,
      shortAuthorId,
      userId,
      content,
      replyTo
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err: any) {
    console.error("Error in httpAddShortComment:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpDeleteShortComment = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const commentId = req.params.commentId;

    const result = await ShortsService.deleteShortComment(commentId, userId);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpDeleteShortComment:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpGetShortComments = async (req: Request, res: Response) => {
  try {
    const shortId = req.params.shortId;
    const page = Number(req.query.page) || 1;
    const limit = Number(req.query.limit) || 10;

    const result = await ShortsService.getShortComments(shortId, page, limit);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetShortComments:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpGetCommentReplies = async (req: Request, res: Response) => {
  try {
    const commentId = req.params.commentId;
    const page = Number(req.query.page) || 1;
    const limit = Number(req.query.limit) || 5;

    const result = await ShortsService.getCommentReplies(
      commentId,
      page,
      limit
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetCommentReplies:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_COMMENT_MESSAGES.INTERNAL_ERROR
    );
  }
};

export const httpLikeOrUnlikeShort = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const shortId = req.params.id;
    const { action } = req.body;

    const result = await ShortsService.likeOrUnlikeShort(
      shortId,
      userId,
      action
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpLikeOrUnlikeShort:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORT_LIKES_MESSAGES.ERROR_LIKE_ACTION
    );
  }
};

export const httpMarkShortViewed = async (req: Request, res: Response) => {
  try {
    const shortId = req.params.id;
    const userId = req.user?._id;
    const ipAddress = req.ip;
    const userAgent = req.headers["user-agent"];

    const result = await ShortsService.markShortViewed(
      shortId,
      userId,
      ipAddress,
      userAgent
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    const shortId = req.params.id;
    const userId = req.user?._id;
    console.error(
      `Error in httpMarkShortViewed for shortId ${shortId}, userId ${userId}:`,
      err
    );

    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_ADDING_VIEW
    );
  }
};


export const httpGetTrendingShorts = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await ShortsService.getTrendingShorts(page, limit);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetTrendingShorts:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING
    );
  }
};


export const httpGetPersonalizedRecommendations = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await ShortsService.fetchPersonalizedRecommendation(userId, page, limit);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Controller Error:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      SHORTS_MESSAGES.ERROR_FETCHING
    );
  }
};
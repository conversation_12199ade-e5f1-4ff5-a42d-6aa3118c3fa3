// Import all controllers and export them as a single object.
import * as FeaturedStreamController from "./streamControllers/featuredStream.controller";
import * as LiveStreamController from "./streamControllers/liveStream.controller";
import * as TrendingStreamController from "./streamControllers/trendingStream.controller";
import * as StreamController from "./streamControllers/stream.controller";
import * as NotInterestedStreamController from "./streamControllers/notInterested.controller";

//Imports from User controller
import * as UserController from "./userControllers/user.controller";
import * as ProfileController from "./userControllers/profile.controller";
import * as UserActivityController from "./userControllers/activity.controller";

//Imports from Auth controller
import * as AuthController from "./authControllers/auth.controller";
import * as SignupController from "./authControllers/signup.controller";
import * as InterestController from "./authControllers/interest.controller";
import * as PhoneAuthController from "./authControllers/phoneAuth.controller";
import * as SSOController from "./authControllers/sso.controller";

//story controllers
import * as StoryController from "./storyControllers/story.controller";

//shorts controllers
import * as ShortsController from "./shortsControllers/shorts.controller";

import * as ShareController from "./shareController/share.controller";

// playlist controllers
import * as PlaylistController from "./playlistControllers/playlist.controller";

// Not Interested controller
import * as NotInterestedController from "./notInterestedControllers/notInterested.controller"

// report controller

import * as ReportController from "./reportControllers/report.controller";
export {
  FeaturedStreamController,
  LiveStreamController,
  TrendingStreamController,
  StreamController,
  NotInterestedStreamController,
  UserController,
  ProfileController,
  UserActivityController,
  SignupController,
  StoryController,
  ShortsController,
  PlaylistController,
  InterestController,
  AuthController,
  PhoneAuthController,
  SSOController,
  ShareController,
  NotInterestedController,
  ReportController
};

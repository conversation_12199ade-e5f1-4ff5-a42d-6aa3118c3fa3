import { Request, Response } from "express";
import SubscriptionPlan from "../models/plan/plan.schema";
import { BillingInterval } from "../types/schema";
import stripe from "../config/stripe";
import Payment from "../models/payment/payment.schema";
import {
  createOrRetrieveStripeAccount,
  createOrRetrieveStripeCustomer,
} from "../lib/stripe";

export const httpCreatePlatformSubscriptionPlans = async (
  req: Request,
  res: Response
) => {
  try {
    const plans = [
      {
        title: "Normal Plan",
        description: "Basic access to the platform",
        billingInterval: "month" as BillingInterval,
        price: 5.0,
        currency: "usd",
        features: ["Basic Access", "Standard Streaming Quality"],
      },
      {
        title: "Premium Plan",
        description: "Premium access to the platform",
        billingInterval: "month" as BillingInterval,
        price: 15.0,
        currency: "usd",
        features: [
          "Premium Access",
          "High-Quality Streaming",
          "Ad-Free Experience",
        ],
      },
    ];

    for (const plan of plans) {
      const product = await stripe.products.create({
        name: plan.title,
        description: plan.description,
      });

      const stripePrice = await stripe.prices.create({
        unit_amount: plan.price * 100,
        currency: plan.currency,
        recurring: { interval: plan.billingInterval },
        product: product.id,
      });

      const newPlan = new SubscriptionPlan({
        creator: null,
        title: plan.title,
        description: plan.description,
        billingInterval: plan.billingInterval,
        price: plan.price,
        currency: plan.currency,
        features: plan.features,
        stripePriceId: stripePrice.id,
      });

      await newPlan.save();
    }

    res
      .status(201)
      .json({ message: "Platform subscription plans created successfully." });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpCreateSubscriptionPlan = async (
  req: Request,
  res: Response
) => {
  try {
    const creatorId = req.user?._id.toString();
    const { title, description, billingInterval, price, currency, features } =
      req.body;

    const product = await stripe.products.create({
      name: title,
      description,
    });

    const stripePrice = await stripe.prices.create({
      unit_amount: price * 100,
      currency,
      recurring: { interval: billingInterval },
      product: product.id,
    });

    const newPlan = new SubscriptionPlan({
      creator: creatorId,
      title,
      description,
      billingInterval,
      price,
      currency,
      features,
      stripePriceId: stripePrice.id,
    });

    await newPlan.save();

    res.status(201).json(newPlan);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpSubscribeToPlatformPlan = async (
  req: Request,
  res: Response
) => {
  try {
    const user = req.user!;
    const { planId } = req.body;

    const plan = await SubscriptionPlan.findById(planId);
    if (!plan) {
      return res.status(404).json({ error: "Plan not found" });
    }

    const customerId = await createOrRetrieveStripeCustomer(user);

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      customer: customerId,
      line_items: [
        {
          price: plan.stripePriceId,
          quantity: 1,
        },
      ],
      metadata: {
        planId: plan._id.toString(),
        isPlatform: "true",
      },
      success_url: `http://localhost:8080/subscription/success`,
      cancel_url: `http://localhost:8080/subscription/cancel`,
    });

    res.json({ id: session.id, url: session.url });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpSubscribeToPlan = async (req: Request, res: Response) => {
  try {
    const user = req.user!;
    const { planId } = req.body;

    const plan = await SubscriptionPlan.findById(planId);
    if (!plan) {
      return res.status(404).json({ error: "Plan not found" });
    }

    const customerId = await createOrRetrieveStripeCustomer(user);

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      customer: customerId,
      line_items: [
        {
          price: plan.stripePriceId,
          quantity: 1,
        },
      ],
      metadata: {
        planId: plan._id.toString(),
        isPlatform: "false",
      },
      success_url: `http://localhost:8080/subscription/success`,
      cancel_url: `http://localhost:8080/subscription/cancel`,
    });

    res.json({ id: session.id, url: session.url });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpGetSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    const { creator } = req.query;

    const plans = await SubscriptionPlan.find({
      creator: creator ? creator : null,
    });
    res.status(200).json({ plans });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpManageSubscription = async (req: Request, res: Response) => {
  try {
    const customer = await createOrRetrieveStripeCustomer(req.user!);
    const session = await stripe.billingPortal.sessions.create({
      customer: customer,
    });
    res.status(200).json({ url: session.url });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpWithdrawEarnings = async (req: Request, res: Response) => {
  try {
    const user = req.user!;
    const { amount } = req.body;

    const accountId = await createOrRetrieveStripeAccount(user);

    const totalEarnings = await Payment.aggregate([
      { $match: { user: user._id, status: "succeeded" } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    if (totalEarnings[0].total < amount) {
      return res.status(400).json({ error: "Insufficient balance" });
    }

    const transfer = await stripe.transfers.create({
      amount: amount * 100,
      currency: "usd",
      destination: accountId,
      description: `Withdrawal for user ${user.username}`,
    });

    res.json({ message: "Withdrawal successful", transfer });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpGetAvailableBalance = async (req: Request, res: Response) => {
  try {
    const user = req.user!;

    const totalEarnings = await Payment.aggregate([
      { $match: { user: user?._id, status: "succeeded" } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    const accountId = await createOrRetrieveStripeAccount(user);

    const totalWithdrawn = await stripe.transfers.list({
      destination: accountId,
    });

    const availableBalance =
      totalEarnings[0].total -
      totalWithdrawn.data.reduce((sum, transfer) => sum + transfer.amount, 0) /
        100;

    res.json({ availableBalance });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const httpOnboardCustomer = async (req: Request, res: Response) => {
  try {
    const user = req.user!;
    const accountId = await createOrRetrieveStripeAccount(user);

    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: "https://example.com/refresh",
      return_url: "https://example.com/return",
      type: "account_onboarding",
      collection_options: {
        fields: "eventually_due",
      },
    });

    res.status(200).json({ url: accountLink });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

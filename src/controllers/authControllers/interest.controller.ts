import { getAllInterests } from "../../models/interests/interests.model";
import {
  APPLE_AUTHENTICATION,
  INTEREST_MESSAGES,
  MESSAGES,
} from "../../constants/responseMessage";
import { Request, Response } from "express";
import { STATUS_CODES } from "../../constants/statusCodes";
import { UserService } from "../../services";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import UserInterest from "../../models/userInterest/userInterest.schema";

/**
 * Fetch interests list api on authentication screen.
 * @param req
 * @param res
 */
export const httpFetchInterestList = async (req: Request, res: Response) => {
  try {
    const interests = await getAllInterests();
    if (!interests) {
      res.status(STATUS_CODES.NOT_FOUND).json({
        success: false,
        messages: MESSAGES.NOT_FOUND,
        interests: [],
      });
    }
    res
      .status(STATUS_CODES.OK)
      .json({ success: true, messages: MESSAGES.SUCCESS, interests });
  } catch (error) {
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      messages: MESSAGES.INTERNAL_SERVER_ERROR,
      interests: [],
    });
  }
};

/**
 * Add user interests and mark profile as complete
 */
export const httpAddUserInterests = async (req: Request, res: Response) => {
  try {
    const { userId, interests } = req.body;
    if (!Array.isArray(interests) || interests.length === 0) {
      return res.status(STATUS_CODES.BAD_REQUEST).json({
        success: false,
        message: APPLE_AUTHENTICATION.INTERESTS,
      });
    }

    const user = await UserService.getUser(userId);
    if (!user) {
      return ErrorResponse(
        res,
        STATUS_CODES.NOT_FOUND,
        false,
        INTEREST_MESSAGES.USER_NOT_FOUND
      );
    }

    if (
      !user.onboardingSteps.emailVerified &&
      !user.onboardingSteps.phoneVerified
    ) {
      return ErrorResponse(
        res,
        STATUS_CODES.FORBIDDEN,
        false,
        INTEREST_MESSAGES.ISVERIFIED
      );
    }

    const existingInterestsDoc = await UserInterest.findOne({ userId });
    if (existingInterestsDoc) {
      existingInterestsDoc.interests = interests;
      await existingInterestsDoc.save();
    } else {
      await UserInterest.create({ userId, interests });
    }

    await user.save();

    return SuccessResponse(
      res,
      STATUS_CODES.OK,
      true,
      INTEREST_MESSAGES.SUCCESS
    );
  } catch (error) {
    console.error("Error adding user interests:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      INTEREST_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

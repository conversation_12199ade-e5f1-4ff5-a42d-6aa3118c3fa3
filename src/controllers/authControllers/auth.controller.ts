import { Request, Response } from "express";
import { UserService } from "../../services";
import OTP from "../../models/otp/otp.schema";
import { generateOTP } from "../../models/otp/otp.model";
import { sendMail } from "../../lib/mailer";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  APPLE_AUTHENTICATION,
  AUTHENTICATION,
  MESSAGES,
  USER_MESSAGES,
} from "../../constants/responseMessage";
import {
  ErrorResponse,
  generateToken,
  SuccessResponse,
} from "../../utils/helper";
import Device from "../../models/device/device.schema";
import { registerDevice } from "../../models/notification/notification.model";
import { getUserInterestsById } from "../../models/userInterest/userInterest.model";
import logger from "../../config/logger";
import { JWT_TOKEN_EXPIRY } from "../../constants/auth";

/**
 * Function to handle user logout.
 * We delete the device token from the database for the user.
 * @param req
 * @param res
 * @returns
 */
export const httpLogout = async (req: Request, res: Response) => {
  try {
    const { userId, fcmToken } = req.body;
    const response = await Device.findOneAndDelete({ userId, fcmToken });
    if (!response) {
      return ErrorResponse(
        res,
        STATUS_CODES.UNAUTHORIZED,
        false,
        AUTHENTICATION.UNAUTHORIZED,
        null
      );
    }

    return SuccessResponse(
      res,
      STATUS_CODES.OK,
      true,
      AUTHENTICATION.LOGOUT_SUCCESS,
      userId
    );
  } catch (error) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR,
      null
    );
  }
};

export const httpSendForgotPasswordOTP = async (
  req: Request,
  res: Response
) => {
  try {
    const { email } = req.body;

    const user = await UserService.getUserByEmail(email);
    if (!user) {
      return res.status(404).send("User not found");
    }

    const otp = generateOTP();
    await OTP.create({ email, otp });

    const emailContent = `Your OTP for resetting the password is: ${otp}. (valid for 5 minutes).`;
    await sendMail(email, "Password Reset OTP", emailContent);

    res.status(200).json({ success: true, message: "OTP sent successfully" });
  } catch (err) {
    res.statusMessage = "Error sending OTP";
    res.status(500).send("Error sending OTP");
  }
};

export const httpVerifyForgotPasswordOTP = async (
  req: Request,
  res: Response
) => {
  try {
    const { email, otp } = req.body;

    const otpDoc = await OTP.findOne({ email, otp });
    if (!otpDoc) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid or expired OTP" });
    }

    res
      .status(200)
      .send({ success: true, message: "OTP verified successfully" });
  } catch (err) {
    res.statusMessage = "Error verifying OTP";
    res.status(500).send("Error verifying OTP");
  }
};

export const httpResetPassword = async (req: Request, res: Response) => {
  try {
    const { email, otp, newPassword } = req.body;
    const otpDoc = await OTP.findOne({ email, otp });
    if (!otpDoc) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: "Invalid or expired OTP" });
    }

    const user = await UserService.getUserByEmail(email);
    if (!user) {
      return res
        .status(STATUS_CODES.NOT_FOUND)
        .json({ success: false, message: "User not found" });
    }

    user.password = newPassword;

    await user.save();

    await OTP.deleteOne({ email, otp });

    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: "Password reset successfully" });
  } catch (err) {
    console.log(err);
    res.statusMessage = "Error resetting password";
    res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json({ success: false, message: "Error resetting password" });
  }
};

/**
 * Login controller function to handle user login with username as well as email.
 * @param  req We accept login credentials.
 * @param  res HTTP response object used to send the response back to the client.
 * @returns
 */
export const httpLogin = async (req: Request, res: Response) => {
  try {
    // Device info for notification
    const { loginIdentifier, password, deviceInfo } = req.body;

    if (!loginIdentifier || !password) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: MESSAGES.INVALID_INPUT });
    }

    const user =
      (await UserService.getUserByEmail(loginIdentifier)) ||
      (await UserService.getUserByUsername(loginIdentifier));

    if (!user) {
      return res
        .status(STATUS_CODES.NOT_FOUND)
        .json({ success: false, message: MESSAGES.NOT_FOUND });
    }

    const isPasswordValid = await user.verifyPassword(password);
    if (!isPasswordValid) {
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .json({ success: false, message: AUTHENTICATION.WRONG_PASS });
    }

    if (deviceInfo) {
      await registerDevice({
        userId: user?._id,
        ...deviceInfo,
      });
    }

    const userInterests = (await getUserInterestsById(user?._id)) || [];
    const token = generateToken(
      { id: user._id, email: user.email },
      JWT_TOKEN_EXPIRY
    );
    const followersCount = user.followers.length;
    const followingCount = user.following.length;
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.followers;
    delete userResponse.following;

    return res.status(STATUS_CODES.OK).json({
      success: true,
      message: AUTHENTICATION.LOGIN_ACCOUNT,
      user: {
        ...userResponse,
        followersCount,
        followingCount,
        fcmToken: deviceInfo?.fcmToken,
        userInterests,
      },
      token,
    });
  } catch (error) {
    console.error("Login Error:", error);
    return res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json({ success: false, message: MESSAGES.INTERNAL_SERVER_ERROR });
  }
};

/**
 * Api for completing user profile.
 * @param req interests and userId
 * @param res updates user interests
 * @returns completesd profile
//  */
// export const httpCompleteUserProfile = async (req: Request, res: Response) => {
//   const { interests, username, password } = req.body;
//   const userId = req.user?._id;

//   try {
//     if (!userId) {
//       return res.status(STATUS_CODES.UNAUTHORIZED).json({
//         success: false,
//         message: APPLE_AUTHENTICATION.UNAUTHORIZED,
//       });
//     }

//     if (!Array.isArray(interests) || interests.length === 0) {
//       return res.status(STATUS_CODES.BAD_REQUEST).json({
//         success: false,
//         message: APPLE_AUTHENTICATION.INTERESTS,
//       });
//     }

//     const userProfileUpdated = await UserService.updateUserProfileCompletion(
//       userId
//     );
//     if (!userProfileUpdated) {
//       return res.status(STATUS_CODES.NOT_FOUND).json({
//         success: false,
//         message: APPLE_AUTHENTICATION.UNAUTHORIZED,
//       });
//     }

//     // Update or insert user's interests in UserInterestSchema
//     const updatedInterests = await updateOrInsertUserInterests(
//       userId,
//       interests
//     );

//     if (!updatedInterests) {
//       return res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
//         success: false,
//         message: "Failed to update or insert user interests",
//       });
//     }
//     // JWT token for app
//     const token = generateToken(
//       { id: userProfileUpdated?._id, email: userProfileUpdated?.email },
//       JWT_TOKEN_EXPIRY
//     );

//     const followersCount = userProfileUpdated?.followers.length;
//     const followingCount = userProfileUpdated?.following.length;
//     const userResponse = userProfileUpdated?.toObject();

//     delete userResponse.password;
//     delete userResponse.followers;
//     delete userResponse.following;

//     res.status(STATUS_CODES.OK).json({
//       success: true,
//       message: "Interests added successfully",
//       user: {
//         ...userResponse,
//         followersCount,
//         followingCount,
//         interests: updatedInterests?.interests,
//       },
//       token,
//     });
//   } catch (error) {
//     console.error("Error adding interests:", error);

//     res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
//       success: false,
//       message: MESSAGES.INTERNAL_SERVER_ERROR,
//     });
//   }
// };

export const httpSendOTPandUpdateEmail = async (
  req: Request,
  res: Response
) => {
  try {
    const { userId, email } = req.body;
    const user = await UserService.getUser(userId);
    if (!user) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        USER_MESSAGES.NOT_FOUND,
        user
      );
    }

    user.email = email;
    await user.save();

    const otp = generateOTP();
    await OTP.create({
      email,
      otp,
    });

    const emailContent = `Hello ${
      user.username ? user.username : ""
    },\n\nYour OTP for verifying your account is: ${otp}.\nIt will expire in 2 minutes.\n\nThank you!`;
    sendMail(email, "Verify Your Account", emailContent);
    return SuccessResponse(
      res,
      STATUS_CODES.CREATED,
      true,
      MESSAGES.SUCCESS,
      user
    );
  } catch (error: any) {
    logger.error(`Error during sending OTP on email: ${error.message}`);
    console.log(error);
    if (error.code === 11000) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        AUTHENTICATION.USER_EXISTS
      );
    }
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

export const httpCompleteUserProfile = async (req: Request, res: Response) => {
  const { userId, username, password } = req.body;

  try {
    if (!userId) {
      return res.status(STATUS_CODES.UNAUTHORIZED).json({
        success: false,
        message: APPLE_AUTHENTICATION.UNAUTHORIZED,
      });
    }

    const user = await UserService.getUser(userId);
    if (!user) {
      return res.status(STATUS_CODES.NOT_FOUND).json({
        success: false,
        message: APPLE_AUTHENTICATION.UNAUTHORIZED,
      });
    }

    user.username = username;
    user.password = password;
    (user.isProfileComplete = true), (user.verified = true);
    const userProfileUpdated = await user.save();

    // JWT token for app
    const token = generateToken(
      { id: userProfileUpdated?._id, email: userProfileUpdated?.email },
      JWT_TOKEN_EXPIRY
    );

    const followersCount = userProfileUpdated?.followers.length;
    const followingCount = userProfileUpdated?.following.length;
    const userResponse = userProfileUpdated?.toObject();

    delete userResponse.password;
    delete userResponse.followers;
    delete userResponse.following;

    const userInterests =
      (await getUserInterestsById(userProfileUpdated?._id)) || [];
    res.status(STATUS_CODES.OK).json({
      success: true,
      message: "Profile completed successfully",
      user: {
        ...userResponse,
        followersCount,
        followingCount,
        userInterests,
      },
      token,
    });
  } catch (error) {
    console.error("Error adding interests:", error);

    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

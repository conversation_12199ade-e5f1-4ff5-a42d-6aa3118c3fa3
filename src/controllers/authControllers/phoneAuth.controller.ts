import { Request, Response } from "express";
import User from "../../models/user/user.schema";
import { admin } from "../../config/firebase";
import { registerDevice } from "../../models/notification/notification.model";
import { ErrorResponse, generateToken } from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  APPLE_AUTHENTICATION,
  USER_MESSAGES,
} from "../../constants/responseMessage";
import { EUserProvider } from "../../types/enum";
import { JWT_TOKEN_EXPIRY } from "../../constants/auth";

// We can user this API for signin and signup both as it creates user if not present
export const httpSignUpWithNumber = async (req: Request, res: Response) => {
  const { userId, idToken, deviceInfo } = req.body;
  try {
    if (!idToken && !deviceInfo) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        "Id token and device info are required"
      );
    }
    if (!admin) {
      return ErrorResponse(
        res,
        STATUS_CODES.INTERNAL_SERVER_ERROR,
        false,
        "Firebase not configured for development environment"
      );
    }
    const decoded = await admin.auth().verifyIdToken(idToken);
    const { uid } = decoded;
    const phoneNumber = decoded.phone_number;
    if (!phoneNumber) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        "Phone number not found from token"
      );
    }

    let user = await User.findOne({ phoneNumber });
    if (user) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        "User with this phone number already exists"
      );
    }

    // if user does not exist with this phone number in DB, find user by userId provided from frontend
    if (!user && userId) {
      user = await User.findById(userId);
    }

    if (user) {
      user.phoneNumber = phoneNumber;
      user.onboardingSteps.phoneVerified = true;
      await user.save();
    } else {
      user = await User.create({
        provider: { provider: EUserProvider.phone, id: uid },
        phoneNumber: phoneNumber,
        onboardingSteps: { phoneVerified: true },
      });
    }

    if (deviceInfo) {
      await registerDevice({
        userId: user?._id,
        ...deviceInfo,
      });
    }
    const followersCount = user?.followers.length;
    const followingCount = user?.following.length;
    const userResponse = user?.toObject();

    res.status(STATUS_CODES.OK).json({
      success: true,
      message: APPLE_AUTHENTICATION.LOGIN_ACCOUNT,
      user: {
        ...userResponse,
        followersCount,
        followingCount,
      },
    });
  } catch (error) {
    console.log("Error signing in with number:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: APPLE_AUTHENTICATION.SIGNIN_FAILED,
      user: {},
      token: "",
    });
  }
};

// We can user this API for signin and signup both as it creates user if not present
export const httpSignInWithNumber = async (req: Request, res: Response) => {
  const { idToken, deviceInfo } = req.body;
  try {
    if (!idToken) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        "Id token is required"
      );
    }

    if (!admin) {
      return ErrorResponse(
        res,
        STATUS_CODES.INTERNAL_SERVER_ERROR,
        false,
        "Firebase not configured for development environment"
      );
    }
    const decoded = await admin.auth().verifyIdToken(idToken);
    const phoneNumber = decoded.phone_number;
    if (!phoneNumber) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        "Phone number not found from token"
      );
    }

    let user = await User.findOne({ phoneNumber });
    if (!user) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        USER_MESSAGES.NOT_FOUND,
        user
      );
    }
    if (!user.onboardingSteps?.phoneVerified) {
      user.onboardingSteps.phoneVerified = true;
      await user.save();
    }

    if (!user.onboardingSteps.phoneVerified || !user.verified) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        USER_MESSAGES.NOT_VERIFIED,
        user
      );
    }

    if (deviceInfo) {
      await registerDevice({
        userId: user?._id,
        ...deviceInfo,
      });
    }

    const token = generateToken(
      { id: user?._id, email: user?.email },
      JWT_TOKEN_EXPIRY
    );
    const followersCount = user?.followers.length;
    const followingCount = user?.following.length;
    const userResponse = user?.toObject();

    res.status(STATUS_CODES.OK).json({
      success: true,
      message: APPLE_AUTHENTICATION.LOGIN_ACCOUNT,
      user: {
        ...userResponse,
        followersCount,
        followingCount,
      },
      token: token,
    });
  } catch (error) {
    console.log("Error signing in with number:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: APPLE_AUTHENTICATION.SIGNIN_FAILED,
      user: {},
      token: "",
    });
  }
};

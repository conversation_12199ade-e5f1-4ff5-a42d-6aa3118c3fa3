import { GoogleLoginRequestBody } from "../../types/requests/AuthRequests";
import { LoginResponse } from "../../types/responses/AuthResponse";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  APPLE_AUTHENTICATION,
  AUTHENTICATION,
  MESSAGES,
  USER_MESSAGES,
} from "../../constants/responseMessage";
import {
  ErrorResponse,
  generateToken,
  SuccessResponse,
  validateAppleToken,
} from "../../utils/helper";
import { NextFunction, Request, Response } from "express";
import { UserService } from "../../services";
import {
  addUserInterests,
  getUserInterestsById,
} from "../../models/userInterest/userInterest.model";
import { registerDevice } from "../../models/notification/notification.model";
import { JWT_TOKEN_EXPIRY } from "../../constants/auth";
import { IUser } from "../../types/schema";

/**
 * API for APPLE sso
 * @param req identityToken and device info(fcmToken)
 * @param res user and token
 * @returns
 */
export const httpAppleSignInController = async (
  req: Request,
  res: Response
) => {
  const { identityToken, deviceInfo, givenName, familyName } = req.body;
  if (!identityToken) {
    return res
      .status(STATUS_CODES.OK)
      .json({ success: false, message: APPLE_AUTHENTICATION.IDENTITY_TOKEN });
  }

  try {
    const verifiedToken = await validateAppleToken(identityToken);
    const { sub: appleId, email, name } = verifiedToken as any;
    if (!email || !appleId) {
      return res.status(STATUS_CODES.BAD_REQUEST).json({
        success: false,
        message: "Apple authentication failed. Missing email or appleId.",
      });
    }

    let user = await UserService.getUserByAppProvider(appleId, "apple");
    if (!user) {
      let newUserData: Partial<IUser> | null = null;
      // check if deleted user exists with same email
      let isAppleUserDeleted = await UserService.getDeletedAppleUserByEmail(
        email,
        appleId
      );
      if (isAppleUserDeleted) {
        newUserData = {
          username: email.split("@")[0] || isAppleUserDeleted.username,
          email: email || isAppleUserDeleted.email,
          displayName: name || isAppleUserDeleted.displayName || "",
          provider: { provider: "apple", id: appleId },
          lastLogin: new Date(),
        };
      } else {
        newUserData = {
          username: email.split("@")[0],
          email,
          displayName: name || "",
          provider: { provider: "apple", id: appleId },
          lastLogin: new Date(),
        };
      }
      user = await UserService.createUser(newUserData);
    } else {
      if (user && user.provider?.provider !== "apple") {
        return res.status(STATUS_CODES.BAD_REQUEST).json({
          success: false,
          message: AUTHENTICATION.PROVIDER_RESTRICTION,
        });
      }

      // Update last login timestamp
      user.lastLogin = new Date();
      await user.save();
    }

    if (deviceInfo) {
      await registerDevice({
        userId: user?._id,
        ...deviceInfo,
      });
    }

    // JWT token for app
    const token = generateToken(
      { id: user?._id, email: user?.email },
      JWT_TOKEN_EXPIRY
    );

    const followersCount = user?.followers.length;
    const followingCount = user?.following.length;
    const userResponse = user?.toObject();
    delete userResponse.password;
    delete userResponse.followers;
    delete userResponse.following;

    res.status(STATUS_CODES.OK).json({
      success: true,
      message: APPLE_AUTHENTICATION.LOGIN_ACCOUNT,
      user: {
        ...userResponse,
        followersCount,
        followingCount,
      },
      token: token,
    });
  } catch (error) {
    console.error(error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: APPLE_AUTHENTICATION.SIGNIN_FAILED,
      user: {},
      token: "",
    });
  }
};

/**
 * In following function we handle Google login or signup functionality.
 * If the user exists, we log them in and return a token.
 * If the user doesn't exist, then we create a new account, log them in, and return a token.
 *
 * @param {GoogleLoginRequestBody} req - The HTTP request object containing user data from the client.
 * @param {LoginResponse} res - The HTTP response object used to send the response back to the client.
 * @returns A JSON response object with the user details and a JWT token.
 */
export const httpLoginWithGoogle = async (
  req: Request<{}, {}, GoogleLoginRequestBody>,
  res: Response<LoginResponse>,
  next: NextFunction
): Promise<Response> => {
  try {
    // const { email, displayName, username, photo, providerType } = req.body;

    const {
      email,
      displayName,
      username,
      photo,
      providerType,
      deviceInfo,
      interests,
    } = req.body;

    if (!email) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: AUTHENTICATION.EMAIL_REQUIRED });
    }
    if (!["email", "google", "facebook", "apple"].includes(providerType)) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: AUTHENTICATION.PROVIDER_TYPE });
    }

    let user = await UserService.getUserByEmail(email);

    if (user && user.provider?.provider !== providerType) {
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: AUTHENTICATION.PROVIDER_RESTRICTION });
    }

    let isNewUser = false;

    if (!user) {
      isNewUser = true;
      user = await UserService.createUser({
        username,
        email,
        profilePicture: photo,
        displayName,
        provider: { provider: providerType },
        verified: true,
      });
    }

    if (interests && interests.length > 0) {
      await addUserInterests(user?._id, interests);
    }

    if (deviceInfo) {
      await registerDevice({
        userId: user?._id,
        ...deviceInfo,
      });
    }

    const userInterests = (await getUserInterestsById(user?._id)) || {
      interests: [],
    };

    const token = generateToken(
      { id: user?._id, email: user?.email },
      JWT_TOKEN_EXPIRY
    );
    const followersCount = user?.followers.length;
    const followingCount = user?.following.length;
    const userResponse = user?.toObject();
    delete userResponse.password;
    delete userResponse.followers;
    delete userResponse.following;

    // Return response
    const message = isNewUser
      ? AUTHENTICATION.CREATE_ACCOUNT
      : AUTHENTICATION.LOGIN_ACCOUNT;

    return res.status(STATUS_CODES.OK).json({
      success: true,
      message,
      user: {
        ...userResponse,
        followersCount,
        followingCount,
        fcmToken: deviceInfo.fcmToken,
        interests: userInterests?.interests,
      },
      token,
    });
  } catch (error) {
    console.error(error);
    return res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json({ success: false, message: MESSAGES.INTERNAL_SERVER_ERROR });
  }
};

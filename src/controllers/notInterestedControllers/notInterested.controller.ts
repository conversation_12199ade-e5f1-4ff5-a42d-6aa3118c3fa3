import { Request, Response } from "express";
import { NotInterestedService } from "../../services";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { INotInterested } from "../../types/schema";
import { STATUS_CODES } from "../../constants/statusCodes";
import { NOT_INTERESTED_MESSAGES } from "../../constants/responseMessage";



export const httpAddToNotInterested = async (
    req: Request,
    res: Response
) => {
    try {
        const userId = req.user?._id;
        const { contentId, contentType } = req.body;

        const result = await NotInterestedService.addToNotInterested(
            userId,
            contentId,
            contentType as "stream" | "short"
        );

        return SuccessResponse<INotInterested>(
            res,
            result.statusCode,
            result.success,
            result.message,
            result.data
        );
    } catch (error) {
        console.error("Error in httpAddToNotInterested:", error);
        return ErrorResponse<INotInterested>(
            res,
            STATUS_CODES.INTERNAL_SERVER_ERROR,
            false,
            NOT_INTERESTED_MESSAGES.INTERNAL_SERVER_ERROR,
            null
        );
    }
};

export const httpRemoveFromNotInterested = async (
    req: Request,
    res: Response
) => {
    try {
        const userId = req.user?._id;
        const { contentId, contentType } = req.body;

        const result = await NotInterestedService.removeFromNotInterested(
            userId,
            contentId,
            contentType as "stream" | "short"
        );

        return SuccessResponse<INotInterested>(
            res,
            result.statusCode,
            result.success,
            result.message,
            result.data
        );
    } catch (error) {
        console.error("Error in httpRemoveFromNotInterested:", error);
        return ErrorResponse<INotInterested>(
            res,
            STATUS_CODES.INTERNAL_SERVER_ERROR,
            false,
            NOT_INTERESTED_MESSAGES.INTERNAL_SERVER_ERROR,
            null
        );
    }
};
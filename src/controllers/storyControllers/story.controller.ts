import { Request, Response } from "express";
import { ObjectId } from "mongoose";
import {
  createStory,
  getStoriesByFollowings,
  getUserStories,
  markStoryAsViewed,
} from "../../models/story/story.model";
import { IUser } from "../../types/schema";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import { StoryService } from "../../services";
import { STATUS_CODES } from "../../constants/statusCodes";
import { STORY_MESSAGES } from "../../constants/responseMessage";

/**
 * Controller function for accepting req and response for uploading stories
 * @param req
 * @param res
 * @returns
 */
export const httpUploadStory = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { media, caption, location, textOverlays, doodleOverlays } = req.body;
    const result = await StoryService.createStory(
      userId,
      media,
      caption,
      location,
      textOverlays,
      doodleOverlays
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpCreateStory:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_CREATING
    );
  }
};

/**
 * controller function for fetching stories uploaded by ther user
 * @param req
 * @param res
 * @returns
 */
export const httpGetUserStories = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    const result = await StoryService.getUserStories(userId);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (err) {
    console.error("Error in httpGetUserStories:", err);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_FETCHING
    );
  }
};

/**
 * Controller function for fetching stories of a user by their ID
 * @param req
 * @param res
 * @returns
 */
export const httpGetUserStoriesById = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const result = await StoryService.getUserStoriesById(userId);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetUserStoriesById:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_FETCHING
    );
  }
};

/**
 * Following controller function is reponsible for,
 * fetching stories of all the creators user is following
 * @param req
 * @param res
 * @returns
 */
export const httpGetStoriesByFollowings = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user?._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await StoryService.getStoriesByFollowings(
      userId,
      page,
      limit
    );

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpGetStoriesByFollowings:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_FETCHING
    );
  }
};

/**
 * Following controller function is responsible for marking a story as viewed.
 * @param req
 * @param res
 * @returns
 */
export const httpMarkStoryAsViewed = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { storyId } = req.body;

    const result = await StoryService.markStoryAsViewed(storyId, userId);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    console.error("Error in httpMarkStoryAsViewed:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_VIEWING
    );
  }
};

export const httpGeneratePreSignedUrlForStories = async (
  req: Request,
  res: Response
) => {
  try {
    const userId = req.user!._id;
    const { contentType } = req.body;

    const result = await StoryService.generateStoryPreSignedUrl(
      userId,
      contentType
    );

    const { url, key } = result;
    return res.status(STATUS_CODES.OK).json({ url, key });
  } catch (err: any) {
    console.error("Story presign error:", err);
    return res
      .status(STATUS_CODES.INTERNAL_SERVER_ERROR)
      .json({ error: err.message });
  }
};

// Mark a story as viewed
// export const httpMarkStoryAsViewed = async (req: Request, res: Response) => {
//   if (!req.user)
//     return res.status(401).json({ success: false, error: "Unauthorized" });

//   const { storyId } = req.params;

//   try {
//     const { alreadyViewed, story } = await markStoryAsViewed(
//       storyId,
//       req.user.id
//     );

//     if (alreadyViewed) {
//       return res.status(200).json({
//         success: true,
//         message: "Story already viewed",
//         data: story,
//       });
//     }

//     res.status(200).json({
//       success: true,
//       message: "Story marked as viewed successfully",
//       data: story,
//     });
//   } catch (error) {
//     console.error("Error in httpMarkStoryAsViewed:", error);
//     res.status(500).json({
//       success: false,
//       error: "Error marking story as viewed",
//     });
//   }
// };

// Get stories of followers
// export const httpGetStoriesByFollowings = async (
//   req: Request,
//   res: Response
// ) => {
//   if (!req.user)
//     return res.status(401).json({ success: false, error: "Unauthorized" });

//   try {
//     // Convert follower ObjectIds to hexadecimal strings
//     const followerIds = req.user.following.list.map((user: IUser) =>
//       user._id.toHexString()
//     );

//     const stories = await getStoriesByFollowings(followerIds);

//     res.status(200).json({
//       success: true,
//       message: "Stories retrieved successfully",
//       data: stories,
//     });
//   } catch (error) {
//     console.error("Error:", error);
//     res.status(500).json({
//       success: false,
//       error: "Error retrieving stories by followers",
//     });
//   }
// };

// Get user stories
// export const httpGetUserStories = async (req: Request, res: Response) => {
//   if (!req.user)
//     return res.status(401).json({ success: false, error: "Unauthorized" });

//   try {
//     const stories = await getUserStories(req.user._id);
//     res.status(200).json({
//       success: true,
//       message: "User stories retrieved successfully",
//       data: stories,
//     });
//   } catch (error) {
//     res
//       .status(500)
//       .json({ success: false, error: "Error retrieving user stories" });
//   }
// };

// Get User Stories by ID
// export const httpGetUserStoriesById = async (req: Request, res: Response) => {
//   if (!req.user)
//     return res.status(401).json({ success: false, error: "Unauthorized" });
//   const { storyId } = req.params;
//   try {
//     const stories = await getUserStories(storyId);
//     res.status(200).json({
//       success: true,
//       message: "User stories retrieved successfully",
//       data: stories,
//     });
//   } catch (error) {
//     res
//       .status(500)
//       .json({ success: false, error: "Error retrieving user stories" });
//   }
// };

import { Request, Response } from "express";
import User from "../models/user/user.schema";
import Keyword from "../models/keyword/keyword.schema";
import Stream from "../models/stream/stream.schema";
import { ErrorResponse, SuccessResponse } from "../utils/helper";
import { MESSAGES } from "../constants/responseMessage";
import { STATUS_CODES } from "../constants/statusCodes";
import mongoose from "mongoose";
import ShortsModel from "../models/shorts/shorts.schema";

export const httpSearch = async (req: Request, res: Response) => {
  const { keyword } = req.query;
  const userId = req.user?._id;

  try {
    const users = await User.aggregate([
      {
        $match: {
          $or: [
            { username: { $regex: keyword, $options: "i" } },
            { fullName: { $regex: keyword, $options: "i" } },
          ],
          isDeleted: false,
        },
      },
      {
        $project: {
          username: 1,
          fullName: 1,
          profilePicture: 1,
          displayName: 1,
          verified: 1,
          followingCount: { $size: "$following" },
          followersCount: { $size: "$followers" },
        },
      },
      {
        $sort: { followersCount: -1 },
      },
      {
        $limit: 5, // Limit results to 5
      },
    ]);

    const streams = await Stream.aggregate([
      {
        $match: {
          $or: [
            { title: { $regex: keyword, $options: "i" } },
            { description: { $regex: keyword, $options: "i" } },
            { tags: { $in: [new RegExp(keyword as string, "i")] } },
          ],
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          description: 1,
          cover: 1,
          tags: 1,
          createdAt: 1,
          transcodedUrl: 1,
          url: 1,
          videoLength: 1,
          isLive: 1,
          vodStatus: 1,
          endedAt: 1,
          viewsCount: { $size: "$views" },
          likesCount: { $size: "$likes" },
          comments: { $size: "$comments" },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            fullName: "$creatorDetails.fullName",
            profilePicture: "$creatorDetails.profilePicture",
            displayName: "$creatorDetails.displayName",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
      {
        $sort: {
          "creatorDetails.followers.length": -1,
        },
      },
      {
        $limit: 5,
      },
    ]);

    const shorts = await ShortsModel.aggregate([
      {
        $match: {
          $or: [
            { description: { $regex: keyword, $options: "i" } },
            { tags: { $in: [new RegExp(keyword as string, "i")] } },
            { "audioDetails.title": { $regex: keyword, $options: "i" } },
            { "audioDetails.artist": { $regex: keyword, $options: "i" } },
          ],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "author",
          foreignField: "_id",
          as: "authorDetails",
        },
      },
      { $unwind: { path: "$authorDetails", preserveNullAndEmptyArrays: true } },
      {
        $project: {
          _id: 1,
          description: 1,
          thumbnailUrl: 1,
          videoUrl: 1,
          audioDetails: 1,
          tags: 1,
          createdAt: 1,
          likesCount: { $size: "$likes" },
          commentsCount: { $size: "$comments" },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          author: {
            _id: "$authorDetails._id",
            username: "$authorDetails.username",
            fullName: "$authorDetails.fullName",
            profilePicture: "$authorDetails.profilePicture",
            displayName: "$authorDetails.displayName",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$authorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
      { $sort: { likesCount: -1 } },
      { $limit: 5 },
    ]);
    // Update keyword statistics
    const existingKeyword = await Keyword.findOneAndUpdate(
      { keyword: { $regex: `^${keyword}$`, $options: "i" }, userId },
      { $inc: { count: 1 } },
      { new: true }
    );

    if (!existingKeyword) {
      const keywordCount = await Keyword.countDocuments({ userId });
      if (keywordCount < 5) {
        await Keyword.create({ keyword, count: 1, userId });
      } else {
        const leastSearched = await Keyword.findOne({ userId }).sort("count");
        if (leastSearched) {
          await Keyword.findByIdAndDelete(leastSearched._id);
          await Keyword.create({ keyword, count: 1, userId });
        }
      }
    }
    return SuccessResponse(res, STATUS_CODES.OK, true, "Search results", {
      users,
      streams,
      shorts,
    });
  } catch (error) {
    console.error("Error during search:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

export const httpOptimizedSearch = async (req: Request, res: Response) => {
  const keyword = req.query.keyword as string;
  const userId = req.user?._id;
  const regex = new RegExp(keyword.trim(), "i");

  try {
    // Use Promise.allSettled to handle individual failures
    const [usersResult, streamsResult, shortsResult] = await Promise.allSettled(
      [
        getMatchingUsers(regex),
        getMatchingStreams(regex, userId),
        getMatchingShorts(regex, userId),
      ]
    );

    // Extract values or provide empty arrays for failed promises
    const users = usersResult.status === "fulfilled" ? usersResult.value : [];
    const streams =
      streamsResult.status === "fulfilled" ? streamsResult.value : [];
    const shorts =
      shortsResult.status === "fulfilled" ? shortsResult.value : [];

    // Log any failures for monitoring/debugging
    if (usersResult.status === "rejected")
      console.error("Error fetching users:", usersResult.reason);
    if (streamsResult.status === "rejected")
      console.error("Error fetching streams:", streamsResult.reason);
    if (shortsResult.status === "rejected")
      console.error("Error fetching shorts:", shortsResult.reason);

    // Only update search history if at least one search succeeded
    if (
      [usersResult, streamsResult, shortsResult].some(
        (result) => result.status === "fulfilled"
      )
    ) {
      await updateSearchKeywordHistory(keyword, userId).catch((err) =>
        console.error("Error updating search history:", err)
      );
    }


    
    return SuccessResponse(res, STATUS_CODES.OK, true, "Search results", {
      users,
      streams,
      shorts,
    });
  } catch (error) {
    console.error("Error during search:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

export const httpGetTrendingSearches = async (req: Request, res: Response) => {
  const userId = req.user?._id;
  try {
    const results = await Keyword.find({ userId }).sort({ count: -1 }).limit(5);
    const keywords = results.map((result) => result.keyword);
    return SuccessResponse(
      res,
      STATUS_CODES.OK,
      true,
      "Trending searches",
      keywords
    );
  } catch (error) {
    console.error("Error fetching trending searches:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * This function fetches the top 5 trending searches globally
 * and returns the top 5 keywords and the streams that match those keywords
 * (sorted by newest first, limited to 10 streams)
 * @param req
 * @param res
 */
export const httpGetGlobalTrendingSearches = async (
  req: Request,
  res: Response
) => {
  try {
    const topKeywords = await Keyword.aggregate([
      {
        $group: {
          _id: { $toLower: "$keyword" },
          totalSearches: { $sum: "$count" },
        },
      },
      {
        $sort: { totalSearches: -1 },
      },
      {
        $limit: 5,
      },
    ]);
    const keywords = topKeywords.map((k) => k._id);

    const matchedStreams = await Stream.find({
      isDeleted: false,
      $or: [
        { title: { $in: keywords.map((kw) => new RegExp(kw, "i")) } },
        { description: { $in: keywords.map((kw) => new RegExp(kw, "i")) } },
        { tags: { $in: keywords } },
      ],
    })
      .sort({ createdAt: -1 })
      .limit(10)
      .populate("creator", "username profilePicture displayName")
      .lean();
    const data = {
      keywords,
      streams: matchedStreams,
    };

    return SuccessResponse(
      res,
      STATUS_CODES.OK,
      true,
      "Trending streams fetched successfully",
      data
    );
  } catch (error) {
    console.error("Error fetching trending streams:", error);

    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

const getMatchingUsers = async (regex: RegExp) => {
  return User.aggregate([
    {
      $match: {
        $or: [{ username: { $regex: regex } }, { fullName: { $regex: regex } }],
        isDeleted: false,
      },
    },
    {
      $project: {
        username: 1,
        fullName: 1,
        profilePicture: 1,
        displayName: 1,
        verified: 1,
        followingCount: { $size: "$following" },
        followersCount: { $size: "$followers" },
      },
    },
    { $sort: { followersCount: -1 } },
    { $limit: 5 },
  ]);
};

const getMatchingStreams = async (regex: RegExp, userId: string) => {
  return Stream.aggregate([
    {
      $match: {
        $or: [
          { title: { $regex: regex } },
          { description: { $regex: regex } },
          { tags: { $in: [regex] } },
        ],
        isDeleted: false,
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "creator",
        foreignField: "_id",
        as: "creatorDetails",
      },
    },
    { $unwind: { path: "$creatorDetails", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 1,
        title: 1,
        description: 1,
        cover: 1,
        tags: 1,
        createdAt: 1,
        transcodedUrl: 1,
        url: 1,
        videoLength: 1,
        isLive: 1,
        vodStatus: 1,
        endedAt: 1,
        viewsCount: { $size: "$views" },
        likesCount: { $size: "$likes" },
        comments: { $size: "$comments" },
        isLiked: {
          $in: [
            new mongoose.Types.ObjectId(userId),
            { $ifNull: ["$likes", []] },
          ],
        },
        creator: {
          _id: "$creatorDetails._id",
          username: "$creatorDetails.username",
          fullName: "$creatorDetails.fullName",
          profilePicture: "$creatorDetails.profilePicture",
          displayName: "$creatorDetails.displayName",
          isFollowing: {
            $in: [
              new mongoose.Types.ObjectId(userId),
              { $ifNull: ["$creatorDetails.followers", []] },
            ],
          },
        },
      },
    },
    { $sort: { "creatorDetails.followers.length": -1 } },
    { $limit: 5 },
  ]);
};

const getMatchingShorts = async (regex: RegExp, userId: string) => {
  return ShortsModel.aggregate([
    {
      $match: {
        $or: [
          { description: { $regex: regex } },
          { tags: { $in: [regex] } },
          { "audioDetails.title": { $regex: regex } },
          { "audioDetails.artist": { $regex: regex } },
        ],
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "author",
        foreignField: "_id",
        as: "authorDetails",
      },
    },
    { $unwind: { path: "$authorDetails", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 1,
        description: 1,
        thumbnailUrl: 1,
        videoUrl: 1,
        audioDetails: 1,
        tags: 1,
        createdAt: 1,
        likesCount: 1,
        commentsCount: 1,
        isLiked: {
          $in: [
            new mongoose.Types.ObjectId(userId),
            {
              $cond: {
                if: { $isArray: "$likes" },
                then: "$likes",
                else: [],
              },
            },
          ],
        },
        author: {
          _id: "$authorDetails._id",
          username: "$authorDetails.username",
          fullName: "$authorDetails.fullName",
          profilePicture: "$authorDetails.profilePicture",
          displayName: "$authorDetails.displayName",
          isFollowing: {
            $in: [
              new mongoose.Types.ObjectId(userId),
              { $ifNull: ["$authorDetails.followers", []] },
            ],
          },
        },
      },
    },
    { $sort: { likesCount: -1 } },
    { $limit: 5 },
  ]);
};

const updateSearchKeywordHistory = async (keyword: string, userId: string) => {
  const existingKeyword = await Keyword.findOneAndUpdate(
    { keyword: { $regex: `^${keyword}$`, $options: "i" }, userId },
    { $inc: { count: 1 } },
    { new: true }
  );

  if (!existingKeyword) {
    const keywordCount = await Keyword.countDocuments({ userId });
    if (keywordCount < 5) {
      await Keyword.create({ keyword, count: 1, userId });
    } else {
      const leastSearched = await Keyword.findOne({ userId }).sort("count");
      if (leastSearched) {
        await Keyword.findByIdAndDelete(leastSearched._id);
        await Keyword.create({ keyword, count: 1, userId });
      }
    }
  }
};

import { NextFunction, Request, Response } from "express";
import { UserService } from "../services";
import { STATUS_CODES } from "../constants/statusCodes";
import logger from "../config/logger";

export const authenticateStartStream = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.info("Start stream authentication request:", { body: req.body });
  const { name } = req.body;
  console.log("name", name);
  console.log("----------------------------------------");
  console.log(req.body);
  console.log("----------------------------------------");

  if (!name) {
    return res
      .status(STATUS_CODES.BAD_REQUEST)
      .send("Unauthorized: name field missing");
  }

//Todo: create a authenticated middle watre jwt is 

  try {
    const [userId, liveStreamId] = name.split("_");
    const user = await UserService.getUser(userId);
    logger.info("User found for stream start:", { userId, username: user?.username });
    if (!user) {
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .send("Unauthorized: User not found");
    }

    req.user = user;
    next();
  } catch (err) {
    logger.error("Start stream verification error:", err);
    return res
      .status(STATUS_CODES.UNAUTHORIZED)
      .send("Unauthorized: Invalid User");
  }
};

export const authenticateEndStream = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.info("End stream authentication request:", { body: req.body });
  const { name } = req.body;

  if (!name) {
    return res
      .status(STATUS_CODES.BAD_REQUEST)
      .send("Unauthorized: name field missing");
  }

  try {
    const [userId, liveStreamId] = name.split("_");
    const user = await UserService.getUser(userId);
    logger.info("User found for stream end:", { userId, username: user?.username });
    if (!user) {
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .send("Unauthorized: User not found");
    }

    req.user = user;

    next();
  } catch (err) {
    logger.error("End stream token verification error:", err);
    return res
      .status(STATUS_CODES.UNAUTHORIZED)
      .send("Unauthorized: Invalid user");
  }
};

import jwt from "jsonwebtoken";
import { JWT_SECRET } from "../config/environment";
import { UserService } from "../services";
import { Socket } from "socket.io";
import logger from "../config/logger";

const authenticateSocket = async (socket: Socket, next: any) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.token;

    if (!token) {
      return next(new Error("Unauthorized"));
    }

    // Verify JWT and get decoded payload in one step
    const decoded: any = jwt.verify(token, JWT_SECRET);
    
    // Use user ID from JWT payload for efficient database lookup
    const user = await UserService.getUser(decoded.id);

    if (!user) {
      return next(new Error("Unauthorized"));
    }

    socket.user = user;
    next();
  } catch (error) {
    logger.error("Socket authentication error:", error);
    next(new Error("Authentication error"));
  }
};

export default authenticateSocket;

import { Request, Response, NextFunction } from "express";
import Stream from "../models/stream/stream.schema";
import { IStream, IUser } from "../types/schema";

export const checkStreamAccess = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const user = req.user;

    const stream: IStream | null = await Stream.findById(id).populate(
      "creator"
    );

    if (!stream) {
      return res.status(404).json({ message: "Stream not found" });
    }

    const visibility = stream.settings?.visibility;
    console.log(visibility);
    

    switch (visibility) {
      case "everyone":
        return next();

      case "followers":
        const creator = stream.creator as unknown as IUser;
        if (user && creator.followers.includes(user._id)) {
          return next();
        }
        return res.status(403).json({
          message: "Access denied. Only followers can view this content.",
        });

      //   case "subscribers":
      //     if (user && user.subscriptions.includes(stream.creator._id)) {
      //       return next();
      //     }
      //     return res.status(403).json({
      //       message: "Access denied. Only subscribers can view this content.",
      //     });

      case "none":
        return res.status(403).json({ message: "This content is private." });

      default:
        return res.status(403).json({ message: "Access denied." });
    }
  } catch (error) {
    console.error("Error during stream access control:", error);
    res.status(500).send("Server error");
  }
};

import { Schema, model } from "mongoose";
import { IProblemReport } from "../../types/schema";

const problemReportSchema = new Schema<IProblemReport>({
  user: { type: Schema.Types.ObjectId, ref: "User", required: true },
  category: { type: String, required: true },
  message: { type: String, required: true },
  images: { type: [String], maxlength: 5 },
  timestamp: { type: Date, default: Date.now },
});

const ProblemReport = model<IProblemReport>(
  "ProblemReport",
  problemReportSchema
);

export default ProblemReport;

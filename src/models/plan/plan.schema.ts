import { Schema, model } from "mongoose";
import { ISubscriptionPlan } from "../../types/schema";

const SubscriptionPlanSchema = new Schema<ISubscriptionPlan>({
  creator: { type: Schema.Types.ObjectId, ref: "User", default: null },
  title: { type: String, required: true },
  description: { type: String },
  billingInterval: {
    type: String,
    enum: ["month", "year"],
    required: true,
  },
  price: { type: Number, required: true },
  currency: { type: String, required: true },
  features: [{ type: String }],
  stripePriceId: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
});

const SubscriptionPlan = model<ISubscriptionPlan>(
  "SubscriptionPlan",
  SubscriptionPlanSchema
);

export default SubscriptionPlan;

import { Schema, model, Types } from "mongoose";
import { ITrendingShortScore } from "../../types/schema";

const TrendingShortScoreSchema = new Schema<ITrendingShortScore>({
    shortId: {
        type: Types.ObjectId,
        ref: "Shorts",
        required: true,
        unique: true,
    },
    trendingScore: {
        type: Number,
        required: true,
        default: 0,
    },
});

TrendingShortScoreSchema.index({ trendingScore: -1 });

const TrendingShortScore = model<ITrendingShortScore>(
    "TrendingShortScore",
    TrendingShortScoreSchema
);

export default TrendingShortScore;

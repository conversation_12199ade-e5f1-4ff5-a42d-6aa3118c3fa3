import { Schema, model, Types, Document } from "mongoose";

export interface IProfileView extends Document {
  viewer: Types.ObjectId;
  target: Types.ObjectId;
  viewedAt: Date;
}

const ProfileViewSchema = new Schema<IProfileView>(
  {
    viewer: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    target: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    viewedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

ProfileViewSchema.index({ viewer: 1, target: 1 }, { unique: true });

export const ProfileView = model<IProfileView>(
  "ProfileView",
  ProfileViewSchema
);

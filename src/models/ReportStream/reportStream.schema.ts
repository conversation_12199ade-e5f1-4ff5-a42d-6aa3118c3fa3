import { Schema, model, Types, Document } from "mongoose";
import { IReport } from "../../types/schema";

export enum REPORT_ENTITY_TYPE {
  STREAM = "stream",
  SHORT = "short",
  // Add more types as needed in the future
}

export enum REPORT_STREAM_ENUM {
  SPAM = "Spam or misleading",
  ADULT = "Sexual content",
  INAPPROPRIATE = "Inappropriate hateful or abusive content",
  HARMFUL = "Harmful or dangerous acts",
  VIOLENT = "Violent or repulsive content",
}

export enum REPORT_STATUS {
  PENDING = "pending",
  REVIEWED = "reviewed",
  RESOLVED = "resolved",
  REJECTED = "rejected",
}

const ReportSchema = new Schema<IReport>(
  {
    entityId: { type: Types.ObjectId, required: true },
    entityType: {
      type: String,
      required: true,
      enum: Object.values(REPORT_ENTITY_TYPE),
    },
    reporter: { type: Types.ObjectId, ref: "User", required: true },
    reason: {
      type: String,
      required: true,
      enum: Object.values(REPORT_STREAM_ENUM),
    },
    description: { type: String },
    status: {
      type: String,
      enum: Object.values(REPORT_STATUS),
      default: REPORT_STATUS.PENDING,
      required: true,
    },
    adminFeedback: { type: String },
    reviewedBy: { type: Types.ObjectId, ref: "User" },
    reviewedAt: { type: Date },
  },
  { timestamps: true }
);

const Report = model<IReport>("Report", ReportSchema);

export default Report;

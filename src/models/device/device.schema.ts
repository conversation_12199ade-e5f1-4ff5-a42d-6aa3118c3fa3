import { Schema, model, Types } from "mongoose";
import { IDevice, PlatformType } from "../../types/schema";

const DeviceSchema = new Schema<IDevice>(
  {
    platform: {
      type: String,
      enum: Object.values(PlatformType),
      required: true,
    },
    OSVersion: { type: String, required: false },
    active: { type: Boolean, default: true },
    userId: { type: Types.ObjectId, ref: "User", required: true },
    fcmToken: { type: String, required: true },
  },
  { timestamps: true }
);

const Device = model<IDevice>("Device", DeviceSchema);

export default Device;

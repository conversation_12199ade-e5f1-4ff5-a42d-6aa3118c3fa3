import { model, Schema } from "mongoose";
import { IShare } from "../../types/schema";
import { EShareContentModel } from "../../types/enum";

const shareSchema = new Schema<IShare>(
  {
    sharedBy: { type: Schema.Types.ObjectId, ref: "User", required: true },
    url: { type: String, required: true },
    platform: { type: String, required: false },
    contentId: {
      type: Schema.Types.ObjectId,
      refPath: "contentType",
      default: null,
    },
    contentType: {
      type: String,
      enum: EShareContentModel,
      default: null,
    },
  },
  { timestamps: true }
);

const ShareModel = model<IShare>("Shares", shareSchema);
export default ShareModel;

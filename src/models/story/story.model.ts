import StoryModel from "./story.schema";
import { IStory, IUser } from "../../types/schema";

// Create a new story
export const createStory = async (
  storyData: Partial<IStory>
): Promise<IStory> => {
  const story = new StoryModel(storyData);
  return await story.save();
};

// Get stories for a user
export const getUserStories = async (userId: string) => {
  return await StoryModel.find({
    user: userId,
    expiresAt: { $gte: new Date() },
  })
    .sort({ createdAt: -1 })
    .populate("user");
};

// Get stories of all followings
export const getStoriesByFollowings = async (userIds: string[]) => {
  try {
    return await StoryModel.find({
      user: { $in: userIds },
      expiresAt: { $gte: new Date() },
    })
      .sort({ createdAt: -1 })
      .populate("user");
  } catch (error) {
    console.error("Error fetching stories of followings:", error);
    throw new Error("Failed to fetch stories of followings");
  }
};

// Mark a story as viewed
import mongoose from "mongoose";

export const markStoryAsViewed = async (storyId: string, viewerId: string) => {
  try {
    // Fetch the story by ID
    const story = await StoryModel.findById(storyId);

    if (!story) {
      throw new Error("Story not found");
    }

    // Check if viewer is already in the viewBy array
    const alreadyViewed = story.viewBy.some((user: IUser) =>
      user._id.equals(viewerId)
    );

    if (alreadyViewed) {
      return { alreadyViewed: true, story };
    }

    // Add the viewer to the viewBy array
    const updatedStory = await StoryModel.findByIdAndUpdate(
      storyId,
      { $push: { viewBy: viewerId } },
      { new: true }
    );

    return { alreadyViewed: false, story: updatedStory };
  } catch (error: any) {
    console.error("Error in markStoryAsViewed:", error);
    throw new Error(error.message || "Error marking story as viewed");
  }
};

// Remove expired stories
export const deleteExpiredStories = async () => {
  try {
    const result = await StoryModel.deleteMany({
      expiresAt: { $lt: new Date() },
    });
    return result;
  } catch (error) {
    console.error("Error deleting expired stories:", error);
    throw new Error("Failed to delete expired stories");
  }
};

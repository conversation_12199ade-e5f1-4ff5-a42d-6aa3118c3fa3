import { Schema, model } from "mongoose";
import { IDoodleOverlay, IDoodlePath, IStory, ITextOverlay } from "../../types/schema";

// TextOverlaySchema
const TextOverlaySchema = new Schema<ITextOverlay>(
  {
    id: { type: String, required: true },
    text: { type: String, required: true },
    font: { type: String, required: true },
    color: { type: String, required: true },
    position: {
      x: { type: Number, required: true },
      y: { type: Number, required: true },
    },
    scale: { type: Number, required: true },
    rotation: { type: Number, required: true },
  },
  { _id: false } // prevent automatic _id in subdocuments
);

// DoodlePathSchema
const DoodlePathSchema = new Schema<IDoodlePath>(
  {
    path: { type: String, required: true },
    color: { type: String, required: true },
    strokeWidth: { type: Number, required: true },
  },
  { _id: false }
);

// DoodleOverlaySchema
const DoodleOverlaySchema = new Schema<IDoodleOverlay>(
  {
    id: { type: String, required: true },
    paths: [DoodlePathSchema],
    scale: { type: Number, required: true },
    rotation: { type: Number, required: true },
  },
  { _id: false }
);

const StorySchema = new Schema<IStory>({
  user: { type: Schema.Types.ObjectId, ref: "User", required: true },
  media: [{ type: String, required: true }],
  caption: { type: String },
  location: {
    type: {
      coordinates: {
        lat: { type: String },
        lng: { type: String },
      },
      address: { type: String },
    },
  },
  viewBy: [{ type: Schema.Types.ObjectId, ref: "User" }],
  createdAt: { type: Date, default: Date.now },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000),
  },
  textOverlays: [TextOverlaySchema],
  doodleOverlays: [DoodleOverlaySchema],
});

const StoryModel = model<IStory>("Story", StorySchema);

export default StoryModel;

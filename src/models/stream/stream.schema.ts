import { Schema, model, Types, Document } from "mongoose";
import { IStream } from "../../types/schema";

const StreamSettingsSchema = new Schema(
  {
    visibility: {
      type: String,
      enum: ["followers", "everyone", "subscribers", "none"],
      required: true,
    },
  },
  { _id: false }
);

const StreamSchema = new Schema<IStream>(
  {
    creator: { type: Types.ObjectId, ref: "User", required: true },
    url: { type: String, default: "" },
    transcodedUrl: { type: String, default: "" },
    type: { type: String, enum: ["vr", "video", "video-live"], required: true },
    vodStatus: {
      type: String,
      enum: ["processing", "ready", "under-review", "rejected"],
      default: "processing",
    },
    status: {
      type: String,
      enum: ["uploading", "draft", "published", "ended", "uploaded"],
      required: true,
    },
    rtmpUrl: { type: String, default: "" },
    streamKey: { type: String, default: "" },
    liveStreamId: { type: String, default: null },
    liveUrl: { type: String, default: "" },
    liveUrls: { type: Map, of: String, default: {} },
    vodUrls: {
      type: Map,
      of: String,
      default: {},
    },
    isLive: { type: Boolean, default: false },
    title: { type: String, required: true },
    description: { type: String, default: "" },
    category: { type: String, default: "" },
    tags: [{ type: String }],
    cover: { type: String, default: "" },
    views: [{ type: Types.ObjectId, ref: "User" }],
    likes: [{ type: Types.ObjectId, ref: "User" }],
    reactions: [{ type: Types.ObjectId, ref: "Reaction" }],
    comments: [{ type: Types.ObjectId, ref: "Comment" }],

    sharesCount: { type: Number, default: 0 },
    settings: {
      type: StreamSettingsSchema,
      default: { visibility: "everyone" },
    },
    isDeleted: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    endedAt: { type: Date },
    videoLength: { type: Number, default: 0 },
    moderationDetails: {
      reviewedBy: String,
      reviewTimestamp: Date,
      reason: String,
    },
  },
  { timestamps: true }
);
StreamSchema.index({ creator: 1, isDeleted: 1, isLive: 1, status: 1, type: 1 });
const Stream = model<IStream>("Stream", StreamSchema);

export default Stream;

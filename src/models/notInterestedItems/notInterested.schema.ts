import { Schema, model, Types, Document } from "mongoose";
import { INotInterested } from "../../types/schema";



/**
 * Following is the updated schema
 */

const NotInterestedSchema = new Schema<INotInterested>(
  {
    userId: { type: Types.ObjectId, ref: "User", required: true },

    contentId: {
      type: Types.ObjectId,
      required: true,
    },

    contentType: {
      type: String,
      enum: ["stream", "short"],
      required: true,
    },

    reason: { type: String }, // Optional for future extension

    createdAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

NotInterestedSchema.index({ userId: 1, contentId: 1, contentType: 1 }, { unique: true });


export const NotInterested = model<INotInterested>(
  "NotInterested",
  NotInterestedSchema
);
import { model, Schema } from "mongoose";
import { IPayment } from "../../types/schema";

const PaymentSchema = new Schema<IPayment>({
  user: { type: Schema.Types.ObjectId, ref: "User", required: true },
  amount: { type: Number, required: true },
  currency: { type: String, required: true },
  paymentMethod: { type: String, required: true },
  status: { type: String, required: true },
  stripePaymentIntentId: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
});

const Payment = model<IPayment>("Payment", PaymentSchema);

export default Payment;

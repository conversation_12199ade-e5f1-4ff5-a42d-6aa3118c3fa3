// models/playlist/playlist.schema.ts
import { Schema, model, Types, Document } from "mongoose";

export interface IPlaylistItem {
  itemId: Types.ObjectId;
  itemType: "stream" | "short";
}

export interface IPlaylist extends Document {
  title: string;
  description?: string;
  createdBy: Schema.Types.ObjectId;
  items: IPlaylistItem[];
  createdAt: Date;
  updatedAt: Date;
}

const PlaylistItemSchema = new Schema<IPlaylistItem>(
  {
    itemId: { type: Schema.Types.ObjectId, required: true },
    itemType: {
      type: String,
      enum: ["stream", "short"],
      required: true,
    },
  },
  { _id: false }
);

const PlaylistSchema = new Schema<IPlaylist>(
  {
    title: { type: String, required: true },
    description: { type: String },
    createdBy: { type: Types.ObjectId, ref: "User", required: true },
    items: [PlaylistItemSchema],
  },
  { timestamps: true }
);

export const Playlist = model<IPlaylist>("Playlist", PlaylistSchema);

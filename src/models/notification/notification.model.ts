// services/notification.service.ts
import { messaging } from "../../config/firebase";
import {
  FirebaseMessage,
  INotification,
  SendNotificationParams,
} from "../../types/schema";
import Device from "../device/device.schema";
import User from "../user/user.schema";
import Notification from "./notification.schema";
import mongoose, { Types } from "mongoose";
import { ENotificationContentModel, ENotificationType } from "../../types/enum";
import { isUserNotificationSettingOn } from "../../services/userService/user.service";
import Stream from "../stream/stream.schema";
import UserInterest from "../userInterest/userInterest.schema";
import ShortsModel from "../shorts/shorts.schema";
import StoryModel from "../story/story.schema";

/**
 * Generate notification content based on type
 */
const getNotificationContent = (
  type: ENotificationType,
  senderName: string,
  message: string
): FirebaseMessage => {
  let title: string;
  let body: string;

  switch (type) {
    case "newFollower":
      title = "New Follower";
      body = `${senderName} started following you`;
      break;
    case "commentReply":
      title = "New Comment Reply";
      body = `${senderName} replied: ${message}`;
      break;
    case "like":
      title = "New Like";
      body = `${senderName} liked your stream`;
      break;
    case "streamUpdate":
      title = "Stream Update";
      body = `${senderName} started a new stream`;
      break;
    case "newMessage":
      title = "New Message";
      body = `${senderName}: ${message}`;
      break;
    default:
      title = "New Notification";
      body = message;
  }

  return {
    title,
    body,
  };
};

/**
 * Send Firebase FCM notification
 */
const sendFirebaseNotification = async (
  fcmToken: string,
  notificationContent: FirebaseMessage
): Promise<boolean> => {
  try {
    const firebaseMessage = {
      notification: {
        title: notificationContent.title,
        body: notificationContent.body,
      },
      token: fcmToken,
    };

    if (!messaging) {
      console.warn('Firebase messaging not available, skipping push notification');
      return true; // Return true for development mode
    }
    await messaging.send(firebaseMessage);
    return true;
  } catch (error) {
    console.error(
      `Error sending Firebase notification for ${fcmToken}:`,
      error
    );
    return false;
  }
};

/**
 * Create notification in database
 */
const createDbNotification = async ({
  userId,
  personId,
  type,
  contentId,
  message = "",
  commentId,
  contentType,
}: SendNotificationParams) => {
  try {
    return await Notification.create({
      user: new Types.ObjectId(userId),
      person: new Types.ObjectId(personId),
      type,
      message: type === "commentReply" || type === "newMessage" ? message : "",
      ...(contentId && { contentId: new Types.ObjectId(contentId) }),
      ...(commentId && { comment: new Types.ObjectId(commentId) }),
      isRead: false,
      contentType,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error("Error creating database notification:", error);
    throw error;
  }
};

/**
 * Updated notification sendNotification function
 * Using Device schema for sending notification this will help in managing multiple devices.
 */
export const sendNotification = async ({
  userId,
  personId,
  type,
  contentId,
  message = "",
  commentId,
  contentType,
}: SendNotificationParams): Promise<boolean> => {
  try {
    if (String(userId) === String(personId)) {
      console.log(
        `Receiver ${userId} is the same as sender ${personId}, skipping notification`
      );
      return false;
    }
    // check if user notification setting is on for particular type
    const isNotificationAllowed = await isUserNotificationSettingOn(
      userId,
      type
    );
    if (!isNotificationAllowed) {
      console.log(
        `User ${userId} has notification setting for ${type} off, skipping notification`
      );
      return false;
    }

    const [devices, sender] = await Promise.all([
      Device.find({ userId, active: true }).select("fcmToken").lean().exec(),
      User.findById(personId).select("username").lean().exec(),
    ]);

    if (!sender) {
      console.warn(`Person ${personId} not found`);
      return false;
    }

    await createDbNotification({
      userId,
      personId,
      type,
      contentId,
      message,
      commentId,
      contentType,
    });
    if (devices.length > 0) {
      const notificationContent = getNotificationContent(
        type,
        sender.username,
        message
      );

      await Promise.all(
        devices.map((device) =>
          sendFirebaseNotification(device.fcmToken, notificationContent)
        )
      );
    } else {
      console.log(
        `User ${userId} has no active devices, skipping push notifications`
      );
    }

    return true;
  } catch (error) {
    console.error("Error in sendNotification:", error);
    return false;
  }
};

const getNotificationContentType = (
  notificationType: "short" | "stream" | "story"
) => {
  switch (notificationType) {
    case "short":
      return ENotificationContentModel.short;
    case "stream":
      return ENotificationContentModel.stream;
    case "story":
      return ENotificationContentModel.story;
  }
};

// when user upload any post, send notification to all users whose interest matches with post interests
export const sendRecommendedContentNotification = async (
  userId: string,
  postId: string,
  type: "short" | "stream"
): Promise<boolean> => {
  try {
    const post =
      type === "short"
        ? await ShortsModel.findById(postId)
        : await Stream.findById(postId);
    if (!post) {
      console.log(`${type} with id ${postId} not found`);
      return false;
    }

    // Get all users who have recommendedContent notifications enabled
    // and are not the post author
    const interestedUsers = await UserInterest.find({
      userId: { $ne: userId }, // Exclude the post author
      interests: {
        $elemMatch: {
          category: post.category,
        },
      },
    }).populate({
      path: "userId",
      match: {
        isDeleted: false,
        "profileSettings.notifications.recommendedContent": true,
      },
      select: "_id",
    });

    // Extract users where population was successful
    const eligibleUsers = interestedUsers.map((u) => u.userId).filter(Boolean);
    if (eligibleUsers.length === 0) {
      console.log(
        "No interested users found to send recommended content notification"
      );
      return true;
    }

    console.log(
      `Sending recommended content notification to ${eligibleUsers.length} users`
    );

    await Promise.all(
      eligibleUsers.map((u: any) => {
        sendNotification({
          userId: String(u._id),
          personId: userId,
          type: ENotificationType.recommendedContent,
          contentId: postId,
          contentType:
            type === "short"
              ? ENotificationContentModel.short
              : ENotificationContentModel.stream,
        });
      })
    );
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

// when user upload any post, send notification to all followers whose post notification setting is on
export const sendNewPostNotification = async (
  userId: string,
  postId: string,
  type: "short" | "stream" | "story"
): Promise<boolean> => {
  try {
    const user = await User.findById(userId)
      .populate({
        path: "followers",
        match: {
          isDeleted: false,
          "profileSettings.notifications.post": true,
        },
        select: "_id",
      })
      .select("_id followers");

    if (!user) {
      console.log("Author user not found - ", userId);
      return false;
    }

    if (!user?.followers || user?.followers?.length === 0) {
      console.log(
        `User ${userId} has no followers. Skipping sending new post notifications to followers`
      );
      return false;
    }

    console.log(
      `Sending new post notification to ${user.followers.length} users`
    );

    await Promise.all(
      user.followers.map((u: any) => {
        sendNotification({
          userId: String(u._id),
          personId: userId,
          type: ENotificationType.post,
          contentId: postId,
          contentType: getNotificationContentType(type),
        });
      })
    );
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

/**
 * Following function is responsible for registering the device
 */
export const registerDevice = async ({
  userId,
  fcmToken,
  platform,
  OSVersion,
}: {
  userId: string;
  fcmToken: string;
  platform: string;
  OSVersion?: string;
}): Promise<void> => {
  try {
    await Device.updateMany({ fcmToken }, { active: false });

    await Device.findOneAndUpdate(
      { userId, fcmToken },
      { platform, OSVersion, active: true },
      { upsert: true, new: true }
    );
  } catch (error) {
    console.error("Error in registerDevice:", error);
  }
};

const getPopulatedNotifications = async (
  notifications: Array<INotification>
) => {
  // Manually populate contentId based on contentType
  return await Promise.all(
    notifications.map(async (notification) => {
      if (notification.contentId && notification.contentType) {
        const { contentId, contentType } = notification;
        try {
          let content = null;
          switch (contentType) {
            case ENotificationContentModel.short:
              content = await ShortsModel.findById(contentId);
              break;
            case ENotificationContentModel.stream:
              content = await Stream.findById(contentId);
              break;
            case ENotificationContentModel.story:
              content = await StoryModel.findById(contentId);
              break;
          }

          return { ...notification, content: content };
        } catch (error) {
          console.warn(
            `Failed to populate ${notification.contentType}:`,
            error
          );
          return notification;
        }
      }
      return notification;
    })
  );
};

/**
 * Get user notifications with pagination
 */
export const getUserNotifications = async (
  userId: string,
  onlyUnread = false,
  page = 1,
  limit = 20
) => {
  try {
    const query = { user: new Types.ObjectId(userId) };
    if (onlyUnread) {
      Object.assign(query, { isRead: false });
    }

    const [notifications, total] = await Promise.all([
      Notification.find(query)
        .populate("person", "username profilePicture")
        .populate("comment", "content")
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean()
        .exec(),
      Notification.countDocuments(query),
    ]);

    const populatedNotifications = await getPopulatedNotifications(
      notifications
    );

    return {
      notifications: populatedNotifications,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total,
    };
  } catch (error) {
    console.error("Error fetching user notifications:", error);
    throw error;
  }
};

/**
 * Mark notifications as read
 */
export const markAsRead = async (
  userId: string,
  notificationIds?: string[]
) => {
  try {
    const query = { user: new Types.ObjectId(userId) };
    if (notificationIds?.length) {
      Object.assign(query, {
        _id: { $in: notificationIds.map((id) => new Types.ObjectId(id)) },
      });
    }

    await Notification.updateMany(query, { $set: { isRead: true } });

    return true;
  } catch (error) {
    console.error("Error marking notifications as read:", error);
    throw error;
  }
};

/**
 * Get unread notifications count
 */
export const getUnreadCount = async (userId: string): Promise<number> => {
  try {
    return await Notification.countDocuments({
      user: new Types.ObjectId(userId),
      isRead: false,
    });
  } catch (error) {
    console.error("Error getting unread count:", error);
    return 0;
  }
};

import { Schema, model, Types } from "mongoose";
import { INotification } from "../../types/schema";
import { ENotificationContentModel, ENotificationType } from "../../types/enum";

const NotificationSchema = new Schema<INotification>(
  {
    user: { type: Schema.Types.ObjectId, ref: "User", required: true },
    type: {
      type: String,
      enum: ENotificationType,
      required: true,
    },
    message: { type: String, default: "" }, //only when its a comment reply or message
    isRead: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    person: { type: Schema.Types.ObjectId, ref: "User", required: true },
    comment: { type: Schema.Types.ObjectId, ref: "Comment", default: null },
    contentId: {
      type: Schema.Types.ObjectId,
      refPath: "contentType",
      default: null,
    },
    contentType: {
      type: String,
      enum: ENotificationContentModel,
      default: null,
    },
  },
  { timestamps: true }
);
const Notification = model<INotification>("Notification", NotificationSchema);

export default Notification;

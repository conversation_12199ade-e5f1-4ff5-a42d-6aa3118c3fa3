import mongoose, { Schema, Document } from "mongoose";
import { IShorts } from "../../types/schema";

const ShortsSchema: Schema = new Schema<IShorts>(
  {
    description: { type: String, required: true },
    videoUrl: { type: String, required: false },
    thumbnailUrl: {
      type: String,
      required: true,
    },
    audioDetails: {
      audioId: {
        type: String,
      },
      title: { type: String },
      artist: { type: String },
      duration: { type: Number },
    },
    shortDuration: { type: Number, required: true },
    tags: { type: [String], required: true },
    author: { type: Schema.Types.ObjectId, ref: "User", required: true },
    likesCount: { type: Number, default: 0 },
    saved: { type: [Schema.Types.ObjectId], ref: "User", default: [] },
    commentCount: { type: Number, default: 0 },
    viewsCount: { type: Number, default: 0 },
    location: {
      type: {
        coordinates: {
          lat: { type: String },
          lng: { type: String },
        },
        address: { type: String },
      },
    },
    reportedBy: [{ type: Schema.Types.ObjectId, ref: "User" }],
    sharesCount: { type: Number, default: 0 },
    interactionScore: { type: Number, default: 0 },
    category: { type: String, required: false },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);
ShortsSchema.index({ author: 1 });
const ShortsModel = mongoose.model<IShorts>("Shorts", ShortsSchema);

export default ShortsModel;

import { Schema, model, Types } from "mongoose";

export interface IShortComment extends Document {
  user: Schema.Types.ObjectId;
  shortId: Schema.Types.ObjectId;
  shortAuthorId: Schema.Types.ObjectId;
  content: string;
  parentId?: Schema.Types.ObjectId;
  replies: Schema.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

const ShortCommentSchema = new Schema<IShortComment>(
  {
    user: { type: Types.ObjectId, ref: "User", required: true },
    shortId: { type: Types.ObjectId, ref: "Short", required: true },
    shortAuthorId: { type: Types.ObjectId, ref: "User", required: true },
    content: { type: String, required: true },
    parentId: { type: Types.ObjectId, ref: "ShortComment", default: null },
    replies: [{ type: Types.ObjectId, ref: "ShortComment" }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

const ShortComment = model<IShortComment>("ShortComment", ShortCommentSchema);

export default ShortComment;

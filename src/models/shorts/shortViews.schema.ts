import { Schema, model, Types, Document } from "mongoose";

export interface IShortView extends Document {
  shortId: Schema.Types.ObjectId;
  userId?: Schema.Types.ObjectId; 
  viewedAt?: Date;
  ipAddress?: string;
  userAgent?: string;
}

const ShortViewSchema = new Schema<IShortView>(
  {
    shortId: { type: Types.ObjectId, ref: "Short", required: true },
    userId: { type: Types.ObjectId, ref: "User", required: false },
    viewedAt: { type: Date, default: Date.now },
    ipAddress: { type: String },
    userAgent: { type: String },
  },
  { timestamps: true }
);

// Ensure uniqueness for logged-in users, allow anonymous duplicates (sparse)
ShortViewSchema.index({ shortId: 1, userId: 1 }, { unique: true, sparse: true });

const ShortView = model<IShortView>("ShortView", ShortViewSchema);
export default ShortView;

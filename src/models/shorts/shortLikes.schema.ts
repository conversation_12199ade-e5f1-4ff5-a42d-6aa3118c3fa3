import { Schema, model, Types, Document } from "mongoose";

export interface IShortLike extends Document {
  shortId: Schema.Types.ObjectId;
  userId: Schema.Types.ObjectId;
  createdAt?: Date;
}

const ShortLikeSchema = new Schema<IShortLike & Document>(
  {
    shortId: {
      type: Types.ObjectId,
      ref: "Short",
      required: true,
    },
    userId: {
      type: Types.ObjectId,
      ref: "User",
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

ShortLikeSchema.index({ shortId: 1, userId: 1 }, { unique: true });

const ShortLike = model<IShortLike & Document>("ShortLike", ShortLikeSchema);

export default ShortLike;

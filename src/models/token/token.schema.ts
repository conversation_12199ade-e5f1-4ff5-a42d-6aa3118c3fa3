import { Schema, model } from "mongoose";

import { IEmailVerificationToken } from "../../types/schema";

const emailVerificationTokenSchema = new Schema<IEmailVerificationToken>({
  user: { type: Schema.Types.ObjectId, ref: "User", required: true },
  token: { type: String, required: true },
  createdAt: { type: Date, required: true, default: Date.now, expires: 3600 },
});

const EmailVerificationToken = model<IEmailVerificationToken>("EmailVerificationToken", emailVerificationTokenSchema);

export default EmailVerificationToken;

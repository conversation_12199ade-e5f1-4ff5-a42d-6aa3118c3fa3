import { IUser } from "../../types/schema";
import EmailVerificationToken from "./token.schema";
import crypto from "crypto";

export const createEmailVerificationToken = async (
  user: string
): Promise<string> => {
  const token = crypto.randomBytes(16).toString("hex");
  const newToken = await EmailVerificationToken.create({ user, token });
  return newToken.token;
};

export const getUserByToken = async (token: string): Promise<IUser | null> => {
  const tokenDocument = await EmailVerificationToken.findOne({
    token,
  }).populate("user");
  if (!tokenDocument) return null;
  const user = tokenDocument.user as unknown as IUser;
  return user;
};

import { Schema, model } from "mongoose";
import { IStreamView } from "../../types/schema";

const StreamViewSchema = new Schema<IStreamView>({
  user: { type: Schema.Types.ObjectId, ref: "User", required: true },
  stream: { type: Schema.Types.ObjectId, ref: "Stream", required: true },
  minutesWatched: { type: Number, required: true },
  socketId: { type: String, required: false },
  lastHeartbeat: { type: Date, default: Date.now },
  lastUpdated: { type: Date, default: Date.now },
});

const StreamView = model<IStreamView>("StreamView", StreamViewSchema);

export default StreamView;

import { Schema, model, Types } from "mongoose";
import { IComment } from "../../types/schema";

const CommentSchema = new Schema<IComment>(
  {
    user: { type: Types.ObjectId, ref: "User", required: true },
    stream: { type: Types.ObjectId, ref: "Stream", required: true },
    content: { type: String, required: true },
    parentId: { type: Types.ObjectId, ref: "Comment", default: null },
    replies: [{ type: Types.ObjectId, ref: "Comment" }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

const Comment = model<IComment>("Comment", CommentSchema);

export default Comment;

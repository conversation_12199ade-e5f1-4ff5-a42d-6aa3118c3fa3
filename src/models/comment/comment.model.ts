import Comment from "./comment.schema";
import { Types } from "mongoose";

/**
 * Get comments for a stream with pagination
 * @param streamId - The ID of the stream
 * @param page - Current page number (default = 1)
 * @param limit - Number of items per page (default = 10)
 * @returns {Promise<{comments: any[], totalComments: number}>} Paginated comments and total count
 */
export const getCommentsByStream = async (
  streamId: string,
  page: number = 1,
  limit: number = 10
): Promise<{ comments: any[]; totalComments: number }> => {
  // Validate the streamId
  if (!Types.ObjectId.isValid(streamId)) {
    return { comments: [], totalComments: 0 };
  }

  // Fetch paginated comments
  const comments = await Comment.find({ stream: streamId })
    .populate({
      path: "user",
      select: "username displayName profilePicture",
    })
    .skip((page - 1) * limit)
    .limit(limit)
    .sort({ createdAt: -1 })
    .lean();

  const totalComments = await Comment.countDocuments({ stream: streamId });

  return { comments, totalComments };
};

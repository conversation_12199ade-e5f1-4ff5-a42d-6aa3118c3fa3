import mongoose, { Schema, Document } from "mongoose";

export interface ISavedItem extends Document {
  userId: mongoose.Types.ObjectId;
  itemId: mongoose.Types.ObjectId;
  itemType: "stream" | "short";
  savedAt: Date;
}

const SavedItemSchema = new Schema<ISavedItem>(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    itemId: { type: Schema.Types.ObjectId, required: true },
    itemType: {
      type: String,
      enum: ["stream", "short"],
      required: true,
    },
    savedAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

SavedItemSchema.index({ userId: 1, itemId: 1, itemType: 1 }, { unique: true });
export const SavedItem = mongoose.model<ISavedItem>(
  "SavedItem",
  SavedItemSchema
);

import { model, Schema, Types } from "mongoose";
import { ILiveChat } from "../../types/schema";

const LiveChatSchema = new Schema<ILiveChat>(
  {
    user: { type: Types.ObjectId, ref: "User", required: true },
    streamId: { type: Types.ObjectId, ref: "Stream", required: true },
    message: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
  },
  { timestamps: true }
);
const LiveChat = model<ILiveChat>("LiveChat", LiveChatSchema);
export default LiveChat;

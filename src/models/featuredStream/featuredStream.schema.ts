import { Schema, model, Types, Document } from "mongoose";
import { IFeaturedStream } from "../../types/schema";

const featuredStreamSchema = new Schema<IFeaturedStream>({
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
  streams: {
    type: [{ type: Schema.Types.ObjectId, ref: "Stream", required: true }],
    validate: {
      validator: (arr: Types.ObjectId[]) => arr.length <= 5,
      message: "A user can only have up to 5 featured streams.",
    },
  },
});

const FeaturedStreams = model<IFeaturedStream>(
  "FeaturedStreams",
  featuredStreamSchema
);

export default FeaturedStreams;

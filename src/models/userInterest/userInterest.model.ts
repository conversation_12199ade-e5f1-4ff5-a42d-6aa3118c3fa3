import mongoose from "mongoose";
import { IUserInterests } from "../../types/schema";
import UserInterest from "./userInterest.schema";

export const addUserInterests = async (
  userId: string,
  interests: IUserInterests[]
): Promise<IUserInterests | null> => {
  if (!userId || !interests) {
    throw new Error("Invalid userId or interests");
  }
  return await UserInterest.create({ userId, interests });
};

// export const getUserInterestsById = async (
//   userId: string
// ): Promise<IUserInterests | null> => {
//   console.log(userId);
//   const userInterest = await UserInterest.findById({ _id: userId });
//   console.log("==================== get user interest ==================");
//   console.log(userInterest);
//   console.log("==================== get user interest ==================");
//   return userInterest;
// };

// export const getUserInterestsById = async (
//   userId: string
// ): Promise<IUserInterests | null> => {
//   try {
//     console.log("User ID passed:", userId);
//     const userInterest = await UserInterest.findOne({
//       userId: new mongoose.Types.ObjectId(userId),
//     });
//     console.log("==================== get user interest ==================");
//     console.log(userInterest);
//     console.log("==================== get user interest ==================");
//     return userInterest;
//   } catch (error) {
//     console.error("Error fetching user interests:", error);
//     throw error;
//   }
// };

export const getUserInterestsById = async (
  userId: string
): Promise<IUserInterests | null> => {
  try {
    // Find the user interest document by userId
    const userInterest = await UserInterest.findOne({
      userId: new mongoose.Types.ObjectId(userId),
    });

    if (!userInterest) {
      console.log("No interests found for the given userId.");
      return null;
    }

    return userInterest;
  } catch (error) {
    console.error("Error fetching user interests:", error);
    throw error;
  }
};

export const updateOrInsertUserInterests = async (
  userId: string,
  interests: Array<{ category: string; subCategories: string[] }>
): Promise<IUserInterests | null> => {
  try {
    // Update or insert interests for the given user
    const updatedInterests = await UserInterest.findOneAndUpdate(
      { userId },
      {
        $addToSet: {
          interests: { $each: interests }, // Add the interests if they don't already exist
        },
      },
      { upsert: true, new: true } // Create a new record if none exists
    );

    // If the interests document doesn't exist or the update fails
    if (!updatedInterests) {
      console.error("Failed to update or insert user interests.");
      return null;
    }

    return updatedInterests;
  } catch (error) {
    console.error("Error updating or inserting user interests:", error);
    throw new Error("Unable to update or insert user interests.");
  }
};

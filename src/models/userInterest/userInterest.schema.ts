import { Schema, model, Types } from "mongoose";
import { IUserInterests } from "../../types/schema";

const UserInterestSchema = new Schema<IUserInterests>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  interests: [
    {
      category: {
        type: String,
        required: false,
      },
      subCategories: [{ type: String, required: false }],
    },
  ],
});
UserInterestSchema.index({ userId: 1 });

const UserInterest = model<IUserInterests>("UserInterests", UserInterestSchema);

export default UserInterest;

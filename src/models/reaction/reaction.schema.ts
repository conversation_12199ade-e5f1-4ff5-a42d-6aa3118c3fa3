import { Schema, model, Types } from "mongoose";
import { IReaction } from "../../types/schema";

const ReactionSchema = new Schema<IReaction>(
  {
    user: { type: Types.ObjectId, ref: "User", required: true },
    stream: { type: Types.ObjectId, ref: "Stream", required: true },
    type: { type: String, required: true }, // e.g., 'like', 'love', 'haha', etc.
    createdAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

const Reaction = model<IReaction>("Reaction", ReactionSchema);

export default Reaction;

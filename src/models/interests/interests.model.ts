import { IInterest } from "../../types/schema";
import Interests from "./interests.schema";

export const addInterests = async (
  interests: Partial<IInterest>
): Promise<IInterest | null> => {
  return await Interests.create(interests);
};

export const getAllInterests = async (): Promise<IInterest[] | null> => {
  const interests = await Interests.find({}, { __v: 0 });
  return interests;
};

const dummyInterests = [
  {
    category: "Entertainment",
    subCategories: ["Music", "Movies", "TV Shows", "Theater"],
  },
  {
    category: "Sports",
    subCategories: ["Cricket", "Football", "Basketball", "Tennis"],
  },
  {
    category: "Technology",
    subCategories: ["AI", "Blockchain", "Web Development", "Mobile Apps"],
  },
  {
    category: "Health & Wellness",
    subCategories: ["Yoga", "Fitness", "Meditation", "Nutrition"],
  },
  {
    category: "Travel",
    subCategories: ["Adventure", "Beaches", "Mountains", "Cultural Trips"],
  },
  {
    category: "Education",
    subCategories: ["Science", "Mathematics", "History", "Language Learning"],
  },
  {
    category: "Gaming",
    subCategories: ["Assassin's Crud", "Wod of Gar"],
  },
];

// Function to populate the database
const populateInterests = async () => {
  try {
    // Connect to MongoDB

    // Clear existing data (optional)
    await Interests.deleteMany({});
    console.log("Cleared existing interests");

    // Insert dummy data
    await Interests.insertMany(dummyInterests);
    console.log("Dummy interests added successfully");

    // Close the connection

    console.log("Disconnected from MongoDB");
  } catch (error) {
    console.error("Error populating interests:", error);
    process.exit(1);
  }
};

// Run the script
// populateInterests();

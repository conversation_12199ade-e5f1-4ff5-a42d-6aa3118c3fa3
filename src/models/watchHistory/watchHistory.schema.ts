import { Schema, model, Types, Document } from "mongoose";
import { IStream, IWatchHistory } from "../../types/schema";

const WatchHistorySchema = new Schema<IWatchHistory>({
  userId: { type: Types.ObjectId, ref: "User", required: true },
  streamId: { type: Types.ObjectId, ref: "Stream", required: true },
  watchedAt: { type: Date, default: Date.now },
  totalWatchTime: { type: Number, default: 0 },
});

const WatchHistory = model<IWatchHistory>("WatchHistory", WatchHistorySchema);

export default WatchHistory;

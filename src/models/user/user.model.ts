// import User from "./user.schema";
// import { IUser, IUserWithStreams } from "../../types/schema";
// import mongoose from "mongoose";

// export const createUser = async (
//   user: Partial<IUser>
// ): Promise<IUser | null> => {
//   return await User.create(user);
// };

// export const getUser = async (id: string): Promise<IUser | null> => {
//   return await User.findById(id, { password: 0, __v: 0 });
// };

// /**
//  * following function aggregates creators details
//  * @param userId
//  * @returns user data
//  */
// export const getCreatorProfile = async (
//   userId: string,
//   creatorId: string
// ): Promise<IUserWithStreams> => {
//   try {
//     const result = await User.aggregate([
//       {
//         $match: { _id: new mongoose.Types.ObjectId(creatorId) },
//       },

//       {
//         $lookup: {
//           from: "streams",
//           localField: "_id",
//           foreignField: "creator",
//           as: "streams",
//           pipeline: [
//             {
//               $project: {
//                 _id: 1,
//                 title: 1,
//                 description: 1,
//                 transcodedUrl: 1,
//                 url: 1,
//                 tags: 1,
//                 cover: 1,
//                 videoLength: 1,
//                 createdAt: 1,
//                 viewsCount: { $size: "$views" },
//                 likesCount: { $size: "$likes" },
//                 commentsCount: { $size: "$comments" },
//               },
//             },
//           ],
//         },
//       },
//       // Add computed fields for followers and following counts
//       {
//         $addFields: {
//           followersCount: { $size: "$followers" },
//           followingCount: { $size: "$following" },
//           isFollowing: {
//             $cond: {
//               if: {
//                 $in: [
//                   new mongoose.Types.ObjectId(userId),
//                   { $ifNull: ["$followers", []] },
//                 ],
//               },
//               then: true,
//               else: false,
//             },
//           },
//         },
//       },
//       // Project only necessary fields
//       {
//         $project: {
//           password: 0,
//           __v: 0,
//           followers: 0,
//           following: 0,
//           provider: 0,
//           profileSettings: 0,
//           postSettings: 0,
//           deletedReason: 0,
//           isProfileComplete: 0,
//           lastLogin: 0,
//           notInterestedIn: 0,
//           platformSubscription: 0,
//           isDeleted: 0,
//           notifications: 0,
//           "streams.__v": 0, // Exclude unneeded fields from streams
//         },
//       },
//     ]);

//     return result.length > 0 ? result[0] : null;
//   } catch (error) {
//     console.error("Error fetching user with streams:", error);
//     throw new Error("Failed to fetch user with streams.");
//   }
// };

// export const getUserByEmail = async (
//   email: string,
//   password: boolean = false
// ): Promise<IUser | null> => {
//   if (!password)
//     return await User.findOne(
//       { email, isDeleted: false }
//       // { password: 0, __v: 0 }
//     );
//   return await User.findOne({ email, isDeleted: false }, { __v: 0 });
// };

// export const updateUserProfileCompletion = async (
//   userId: string
// ): Promise<IUser | null> => {
//   try {
//     // Find the user by ID and update the profile completion status in one operation
//     const user = await User.findByIdAndUpdate(
//       userId,
//       { $set: { isProfileComplete: true } },
//       { new: true } // Ensure the updated user document is returned
//     );

//     // If user doesn't exist, return null
//     if (!user) {
//       console.error("User not found");
//       return null;
//     }

//     return user;
//   } catch (error) {
//     console.error("Error updating user profile:", error);
//     throw new Error("Unable to update user profile completion.");
//   }
// };

// export const getUserByAppProvider = async (
//   sub: string,
//   provider: string
// ): Promise<IUser | null> => {
//   return await User.findOne({
//     "provider.id": sub,
//     "provider.provider": provider,
//   });
// };

// export const getUserByUsername = async (
//   username: string
// ): Promise<IUser | null> => {
//   return await User.findOne(
//     { username, isDeleted: false }
//     // { password: 0, __v: 0 }
//   );
// };

// export const checkUsernameExists = async (
//   username: string
// ): Promise<boolean> => {
//   const user = await User.findOne({ username });
//   return !!user;
// };

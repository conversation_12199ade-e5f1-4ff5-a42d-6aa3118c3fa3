import { Schema, model } from "mongoose";
import bcrypt from "bcrypt";
import { IUser } from "../../types/schema";
import { EUserProvider } from "../../types/enum";
import { UserDefaultNotifications } from "../../constants/user";

const deleteReasonSchema = new Schema(
  {
    category: { type: String, required: true },
    reason: { type: String, required: true },
    deletedAt: { type: Date, required: true, default: Date.now },
  },
  { _id: false }
);

const profileSettingsSchema = new Schema(
  {
    notifications: {
      type: Object,
      default: UserDefaultNotifications,
    },
    theme: {
      type: String,
      enum: ["light", "dark", "system"],
      default: "system",
    },
  },
  { _id: false }
);

const postSettingsSchema = new Schema(
  {
    postVisibility: {
      type: String,
      enum: ["followers", "everyone", "subscribers", "none"],
      default: "everyone",
    },
    liveVisibility: {
      type: String,
      enum: ["followers", "everyone", "subscribers", "none"],
      default: "everyone",
    },
    liveVideoPricing: {
      type: {
        currency: { type: String, enum: ["USD", "INR"], default: "USD" },
        price: { type: Number, default: 0 },
      },
    },
  },
  { _id: false }
);

// providers: [
//   {
//     provider: { type: String, enum: ["apple", "google", "facebook"], required: true },
//     providerId: { type: String, required: true },
//     accessToken: { type: String },
//     refreshToken: { type: String },
//     idToken: { type: String },  // If applicable
//   }
// ],

const userSchema = new Schema<IUser>(
  {
    username: { type: String, required: false, unique: true, sparse: true },
    email: { type: String, required: false, unique: true, sparse: true },
    phoneNumber: { type: String, required: false, unique: true, sparse: true },
    displayName: { type: String, default: "" },
    bio: { type: String, default: "" },
    profilePicture: { type: String, default: "" },
    password: { type: String, required: false },
    provider: {
      type: {
        provider: {
          type: String,
          enum: EUserProvider,
        },
        id: { type: String, required: false, unique: true, sparse: true },
      },
    },

    followers: [{ type: Schema.Types.ObjectId, ref: "User" }],
    following: [{ type: Schema.Types.ObjectId, ref: "User" }],
    notifications: [{ type: Schema.Types.ObjectId, ref: "Notification" }],
    verified: { type: Boolean, default: false },
    fcmToken: { type: String },
    stripeAccountId: { type: String },
    stripeCustomerId: { type: String },
    isDeleted: { type: Boolean, default: false },
    deletedReason: { type: [deleteReasonSchema], default: [] },
    profileSettings: {
      type: profileSettingsSchema,
      default: {
        notifications: {},
        theme: "system",
      },
    },
    postSettings: {
      type: postSettingsSchema,
      default: {
        postVisibility: "everyone",
        liveVisibility: "everyone",
        liveVideoPricing: {
          currency: "USD",
          price: 0,
        },
      },
    },
    platformSubscription: {
      type: {
        plan: {
          type: Schema.Types.ObjectId,
          ref: "SubscriptionPlan",
          required: true,
        },
        status: { type: String, enum: ["active", "expired"], required: true },
      },
      default: null,
    },
    creatorSubscriptions: {
      type: [
        {
          plan: {
            type: Schema.Types.ObjectId,
            ref: "SubscriptionPlan",
            required: true,
          },
          status: { type: String, enum: ["active", "expired"], required: true },
        },
      ],
      default: [],
    },
    notInterestedIn: [{ type: Schema.Types.ObjectId, ref: "User" }],
    lastLogin: { type: Date, default: null },
    isProfileComplete: { type: Boolean, default: false },
    userInstagram: { type: String, default: "" },
    userFacebook: { type: String, default: "" },
    userYoutube: { type: String, default: "" },
    onboardingSteps: {
      phoneVerified: { type: Boolean, default: false },
      emailVerified: { type: Boolean, default: false },
    },
    sharesCount: { type: Number, required: false, default: 0 },
  },
  { timestamps: true }
);

userSchema.pre("save", async function (next) {
  const user = this;

  try {
    if (!user.isModified("password")) return next();
    const hash = await bcrypt.hash(user.password ?? "", 13);
    user.password = hash;
    next();
  } catch (error: any) {
    next(error);
  }
});

userSchema.methods.verifyPassword = async function (password: string) {
  try {
    const result = await bcrypt.compare(password, this.password);
    return result;
  } catch (error) {
    return false;
  }
};

const User = model<IUser>("User", userSchema);

export default User;

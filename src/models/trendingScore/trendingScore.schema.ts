import { Schema, model, Types, Document } from "mongoose";
import { ITrendingScore } from "../../types/schema";

/**
 * Schema to store trending scores for streams.
 * We ca index trendingScore field to ensure faster data retrieval and performance.
 */
const TrendingScoreSchema = new Schema<ITrendingScore>({
  streamId: {
    type: Types.ObjectId,
    ref: "Stream",
    required: true,
    unique: true,
  },
  trendingScore: { type: Number, required: true, default: 0 },
});

TrendingScoreSchema.index({ trendingScore: -1 });
const TrendingScore = model<ITrendingScore>(
  "TrendingScore",
  TrendingScoreSchema
);

export default TrendingScore;

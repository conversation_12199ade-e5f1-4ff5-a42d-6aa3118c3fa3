import express from "express";
import http from "http";
import cors from "cors";
import morgan from "morgan";
import passport from "passport";
import mongoose from "mongoose";
import { Server } from "socket.io";
import swaggerUi from "swagger-ui-express";
import swaggerJSDoc from "swagger-jsdoc";

import { MONGODB_URI, PORT } from "./config/environment";
import router from "./routes";
import "./config/passport";
import { API_BASE_URL } from "./config/router";
import { swaggerOptions } from "./config/swagger";
import logger, { morganStream } from "./config/logger";
import streamSocket from "./sockets/stream";
import webhookRouter from "./webhooks/index.webhook";
import init from "./lib/sqs";

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

app.set("trust proxy", 1);
app.use("/webhook", webhookRouter);

app.use(express.urlencoded({ extended: true, limit: "950mb" }));
app.use(express.json({ limit: "950mb" }));
app.use(
  morgan("combined", {
    stream: morganStream,
  })
);
app.use(
  cors({
    origin: true,
    credentials: true,
  })
);
app.use(passport.initialize());
const swaggerSpecs = swaggerJSDoc(swaggerOptions);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpecs));

app.get("/", (req, res) => {
  res.status(200).send("Hello from App!");
});

// Health check endpoint for Docker health checks and load balancers
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || "1.0.0",
    environment: process.env.NODE_ENV || "development"
  });
});

app.use(API_BASE_URL, router);
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    if (err && err.error && err.error.isJoi) {
      return res.status(400).json({
        type: err.type,
        message: err.error.toString(),
      });
    }

    logger.error(`Error: ${err.message}`);
    res.status(500).send("Internal Server Error");
  }
);
streamSocket(io);
export default async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    logger.info("Successfully connected to MongoDB");
    server.listen(PORT, () => {
      // Only initialize SQS in production environment
      if (process.env.NODE_ENV === "production" && process.env.AWS_S3_SQS_URL) {
        try {
          init();
          logger.info("SQS service initialized");
        } catch (error) {
          logger.warn("SQS service failed to initialize (non-fatal):", error);
        }
      } else {
        logger.info("SQS service skipped for development environment");
      }

      logger.info("App started on " + PORT);
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
};

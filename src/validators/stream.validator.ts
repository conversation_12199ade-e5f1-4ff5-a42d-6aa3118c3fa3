import { Joi } from "../middlewares/validator";

export const createStreamSchema = Joi.object({
  type: Joi.string().valid("vr", "video", "video-live").required(),
  status: Joi.string()
    .valid("uploading", "draft", "published", "ended", "uploaded")
    .required(),
  visibility: Joi.string()
    .valid("everyone", "followers", "subscribers", "none")
    .optional(),
  title: Joi.string().required(),
  description: Joi.string().optional(),
  category: Joi.string().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  cover: Joi.string().optional(),
  url: Joi.string().required(),
});

export const getStreamDetailsSchema = Joi.object({
  id: Joi.string().required(),
});

export const isStreamLiveSchema = Joi.object({
  streamId: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      "string.empty": "Stream ID is required.",
      "string.pattern.base":
        "Invalid Stream ID format. Must be a 24-character hexadecimal MongoDB ObjectId.",
      "any.required": "Stream ID is required.",
    }),
});

export const updateStreamSettingsSchema = Joi.object({
  visibility: Joi.string()
    .valid("everyone", "followers", "subscribers", "none")
    .required(),
});

export const fetchStreamsSchema = Joi.object({
  status: Joi.string()
    .valid("uploading", "draft", "published", "ended", "uploaded")
    .optional(),
  type: Joi.string().valid("vr", "video", "video-live").optional(),
  page: Joi.number().default(1),
  limit: Joi.number().default(10),
});

export const deleteStreamSchema = Joi.object({
  id: Joi.string().required(),
});

export const AddFeaturedStreamSchema = Joi.object({
  streamId: Joi.string().required(),
});

export const NotInterestedStreamSchema = Joi.object({
  streamId: Joi.string().required(),
});

export const ReportStreamSchema = Joi.object({
  streamId: Joi.string().required(),
  reason: Joi.string()
    .valid(
      "Spam or misleading",
      "Sexual content",
      "Inappropriate hateful or abusive content",
      "Harmful or dangerous acts",
      "Violent or repulsive content"
    )
    .required(),
});

export const UpdateReportStreamStatus = Joi.object({
  streamId: Joi.string().required(),
  status: Joi.string()
    .valid("pending", "reviewed", "resolved", "rejected")
    .required(),
  adminFeedback: Joi.string(),
});

// Live2 streaming validation schemas
export const getStreamUrlsSchema = Joi.object({
  streamKey: Joi.string()
    .min(1)
    .max(100)
    .pattern(/^[a-zA-Z0-9_-]+$/)
    .required()
    .messages({
      "string.empty": "Stream key is required.",
      "string.min": "Stream key must be at least 1 character long.",
      "string.max": "Stream key cannot exceed 100 characters.",
      "string.pattern.base": "Stream key can only contain alphanumeric characters, hyphens, and underscores.",
      "any.required": "Stream key is required.",
    }),
});

import { Joi } from "../middlewares/validator";

const textOverlaySchema = Joi.object({
  id: Joi.string().required(),
  text: Joi.string().required(),
  font: Joi.string().required(),
  color: Joi.string().required(),
  position: Joi.object({
    x: Joi.number().required(),
    y: Joi.number().required(),
  }).required(),
  scale: Joi.number().required(),
  rotation: Joi.number().required(),
});

// Doodle overlay schema
const doodleOverlaySchema = Joi.object({
  id: Joi.string().required(),
  paths: Joi.array()
    .items(
      Joi.object({
        path: Joi.string().required(),
        color: Joi.string().required(),
        strokeWidth: Joi.number().required(),
      })
    )
    .min(1)
    .required(),
  scale: Joi.number().required(),
  rotation: Joi.number().required(),
});

export const createStoryValidator = Joi.object({
  media: Joi.array().items(Joi.string().uri()).min(1).max(10).required(),
  caption: Joi.string().max(300).optional(),
  location: Joi.object({
    coordinates: Joi.object({
      lat: Joi.string().optional(),
      lng: Joi.string().optional(),
    }).optional(),
    address: Joi.string().optional(),
  }).optional(),
  textOverlays: Joi.array().items(textOverlaySchema).optional(),
  doodleOverlays: Joi.array().items(doodleOverlaySchema).optional(),
});
// Validator for marking a story as viewed
export const markStoryAsViewedValidator = Joi.object({
  storyId: Joi.string().required(),
});

export const generateStoryPresignSchema = Joi.object({
  contentType: Joi.string()
    .valid("image/jpeg", "image/png", "image/webp", "image/heic", "video/mp4")
    .required(),
});

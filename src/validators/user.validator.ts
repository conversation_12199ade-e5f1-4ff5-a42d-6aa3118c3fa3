import mongoose from "mongoose";
import { Jo<PERSON> } from "../middlewares/validator";

export const updateUserDetailsSchema = Joi.object({
  username: Joi.string()
    .optional()
    .pattern(
      /^(?!.*[@#$%^&*!`~])(?=^[a-zA-Z0-9])(?!.*[._-]$)[a-zA-Z0-9._-]{3,30}$/
    )
    .messages({
      "string.pattern.base":
        "Username can only contain letters, numbers, dots (.), hyphens (-), or underscores (_). It must not start or end with a dot, hyphen, or underscore and cannot contain restricted characters like @, #, $, %, ^, &, *, !, or ~.",
      "string.empty": "Username cannot be empty.",
      "string.min": "Username must be at least 3 characters long.",
      "string.max": "Username Cannot exceed 30 characters.",
      "any.required": "Username is required.",
    }),
  displayName: Joi.string().optional(),
  bio: Joi.string().optional(),
  profilePicture: Joi.string().uri().optional(),
  userInstagram: Joi.string().uri().optional(),
  userFacebook: Joi.string().uri().optional(),
  userYoutube: Joi.string().uri().optional(),
});

export const updateUserSettingsSchema = Joi.object({
  notifications: Joi.object().pattern(Joi.string(), Joi.boolean()).optional(),
  theme: Joi.string().valid("light", "dark", "system").optional(),
});

export const followUserSchema = Joi.object({
  targetUserId: Joi.string().required(),
});

export const getUserStreamsSchema = Joi.object({
  id: Joi.string().required(),
});

export const userIdCheckMiddleware = Joi.object({
  creatorId: Joi.string().required(),
});

export const deleteUserSchema = Joi.object({
  category: Joi.string().required().messages({
    "string.empty": "Category is required.",
    "any.required": "Category is a mandatory field.",
  }),
  reason: Joi.string().required().messages({
    "string.empty": "Reason is required.",
    "any.required": "Reason is a mandatory field.",
  }),
});

export const updatePostSettingsSchema = Joi.object({
  postVisibility: Joi.string()
    .valid("followers", "everyone", "subscribers", "none")
    .optional()
    .messages({
      "any.only": "Invalid post visibility value.",
    }),
  liveVisibility: Joi.string()
    .valid("followers", "everyone", "subscribers", "none")
    .optional()
    .messages({
      "any.only": "Invalid live visibility value.",
    }),
  liveVideoPricing: Joi.object({
    currency: Joi.string().valid("USD", "INR").required().messages({
      "any.only": "Currency must be either USD or INR.",
    }),
    price: Joi.number().min(0).required().messages({
      "number.base": "Price must be a valid number.",
      "number.min": "Price must be greater than or equal to 0.",
    }),
  }).optional(),
});

export const addProfileViewSchema = Joi.object({
  creatorId: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
    .label("Creator ID")
    .messages({
      "string.pattern.base": "Creator ID must be a valid ObjectId.",
    }),
});

export const getProfileViewersSchema = {
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1).label("Page Number"),
    limit: Joi.number().integer().min(1).default(10).label("Limit"),
  }),
};

export const checkUsernameValidator = Joi.object({
  username: Joi.string()
    .pattern(
      /^(?!.*[@#$%^&*!`~])(?=^[a-zA-Z0-9])(?!.*[._-]$)[a-zA-Z0-9._-]{3,30}$/
    )
    .required()
    .messages({
      "string.pattern.base":
        "Username can only contain letters, numbers, dots (.), hyphens (-), or underscores (_). It must not start or end with a dot, hyphen, or underscore and cannot contain restricted characters like @, #, $, %, ^, &, *, !, or ~.",
      "string.empty": "Username cannot be empty.",
      "string.min": "Username must be at least 3 characters long.",
      "string.max": "Username cannot exceed 30 characters.",
      "any.required": "Username is required.",
    }),
});

export const getFollowersSchema = {
  params: Joi.object({
    userId: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .required()
      .label("User ID")
      .messages({
        "string.pattern.base": "User ID must be a valid ObjectId.",
      }),
  }),
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1).label("Page"),
    limit: Joi.number().integer().min(1).default(10).label("Limit"),
    search: Joi.string().allow("").default("").label("Search Text"),
  }),
};

export const getTotalLikesSchema = {
  params: Joi.object({
    userId: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .required()
      .label("User ID")
      .messages({
        "string.pattern.base": "User ID must be a valid ObjectId.",
      }),
  }),
};

export const getLikedStreamsSchema = {
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1).label("Page"),
    limit: Joi.number().integer().min(1).default(10).label("Limit"),
  }),
};

export const saveUnsaveItemSchema = {
  body: Joi.object({
    itemId: Joi.string()
      .custom((value, helpers) => {
        if (!mongoose.Types.ObjectId.isValid(value)) {
          return helpers.error("any.invalid");
        }
        return value;
      }, "ObjectId Validation")
      .required()
      .label("Item ID")
      .messages({
        "any.invalid": "Item ID must be a valid ObjectId.",
      }),
    itemType: Joi.string()
      .valid("stream", "short")
      .required()
      .label("Item Type"),
  }),
};

export const getSavedItemsSchema = {
  query: Joi.object({
    type: Joi.string()
      .valid("stream", "short")
      .required()
      .label("Type")
      .messages({
        "any.only": "Type must be either 'stream' or 'short'.",
        "string.empty": "Type is required.",
      }),
    page: Joi.number().integer().min(1).default(1).label("Page"),
    limit: Joi.number().integer().min(1).default(10).label("Limit"),
  }),
};

export const getAllSavedItemsSchema = {
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1).label("Page"),
    limit: Joi.number().integer().min(1).default(10).label("Limit"),
  }),
};

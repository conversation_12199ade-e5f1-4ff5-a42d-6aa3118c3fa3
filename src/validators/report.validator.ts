import Jo<PERSON> from "joi";
import { validateMongoObjectId } from "../utils/joiHelper";
import { REPORT_ENTITY_TYPE, REPORT_STREAM_ENUM } from "../models/ReportStream/reportStream.schema";

export const createReportValidator = Joi.object({
    entityId: Joi.string()
        .custom(validateMongoObjectId)
        .required()
        .messages({
            "any.required": "entityId is required",
            "string.base": "entityId must be a string",
        }),

    entityType: Joi.string()
        .valid(...Object.values(REPORT_ENTITY_TYPE))
        .required()
        .messages({
            "any.only": "Invalid entityType",
            "any.required": "entityType is required",
        }),

    reason: Joi.string()
        .valid(...Object.values(REPORT_STREAM_ENUM))
        .required()
        .messages({
            "any.only": "Invalid reason",
            "any.required": "reason is required",
        }),

    description: Joi.string().optional().allow("").messages({
        "string.base": "description must be a string",
    }),
});

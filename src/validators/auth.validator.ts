import { Joi } from "../middlewares/validator";
import { validateMongoObjectId } from "../utils/joiHelper";

export const signupSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).optional(),
  email: Joi.string().email().required(),
  password: Joi.string().optional(),
});

export const forgotPasswordSchema = Joi.object({
  email: Joi.string().email().required(),
});

export const resendOTPSchema = Joi.object({
  email: Joi.string().email().required(),
});

export const verifyOTPSchema = Joi.object({
  email: Joi.string().email().required(),
  otp: Joi.string().length(4).required(),
});

export const resetPasswordSchema = Joi.object({
  email: Joi.string().email().required(),
  otp: Joi.string().length(4).required(),
  newPassword: Joi.string().min(8).required(),
});

export const addInterestSchema = Joi.object({
  userId: Joi.string()
    .custom(validateMongoObjectId, "ObjectId Validation")
    .required()
    .label("User ID")
    .messages({
      "any.invalid": "User ID must be a valid MongoDB ObjectId.",
    }),
  interests: Joi.array()
    .items(
      Joi.object({
        category: Joi.string().required(),
        subCategories: Joi.array().items(Joi.string()).required(),
      })
    )
    .required(),
});

export const completeProfileSchema = {
  body: Joi.object({
    userId: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("User ID")
      .messages({
        "any.invalid": "User ID must be a valid MongoDB ObjectId.",
      }),
    username: Joi.string().alphanum().min(3).max(30).optional(),
    password: Joi.string().optional(),
  }),
};

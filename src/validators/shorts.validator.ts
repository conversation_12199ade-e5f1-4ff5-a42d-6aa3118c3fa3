import { Joi } from "../middlewares/validator";
import { validateMongoObjectId } from "../utils/joiHelper";

export const createShortSchema = Joi.object({
  description: Joi.string().required(),
  videoUrl: Joi.string().uri().optional(),
  thumbnailUrl: Joi.string().uri().required(),
  shortDuration: Joi.number().min(0).required(),
  audioDetails: Joi.object({
    audioId: Joi.string().optional(),
    title: Joi.string().optional(),
    artist: Joi.string().optional(),
    duration: Joi.number().optional(),
  }).optional(),

  tags: Joi.array().items(Joi.string()).required(),
  author: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required(),

  location: Joi.object({
    coordinates: Joi.object({
      lat: Joi.string().optional(),
      lng: Joi.string().optional(),
    }).optional(),
    address: Joi.string().optional(),
  }).optional(),
  category: Joi.string().optional(),
});

export const deleteShortCommentSchema = Joi.object({
  commentId: Joi.string()
    .custom(validateMongoObjectId, "ObjectId Validation")
    .required()
    .label("Comment ID")
    .messages({
      "any.invalid": "Comment ID must be a valid MongoDB ObjectId.",
      "any.required": "Comment ID is required.",
    }),
});

export const getShortCommentsSchema = {
  params: Joi.object({
    shortId: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("Short ID")
      .messages({
        "any.invalid": "Short ID must be a valid MongoDB ObjectId.",
      }),
  }),

  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(10),
  }),
};

export const getRepliesSchema = {
  params: Joi.object({
    commentId: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("Comment ID")
      .messages({
        "any.invalid": "Comment ID must be a valid MongoDB ObjectId.",
      }),
  }),

  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(5),
  }),
};

export const addShortCommentSchema = {
  params: Joi.object({
    id: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("Short ID"),
  }),
  body: Joi.object({
    shortAuthorId: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("Short Author ID"),

    content: Joi.string().min(1).required(),

    replyTo: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .optional()
      .allow(null),
  }),
};

export const likeShortSchema = {
  params: Joi.object({
    id: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("Short ID"),
  }),

  body: Joi.object({
    action: Joi.string().valid("like", "unlike").required(),
  }),
};

export const viewShortSchema = {
  params: Joi.object({
    id: Joi.string()
      .custom(validateMongoObjectId, "ObjectId Validation")
      .required()
      .label("Short ID")
      .messages({
        "any.invalid": "Short ID must be a valid MongoDB ObjectId.",
      }),
  }),
};

export const getTrendingShortsValidator = Joi.object({
  page: Joi.number().integer().min(1).optional().default(1),
  limit: Joi.number().integer().min(1).max(100).optional().default(10),
});

export const getRecommendationValidator = Joi.object({
  page: Joi.number().integer().min(1).optional().default(1),
  limit: Joi.number().integer().min(1).max(100).optional().default(10),
});

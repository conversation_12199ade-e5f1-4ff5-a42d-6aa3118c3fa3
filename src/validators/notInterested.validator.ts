import { Joi } from "../middlewares/validator";
import { validateMongoObjectId } from "../utils/joiHelper";

export const addToNotInterestedValidator = Joi.object({
    contentId: Joi.string().custom(validateMongoObjectId).required(),
    contentType: Joi.string().valid("stream", "short").required(),
});


export const removeFromNotInterestedValidator = Joi.object({
    contentId: Joi.string().custom(validateMongoObjectId).required(),
    contentType: Joi.string().valid("stream", "short").required(),
});

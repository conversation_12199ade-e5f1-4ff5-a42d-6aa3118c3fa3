
> ai.metastart.vyoo.server@1.0.0 dev
> ts-node-dev --respawn --transpile-only ./src/index.ts

[INFO] 08:55:18 ts-node-dev ver. 2.0.0 (using ts-node ver. 10.9.2, typescript ver. 5.4.5)
⚠️  Firebase not initialized - missing variables: FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY_ID, FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL, FIREBASE_CLIENT_ID
💡 Firebase features will be disabled. Add credentials to .env file to enable Firebase.
✅ Google OAuth strategy initialized
2025-07-17 08:55:22 [vyoo-api] [32MINFO[39M: Successfully connected to MongoDB {"version":"1.0.0","environment":"development"}
[ERROR] 08:55:22 Error: listen EADDRINUSE: address already in use :::8081

# VYOO Streaming Platform - API Endpoints Documentation
# Base URL: http://localhost:8081/v1/api
# Production URL: Replace localhost:8081 with your production domain

# =====================================
# AUTHENTICATION ENDPOINTS
# =====================================

## 1. User Signup with Email
# POST /v1/api/auth/email/signup
# Description: Create a new user account with email verification
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/email/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser123",
    "password": "securepassword123"
  }'

## 2. Verify Signup OTP
# POST /v1/api/auth/email/verify-otp
# Description: Verify the OTP sent to email during signup
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/email/verify-otp" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "1234"
  }'

## 3. User Login
# POST /v1/api/auth/login
# Description: Login with email/username and password
# Authentication: None required
# Returns: JWT token for authenticated requests
curl -X POST "http://localhost:8081/v1/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "loginIdentifier": "<EMAIL>",
    "password": "securepassword123",
    "deviceInfo": {
      "fcmToken": "optional_fcm_token",
      "deviceType": "web"
    }
  }'

## 4. Google OAuth Login
# POST /v1/api/auth/login/google
# Description: Login/signup with Google OAuth
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/login/google" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "username": "johndoe",
    "photo": "https://example.com/photo.jpg",
    "providerType": "google",
    "deviceInfo": {
      "fcmToken": "fcm_token_here"
    },
    "interests": []
  }'

## 5. Apple Sign In
# POST /v1/api/auth/login/apple
# Description: Login/signup with Apple ID
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/login/apple" \
  -H "Content-Type: application/json" \
  -d '{
    "identityToken": "apple_identity_token",
    "deviceInfo": {
      "fcmToken": "fcm_token_here"
    },
    "givenName": "John",
    "familyName": "Doe"
  }'

## 6. Forgot Password - Send OTP
# POST /v1/api/auth/forgot-password
# Description: Send OTP to email for password reset
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'

## 7. Reset Password
# POST /v1/api/auth/reset-password
# Description: Reset password using OTP
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "1234",
    "newPassword": "newsecurepassword123"
  }'

## 8. Get Interests List
# GET /v1/api/auth/interests
# Description: Get available interests for user selection
# Authentication: None required
curl -X GET "http://localhost:8081/v1/api/auth/interests"

## 9. User Logout
# POST /v1/api/auth/logout
# Description: Logout user and remove device token
# Authentication: None required
curl -X POST "http://localhost:8081/v1/api/auth/logout" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user_id_here",
    "fcmToken": "fcm_token_here"
  }'

# =====================================
# STREAMING ENDPOINTS
# =====================================

## 10. Create Live Stream Channel
# POST /v1/api/stream/live/start-channel
# Description: Create a new live stream and get RTMP/HLS URLs
# Authentication: Required (Bearer token)
# Returns: Stream key, RTMP URL, HLS URLs pointing to CloudFront
curl -X POST "http://localhost:8081/v1/api/stream/live/start-channel" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "My Live Stream",
    "description": "Testing live streaming",
    "category": "gaming",
    "tags": ["gaming", "live"]
  }'

## 11. Get Stream URLs
# GET /v1/api/stream/urls/{streamKey}
# Description: Get HLS and RTMP URLs for a stream (now points to CloudFront)
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/stream/urls/YOUR_STREAM_KEY" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 12. Check if Stream is Live
# GET /v1/api/stream/live/{streamId}
# Description: Check if a stream is currently live
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/stream/live/YOUR_STREAM_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 13. Fetch Live Streams
# GET /v1/api/stream/fetch-live-streams
# Description: Get list of currently live streams
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/stream/fetch-live-streams" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 14. Stream Health Check
# GET /v1/api/stream/health
# Description: Check streaming infrastructure health
# Authentication: None required
curl -X GET "http://localhost:8081/v1/api/stream/health"

## 15. Create Regular Stream (VOD)
# POST /v1/api/stream
# Description: Create a video-on-demand stream
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "My Video",
    "description": "Video description",
    "type": "video",
    "category": "entertainment",
    "tags": ["video", "entertainment"]
  }'

## 16. Fetch Streams
# GET /v1/api/stream?page=1&limit=10
# Description: Get paginated list of streams
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/stream?page=1&limit=10&category=gaming" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 17. Get Stream Details
# GET /v1/api/stream/{streamId}
# Description: Get detailed information about a specific stream
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/stream/YOUR_STREAM_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 18. Delete Stream
# DELETE /v1/api/stream/{streamId}
# Description: Delete a stream
# Authentication: Required (Bearer token)
curl -X DELETE "http://localhost:8081/v1/api/stream/YOUR_STREAM_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# =====================================
# RTMP AUTHENTICATION ENDPOINTS (Internal)
# =====================================

## 19. RTMP Publish Authentication
# POST /v1/api/rtmp-auth/publish
# Description: Internal endpoint called by NGINX-RTMP for stream authentication
# Authentication: None (called by RTMP server)
# Note: This is called automatically by Live2 container
curl -X POST "http://localhost:8081/v1/api/rtmp-auth/publish" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "JWT_STREAM_KEY_HERE"
  }'

## 20. RTMP Play Authentication
# POST /v1/api/rtmp-auth/play
# Description: Internal endpoint for play authentication
# Authentication: None (called by RTMP server)
curl -X POST "http://localhost:8081/v1/api/rtmp-auth/play" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "JWT_STREAM_KEY_HERE"
  }'

# =====================================
# USER MANAGEMENT ENDPOINTS
# =====================================

## 21. Get User Profile
# GET /v1/api/users/{userId}
# Description: Get user profile information
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/users/USER_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 22. Update User Profile
# PUT /v1/api/users/{userId}
# Description: Update user profile details
# Authentication: Required (Bearer token)
curl -X PUT "http://localhost:8081/v1/api/users/USER_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "displayName": "New Display Name",
    "bio": "Updated bio"
  }'

## 23. Follow User
# POST /v1/api/users/follow
# Description: Follow another user
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/users/follow" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "userId": "USER_ID_TO_FOLLOW"
  }'

## 24. Unfollow User
# POST /v1/api/users/unfollow
# Description: Unfollow a user
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/users/unfollow" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "userId": "USER_ID_TO_UNFOLLOW"
  }'

## 25. Get User Followers
# GET /v1/api/users/{userId}/followers
# Description: Get list of user followers
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/users/USER_ID/followers?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 26. Check Username Availability
# GET /v1/api/users/check-username/{username}
# Description: Check if username is available
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/users/check-username/testusername" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# =====================================
# SEARCH ENDPOINTS
# =====================================

## 27. Global Search
# GET /v1/api/search?query=test&type=users
# Description: Search for users, streams, or content
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/search?query=gaming&type=streams&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 28. Get Trending Searches
# GET /v1/api/search/trending
# Description: Get trending search terms
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/search/trending" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# =====================================
# SHORTS ENDPOINTS
# =====================================

## 29. Create Short Video
# POST /v1/api/shorts/create-short
# Description: Create a short video
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/shorts/create-short" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "My Short Video",
    "description": "Short video description",
    "url": "https://s3.amazonaws.com/bucket/video.mp4",
    "cover": "https://s3.amazonaws.com/bucket/thumbnail.jpg",
    "tags": ["short", "video"]
  }'

## 30. Get Trending Shorts
# GET /v1/api/shorts/trending
# Description: Get trending short videos
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/shorts/trending?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 31. Like Short Video
# POST /v1/api/shorts/like
# Description: Like a short video
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/shorts/like" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "shortId": "SHORT_ID_HERE"
  }'

## 32. View Short Video
# POST /v1/api/shorts/view
# Description: Record a view for a short video
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/shorts/view" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "shortId": "SHORT_ID_HERE"
  }'

# =====================================
# PLAYLIST ENDPOINTS
# =====================================

## 33. Create Playlist
# POST /v1/api/playlist
# Description: Create a new playlist
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/playlist" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "My Playlist",
    "description": "Playlist description",
    "visibility": "public"
  }'

## 34. Get User Playlists
# GET /v1/api/playlist?userId=USER_ID
# Description: Get all playlists for a user
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/playlist?userId=USER_ID&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 35. Add Item to Playlist
# POST /v1/api/playlist/add-item
# Description: Add a stream/video to playlist
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/playlist/add-item" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "playlistId": "PLAYLIST_ID",
    "itemId": "STREAM_ID",
    "itemType": "stream"
  }'

# =====================================
# STORY ENDPOINTS
# =====================================

## 36. Upload Story
# POST /v1/api/story/upload-story
# Description: Upload a story
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/story/upload-story" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "url": "https://s3.amazonaws.com/bucket/story.mp4",
    "type": "video",
    "caption": "My story caption"
  }'

## 37. Get Stories by Following
# GET /v1/api/story/following
# Description: Get stories from users you follow
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/story/following" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

## 38. Get User Stories
# GET /v1/api/story/user/{userId}
# Description: Get stories for a specific user
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/story/user/USER_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# =====================================
# NOTIFICATION ENDPOINTS
# =====================================

## 39. Get Notifications
# POST /v1/api/notification/fetch
# Description: Get user notifications
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/notification/fetch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "page": 1,
    "limit": 20
  }'

## 40. Mark Notifications as Read
# POST /v1/api/notification/mark-read
# Description: Mark notifications as read
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/notification/mark-read" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "notificationIds": ["NOTIFICATION_ID_1", "NOTIFICATION_ID_2"]
  }'

## 41. Get Unread Notification Count
# GET /v1/api/notification/unread-count
# Description: Get count of unread notifications
# Authentication: Required (Bearer token)
curl -X GET "http://localhost:8081/v1/api/notification/unread-count" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# =====================================
# STRIPE PAYMENT ENDPOINTS
# =====================================

## 42. Create Subscription Plan
# POST /v1/api/stripe/create-subscription-plan
# Description: Create a subscription plan
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/stripe/create-subscription-plan" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Premium Plan",
    "price": 999,
    "currency": "usd",
    "interval": "month"
  }'

## 43. Subscribe to Plan
# POST /v1/api/stripe/subscribe
# Description: Subscribe user to a plan
# Authentication: Required (Bearer token)
curl -X POST "http://localhost:8081/v1/api/stripe/subscribe" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "planId": "PLAN_ID",
    "paymentMethodId": "PAYMENT_METHOD_ID"
  }'

# =====================================
# ABOUT ENDPOINTS
# =====================================

## 44. Get Privacy Policy
# GET /v1/api/about/privacy-policy
# Description: Get privacy policy content
# Authentication: None required
curl -X GET "http://localhost:8081/v1/api/about/privacy-policy"

## 45. Get Terms of Service
# GET /v1/api/about/terms-of-service
# Description: Get terms of service content
# Authentication: None required
curl -X GET "http://localhost:8081/v1/api/about/terms-of-service"

# =====================================
# HEALTH CHECK ENDPOINTS
# =====================================

## 46. Application Health Check
# GET /health
# Description: Check application health status
# Authentication: None required
curl -X GET "http://localhost:8081/health"

# =====================================
# TESTING WORKFLOW EXAMPLES
# =====================================

## Complete User Registration and Stream Creation Flow:

# Step 1: Sign up
curl -X POST "http://localhost:8081/v1/api/auth/email/signup" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "username": "testuser", "password": "password123"}'

# Step 2: Verify OTP (get OTP from email/logs)
curl -X POST "http://localhost:8081/v1/api/auth/email/verify-otp" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "otp": "1234"}'

# Step 3: Login
TOKEN=$(curl -s -X POST "http://localhost:8081/v1/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"loginIdentifier": "<EMAIL>", "password": "password123"}' | jq -r '.token')

# Step 4: Create live stream
curl -X POST "http://localhost:8081/v1/api/stream/live/start-channel" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"title": "Test Stream", "description": "Testing streaming"}'

# =====================================
# IMPORTANT NOTES
# =====================================

# 1. Authentication:
#    - Most endpoints require Bearer token authentication
#    - Get token from login endpoint
#    - Include in Authorization header: "Authorization: Bearer YOUR_TOKEN"

# 2. S3/CloudFront Configuration:
#    - HLS URLs now point to CloudFront: https://d1s6yky0m2rx9y.cloudfront.net/hls
#    - Stream segments automatically uploaded to S3 bucket: vyooo-abr
#    - JWT stream keys have 6-hour expiration

# 3. RTMP Streaming:
#    - Production RTMP URL: rtmp://ec2-65-0-205-128.ap-south-1.compute.amazonaws.com:1935/src
#    - Use JWT stream key from /stream/live/start-channel response
#    - Format: rtmp://server:1935/src/{jwt_stream_key}

# 4. Environment Variables:
#    - JWT_SECRET: Used for token signing
#    - HLS_CLOUDFRONT_URL: CloudFront distribution URL
#    - AWS credentials for S3 integration

# 5. Error Handling:
#    - Check response status codes
#    - 401: Authentication required/invalid
#    - 403: Forbidden/insufficient permissions
#    - 400: Bad request/validation error
#    - 500: Internal server error

# 6. Rate Limiting:
#    - Some endpoints may have rate limiting
#    - Check response headers for rate limit info

# 7. Pagination:
#    - Most list endpoints support pagination
#    - Use page and limit query parameters
#    - Default limit is usually 10-20 items

# 8. Testing with Postman:
#    - Import these curl commands as Postman collection
#    - Set environment variables for base URL and tokens
#    - Use collection variables for dynamic values 
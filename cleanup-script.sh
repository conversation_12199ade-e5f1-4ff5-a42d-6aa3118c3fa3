#!/bin/bash

# Vyoo Streaming Platform - Automated Cleanup Script
# Based on comprehensive analysis in report/docs/

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_manual() {
    echo -e "${YELLOW}[MANUAL ACTION REQUIRED]${NC} $1"
}

# Header
echo "=============================================="
echo "  Vyoo Streaming Platform - Cleanup Script"
echo "=============================================="
echo ""

# Check if we're in the right directory
if [[ ! -f "package.json" ]] || [[ ! -d "src" ]] || [[ ! -d "live2" ]]; then
    print_error "This script must be run from the root directory of the Vyoo project"
    print_error "Expected: package.json, src/, and live2/ directories"
    exit 1
fi

# Confirmation
echo "This script will remove unused files and clean up the codebase."
echo "Files to be removed:"
echo "  - Firebase credentials (src/config/serviceAccount.json)"
echo "  - Duplicate NGINX config (src/config/nginx.conf)"
echo "  - Unused NMS files (src/lib/nms.ts, src/config/nms.ts)"
echo "  - Unused player files (3 files in live2/players/)"
echo "  - Incomplete scripts (5 files in live2/)"
echo "  - Redundant test scripts (2 files)"
echo "  - Build artifacts (dist/ directory)"
echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Cleanup cancelled by user"
    exit 0
fi

# PHASE 1: CRITICAL SECURITY & CONFLICTS
echo ""
print_status "=== PHASE 1: CRITICAL SECURITY & CONFLICTS ==="

# Step 1.1: Backup
print_status "Creating backup..."
BACKUP_DATE=$(date +%Y%m%d-%H%M)
git checkout -b "cleanup-backup-${BACKUP_DATE}" 2>/dev/null || print_warning "Branch already exists or git not available"
git tag "pre-cleanup-${BACKUP_DATE}" 2>/dev/null || print_warning "Tag creation failed or git not available"

if [[ -f ".env" ]]; then
    cp .env ".env.backup-${BACKUP_DATE}"
    print_success "Backed up .env file"
fi

if [[ -f "src/config/serviceAccount.json" ]]; then
    cp src/config/serviceAccount.json "serviceAccount.backup.json"
    print_success "Backed up Firebase credentials"
fi

# Step 1.2: Remove Firebase credentials
print_status "Removing Firebase credentials from source control..."
if [[ -f "src/config/serviceAccount.json" ]]; then
    git rm src/config/serviceAccount.json 2>/dev/null || rm src/config/serviceAccount.json
    echo "src/config/serviceAccount.json" >> .gitignore
    echo "serviceAccount*.json" >> .gitignore
    print_success "Removed Firebase credentials from source control"
    print_manual "Add Firebase credentials to .env file as environment variables"
    print_manual "Update src/config/firebase.ts to use environment variables"
else
    print_warning "Firebase credentials file not found"
fi

# Step 1.3: Remove duplicate NGINX config
print_status "Removing duplicate NGINX configuration..."
if [[ -f "src/config/nginx.conf" ]]; then
    rm src/config/nginx.conf
    print_success "Removed duplicate NGINX config (src/config/nginx.conf)"
    print_success "Active config remains: live2/conf/nginx.conf"
else
    print_warning "Duplicate NGINX config not found"
fi

# Step 1.4: Remove unused NMS code
print_status "Removing unused Node Media Server code..."
if [[ -f "src/lib/nms.ts" ]]; then
    rm src/lib/nms.ts
    print_success "Removed unused NMS library (src/lib/nms.ts)"
else
    print_warning "NMS library file not found"
fi

if [[ -f "src/config/nms.ts" ]]; then
    rm src/config/nms.ts
    print_success "Removed unused NMS config (src/config/nms.ts)"
else
    print_warning "NMS config file not found"
fi

# Step 1.5: Clean commented code (basic cleanup)
print_status "Cleaning basic commented code from app.ts..."
if [[ -f "src/app.ts" ]]; then
    # Create backup
    cp src/app.ts src/app.ts.backup
    
    # Remove commented NMS import
    sed -i.tmp '/^\/\/ import nms from/d' src/app.ts
    
    # Remove commented nms.run()
    sed -i.tmp '/^[[:space:]]*\/\/ nms\.run();/d' src/app.ts
    
    # Clean up temporary files
    rm src/app.ts.tmp 2>/dev/null || true
    
    print_success "Cleaned basic commented code from src/app.ts"
    print_manual "Manually remove duplicate health endpoint and commented CORS config"
else
    print_warning "src/app.ts not found"
fi

# PHASE 2: REMOVE UNUSED FILES
echo ""
print_status "=== PHASE 2: REMOVE UNUSED FILES ==="

# Step 2.1: Clean live2/ directory
print_status "Cleaning unused files from live2/ directory..."

# Remove unused player files
for file in "live2/players/dash.html" "live2/players/rtmp.html" "live2/players/rtmp_hls.html"; do
    if [[ -f "$file" ]]; then
        rm "$file"
        print_success "Removed unused player: $file"
    else
        print_warning "Player file not found: $file"
    fi
done

# Remove incomplete/unused scripts
declare -a unused_files=(
    "live2/ABR.sh"
    "live2/monitor_c6g.sh"
    "live2/LICENSE"
    "live2/NATIVE_INSTALL_4VCPU.md"
    "live2/commands.txt"
)

for file in "${unused_files[@]}"; do
    if [[ -f "$file" ]]; then
        rm "$file"
        print_success "Removed unused file: $file"
    else
        print_warning "File not found: $file"
    fi
done

# Step 2.2: Clean root directory scripts
print_status "Cleaning redundant scripts from root directory..."

if [[ -f "simple-test.sh" ]]; then
    rm simple-test.sh
    print_success "Removed redundant script: simple-test.sh"
else
    print_warning "simple-test.sh not found"
fi

if [[ -f "scripts/comprehensive-test.sh" ]]; then
    rm scripts/comprehensive-test.sh
    print_success "Removed complex test script: scripts/comprehensive-test.sh"
else
    print_warning "scripts/comprehensive-test.sh not found"
fi

# Step 2.3: Clean build artifacts
print_status "Cleaning build artifacts..."
if [[ -d "dist" ]]; then
    rm -rf dist/
    print_success "Removed build artifacts: dist/ directory"
else
    print_warning "dist/ directory not found"
fi

# PHASE 3: SUMMARY AND MANUAL ACTIONS
echo ""
print_status "=== CLEANUP SUMMARY ==="

print_success "Automated cleanup completed successfully!"
echo ""
print_status "Files removed:"
print_status "  ✅ Firebase credentials (moved to backup)"
print_status "  ✅ Duplicate NGINX config"
print_status "  ✅ Unused NMS files"
print_status "  ✅ Unused player files (3 files)"
print_status "  ✅ Incomplete/unused scripts (5 files)"
print_status "  ✅ Redundant test scripts (2 files)"
print_status "  ✅ Build artifacts (dist/ directory)"

echo ""
print_status "=== MANUAL ACTIONS REQUIRED ==="
print_manual "1. Add Firebase credentials to .env file:"
echo "   FIREBASE_PROJECT_ID=ai-metastart-vyoo-3ec52"
echo "   FIREBASE_CLIENT_EMAIL=<EMAIL>"
echo "   FIREBASE_PRIVATE_KEY=\"-----BEGIN PRIVATE KEY-----\\n[KEY_HERE]\\n-----END PRIVATE KEY-----\""

print_manual "2. Update src/config/firebase.ts to use environment variables"

print_manual "3. Edit src/app.ts manually:"
echo "   - Remove duplicate health endpoint (lines ~37-44)"
echo "   - Remove commented CORS configuration (lines ~55-61)"

print_manual "4. Fix console.log statements:"
echo "   - src/middlewares/restrict-ip.ts (lines 44, 56)"

print_manual "5. Optimize authentication in src/middlewares/authenticate.ts"

print_manual "6. Remove redundant JWT verification in src/middlewares/authenticate-socket.ts"

echo ""
print_status "=== VERIFICATION STEPS ==="
echo "Run these commands to verify the cleanup:"
echo "1. npm run build                    # Test build"
echo "2. docker-compose up -d            # Test containers"
echo "3. curl http://localhost:3000/health # Test API"
echo "4. curl http://localhost:8080/health # Test RTMP server"

echo ""
print_status "=== ROLLBACK INSTRUCTIONS ==="
echo "If issues arise, rollback with:"
echo "git checkout cleanup-backup-${BACKUP_DATE}"
echo "cp .env.backup-${BACKUP_DATE} .env"
echo "cp serviceAccount.backup.json src/config/serviceAccount.json"

echo ""
print_success "Cleanup script completed! Review manual actions above."
print_status "For detailed information, see: report/docs/MASTER_CLEANUP_EXECUTION_PLAN.md"

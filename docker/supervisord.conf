[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
pidfile=/tmp/supervisord.pid
user=root

[program:main-server]
command=node dist/index.js
directory=/app
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/main-server.log

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock 
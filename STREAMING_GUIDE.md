# Vyoo Streaming Server - A Practical Guide

This guide provides practical instructions on how to use the streaming features of the Vyoo platform, from broadcasting your first stream to watching it live.

## 🌊 The Streaming Workflow

The streaming architecture consists of two main components:
1.  **The Vyoo Server (Node.js)**: Manages users, stream metadata, and authentication.
2.  **The Live2 Server (Docker)**: An NGINX-based server that ingests RTMP streams and converts them to HLS for playback.

The workflow is as follows:
1.  A streamer authenticates with the Vyoo Server to get a unique **stream key**.
2.  The streamer uses this key in their broadcasting software (e.g., OBS) to send an RTMP stream to the Live2 server.
3.  The Live2 server receives the stream, authenticates the key with the Vyoo Server, and begins converting it to HLS segments.
4.  Viewers request the HLS playlist from the Live2 server to watch the stream.

## Broadcast Your Stream

### Step 1: Get Your Stream Key

Before you can stream, you need a unique stream key. You can get this by creating a stream via the API.

-   **Endpoint**: `POST /v1/api/stream`
-   **Auth**: Required `Bearer` token.
-   **Body**:
    ```json
    {
      "title": "My Awesome Live Stream",
      "description": "Broadcasting live to the world!"
    }
    ```
-   **Response**: The API will return a stream object which includes your `streamKey`.

### Step 2: Configure Your Broadcasting Software

Use the following details in your broadcasting software (like OBS, Streamlabs, or FFmpeg):

-   **Service**: Custom
-   **Server (RTMP URL)**: `rtmp://localhost:1935/live`
-   **Stream Key**: The `streamKey` you received from the API.

### Step 3: Start Streaming!

Once you start the stream from your software, the Live2 server will automatically pick it up and make it available for viewing.

## ▶️ Watch a Live Stream

To watch a stream, you'll need the HLS playback URL.

-   **HLS URL Format**: `http://localhost:8080/hls/<stream_key>.m3u8`
-   Replace `<stream_key>` with the key of the stream you want to watch.

You can open this URL in any HLS-compatible player, such as:
-   [VLC Media Player](https://www.videolan.org/vlc/)
-   A web-based player like [HLS.js](https://hls-js.netlify.app/demo/)
-   Safari on macOS or iOS

## 📊 Monitor Your Stream

You can view real-time statistics for the streaming server, including active streams, bandwidth, and connected clients.

-   **Statistics URL**: `http://localhost:8080/stat`

## 🛠️ Example with FFmpeg

If you don't have OBS, you can use `ffmpeg` to send a test stream.

```bash
# This command creates a test video pattern and streams it.
# Replace 'test_stream' with your actual stream key.
ffmpeg -re -f lavfi -i testsrc2=size=1280x720:rate=30 \
       -c:v libx264 -preset ultrafast -f flv \
       rtmp://localhost:1935/live/test_stream
```

## ❓ Troubleshooting

-   **"Connection Refused" in OBS/FFmpeg**:
    -   Ensure the Live2 Docker container is running (`sudo docker ps`).
    -   Check that port `1935` is accessible.
-   **Stream Not Playing (404 Error)**:
    -   Double-check that you are using the correct stream key in the HLS URL.
    -   Verify that the stream is active by checking the `/stat` page.
-   **Authentication Errors**:
    -   Make sure your stream key is correct and hasn't expired.
    -   Check the logs of the Vyoo server for any authentication errors from the RTMP server. 
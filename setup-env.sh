#!/bin/bash

# Vyoo Streaming Platform Environment Setup Script
set -e

echo "=== Vyoo Environment Setup ==="
echo "This script will configure your environment for the comprehensive fail-safe streaming system."
echo ""

# Check if .env file exists
if [ -f ".env" ]; then
    echo "⚠️  .env file already exists."
    echo "Would you like to backup and recreate it? (y/n)"
    read -r answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        echo "✅ Backup created: .env.backup.$(date +%Y%m%d_%H%M%S)"
    else
        echo "Keeping existing .env file. You may need to manually add missing variables."
        exit 0
    fi
fi

echo "Creating .env file with AWS credentials and fail-safe configuration..."

cat > .env << 'EOF'
# Vyoo Streaming Platform Environment Configuration

# Server Configuration
NODE_ENV=production
PORT=8081
SERVER_PORT=3000

# Database
MONGODB_URI=mongodb://localhost:27017/vyoo-server

# JWT Configuration (CHANGE THESE IN PRODUCTION)
JWT_SECRET=vyoo-jwt-secret-key-change-in-production
COOKIE_SECRET=vyoo-cookie-secret-change-in-production

# AWS Configuration for S3 Integration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=RdIHiTGoBlr/ncBxmie4/3a+3GS0gsMxJXi0Q13k
AWS_REGION=ap-south-1
AWS_S3_BUCKET_NAME=vyooo-abr

# Live2 RTMP/HLS Configuration
RTMP_PORT=1935
HLS_PORT=8080
RTMP_BASE_URL=rtmp://localhost:1935/live
HLS_BASE_URL=http://localhost:8080/hls
HLS_CLOUDFRONT_URL=https://d1s6yky0m2rx9y.cloudfront.net/hls

# Fail-Safe System Configuration
S3_UPLOAD_ENABLED=true
S3_RECOVERY_ENABLED=true
MONITOR_ENABLED=true
LOG_LEVEL=INFO

# Performance Configuration
LIVE2_MEMORY_LIMIT=8G
LIVE2_CPU_LIMIT=6.0
LIVE2_SHM_SIZE=2g

# OAuth Configuration (update with your actual values)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
APPLE_CLIENT_ID=your-apple-client-id

# Stripe Configuration (update with your actual values)
STRIPE_API_KEY=your-stripe-api-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Firebase Configuration (update with your actual values)
FIREBASE_ADMIN_SDK_KEY=your-firebase-admin-sdk-key

# Email Configuration (update with your actual values)
MAILER_EMAIL=<EMAIL>
MAILER_PASSWORD=your-email-password
EOF

echo "✅ .env file created successfully!"
echo ""

# Test AWS connectivity
echo "=== Testing AWS Connectivity ==="
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="RdIHiTGoBlr/ncBxmie4/3a+3GS0gsMxJXi0Q13k"
export AWS_REGION="ap-south-1"

if command -v aws &> /dev/null; then
    echo "Testing S3 bucket access..."
    if aws s3 ls s3://vyooo-abr/ > /dev/null 2>&1; then
        echo "✅ S3 bucket access verified!"
    else
        echo "⚠️  Warning: Could not access S3 bucket. Check AWS credentials."
    fi
else
    echo "⚠️  AWS CLI not found. Install it for local testing:"
    echo "   curl \"https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip\" -o \"awscliv2.zip\""
    echo "   unzip awscliv2.zip"
    echo "   sudo ./aws/install"
fi

echo ""
echo "=== Setup Complete ==="
echo "✅ Environment configured for fail-safe streaming system"
echo "✅ AWS credentials configured"
echo "✅ All fail-safe services enabled"
echo ""
echo "=== Next Steps ==="
echo "1. Review and update .env file with your specific values"
echo "2. Start the system:"
echo "   docker-compose up --build -d"
echo "   # OR use Live2 standalone:"
echo "   cd live2 && ./build_docker.sh && ./run_docker.sh"
echo ""
echo "3. Test the integration:"
echo "   cd live2 && ./test-s3-integration.sh"
echo ""
echo "🎯 Ready for bulletproof streaming!" 
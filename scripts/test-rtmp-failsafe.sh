#!/bin/bash
set -euo pipefail

# --- RTMP Server Fail-Safe Testing Script ---
# Comprehensive testing of RTMP server with all fail-safe mechanisms

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Test configuration
TEST_STREAM_KEY="test_failsafe_$(date +%s)"
TEST_DURATION=${TEST_DURATION:-60}  # seconds
RTMP_URL="rtmp://localhost:1935/live"
HLS_BASE_URL="http://localhost:8080/hls"
S3_BUCKET="vyooo-abr"

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

print_header() {
    echo -e "${BLUE}"
    echo "=============================================================="
    echo "   RTMP SERVER FAIL-SAFE TESTING SUITE"
    echo "   Testing: Live2 Container + S3 Upload + Recovery"
    echo "   Stream Key: $TEST_STREAM_KEY"
    echo "   Duration: ${TEST_DURATION}s"
    echo "=============================================================="
    echo -e "${NC}"
}

print_section() {
    echo -e "\n${YELLOW}🔧 $1${NC}"
    echo "--------------------------------------------------------------"
}

print_test() {
    echo -e "${CYAN}📋 Testing: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_TESTS++))
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_TESTS++))
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="${3:-0}"
    
    ((TOTAL_TESTS++))
    print_test "$test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        local result=0
    else
        local result=1
    fi
    
    if [ "$result" -eq "$expected_result" ]; then
        print_success "$test_name"
        return 0
    else
        print_error "$test_name"
        return 1
    fi
}

# Test Prerequisites
test_prerequisites() {
    print_section "PREREQUISITES CHECK"
    
    run_test "Docker is installed" "command -v docker"
    run_test "Docker is running" "docker info"
    run_test "FFmpeg is available" "command -v ffmpeg"
    run_test "curl is available" "command -v curl"
    run_test "AWS CLI is available" "command -v aws"
    
    # Check AWS credentials
    if aws sts get-caller-identity >/dev/null 2>&1; then
        print_success "AWS credentials are configured"
        ((PASSED_TESTS++))
    else
        print_error "AWS credentials are not configured properly"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    # Check S3 bucket access
    if aws s3 ls "s3://$S3_BUCKET" >/dev/null 2>&1; then
        print_success "S3 bucket '$S3_BUCKET' is accessible"
        ((PASSED_TESTS++))
    else
        print_error "S3 bucket '$S3_BUCKET' is not accessible"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Test Container Status
test_container_status() {
    print_section "CONTAINER STATUS CHECK"
    
    # Check if containers are running
    if docker ps | grep -q "rtmp-server"; then
        print_success "Live2 RTMP container is running"
        ((PASSED_TESTS++))
    else
        print_error "Live2 RTMP container is not running"
        print_info "Starting container..."
        docker-compose up -d rtmp-server
        sleep 10
        if docker ps | grep -q "rtmp-server"; then
            print_success "Live2 RTMP container started successfully"
            ((PASSED_TESTS++))
        else
            print_error "Failed to start Live2 RTMP container"
            ((FAILED_TESTS++))
        fi
    fi
    ((TOTAL_TESTS++))
    
    # Test container health
    run_test "Container health check" "curl -f http://localhost:8080/stat"
    run_test "RTMP port is accessible" "nc -z localhost 1935"
    run_test "HLS port is accessible" "nc -z localhost 8080"
    
    # Check if S3 upload script is running
    if docker exec rtmp-server pgrep -f "upload-to-s3.sh" >/dev/null 2>&1; then
        print_success "S3 upload script is running inside container"
        ((PASSED_TESTS++))
    else
        print_warning "S3 upload script is not running, attempting to start..."
        docker exec -d rtmp-server /usr/local/bin/upload-to-s3.sh
        sleep 3
        if docker exec rtmp-server pgrep -f "upload-to-s3.sh" >/dev/null 2>&1; then
            print_success "S3 upload script started successfully"
            ((PASSED_TESTS++))
        else
            print_error "Failed to start S3 upload script"
            ((FAILED_TESTS++))
        fi
    fi
    ((TOTAL_TESTS++))
}

# Test AWS Configuration Inside Container
test_aws_config() {
    print_section "AWS CONFIGURATION TEST"
    
    # Test AWS credentials inside container
    if docker exec rtmp-server aws sts get-caller-identity >/dev/null 2>&1; then
        print_success "AWS credentials are available inside container"
        ((PASSED_TESTS++))
    else
        print_error "AWS credentials are not available inside container"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    # Test S3 access from container
    if docker exec rtmp-server aws s3 ls "s3://$S3_BUCKET" >/dev/null 2>&1; then
        print_success "S3 bucket is accessible from container"
        ((PASSED_TESTS++))
    else
        print_error "S3 bucket is not accessible from container"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    # Test environment variables
    local s3_bucket_env
    s3_bucket_env=$(docker exec rtmp-server printenv S3_BUCKET 2>/dev/null || echo "")
    if [ "$s3_bucket_env" = "$S3_BUCKET" ]; then
        print_success "S3_BUCKET environment variable is set correctly"
        ((PASSED_TESTS++))
    else
        print_error "S3_BUCKET environment variable is not set correctly (got: '$s3_bucket_env')"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Generate test stream
start_test_stream() {
    print_section "STARTING TEST STREAM"
    
    print_info "Starting FFmpeg test stream to: $RTMP_URL/$TEST_STREAM_KEY"
    print_info "Stream duration: ${TEST_DURATION} seconds"
    
    # Start FFmpeg in background
    ffmpeg -f lavfi -i testsrc2=size=1280x720:rate=30 \
           -f lavfi -i sine=frequency=1000:sample_rate=44100 \
           -c:v libx264 -preset veryfast -tune zerolatency \
           -b:v 2M -maxrate 2M -bufsize 4M \
           -c:a aac -b:a 128k \
           -f flv "$RTMP_URL/$TEST_STREAM_KEY" \
           -t "$TEST_DURATION" \
           >/tmp/ffmpeg_test.log 2>&1 &
    
    local ffmpeg_pid=$!
    echo "$ffmpeg_pid" > /tmp/ffmpeg_test.pid
    
    print_info "FFmpeg started with PID: $ffmpeg_pid"
    sleep 5  # Give it time to connect
    
    # Check if FFmpeg is still running
    if kill -0 "$ffmpeg_pid" 2>/dev/null; then
        print_success "Test stream started successfully"
        return 0
    else
        print_error "Test stream failed to start"
        cat /tmp/ffmpeg_test.log
        return 1
    fi
}

# Test HLS Generation
test_hls_generation() {
    print_section "HLS GENERATION TEST"
    
    print_info "Waiting for HLS segments to be generated..."
    sleep 10
    
    # Check if master playlist exists
    if curl -s "$HLS_BASE_URL/${TEST_STREAM_KEY}.m3u8" | grep -q "EXTM3U"; then
        print_success "Master HLS playlist is available"
        ((PASSED_TESTS++))
    else
        print_error "Master HLS playlist is not available"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    # Check individual quality playlists
    for quality in "4k" "2k" "1080"; do
        if curl -s "$HLS_BASE_URL/${TEST_STREAM_KEY}_${quality}.m3u8" | grep -q "EXTM3U"; then
            print_success "$quality quality playlist is available"
            ((PASSED_TESTS++))
        else
            print_error "$quality quality playlist is not available"
            ((FAILED_TESTS++))
        fi
        ((TOTAL_TESTS++))
    done
    
    # Check if segments are being created
    local segment_count
    segment_count=$(docker exec rtmp-server find /mnt/hls -name "${TEST_STREAM_KEY}*.ts" | wc -l)
    if [ "$segment_count" -gt 0 ]; then
        print_success "HLS segments are being generated ($segment_count segments found)"
        ((PASSED_TESTS++))
    else
        print_error "No HLS segments found"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Test S3 Upload Functionality
test_s3_upload() {
    print_section "S3 UPLOAD FUNCTIONALITY TEST"
    
    print_info "Waiting for S3 uploads to begin..."
    sleep 15
    
    # Check if files are being uploaded to S3
    local s3_objects
    s3_objects=$(aws s3 ls "s3://$S3_BUCKET/" --recursive | grep "$TEST_STREAM_KEY" | wc -l)
    
    if [ "$s3_objects" -gt 0 ]; then
        print_success "Files are being uploaded to S3 ($s3_objects objects found)"
        ((PASSED_TESTS++))
    else
        print_warning "No files found in S3 yet, checking upload logs..."
        docker exec rtmp-server cat /var/log/s3_upload_errors.log || echo "No error log found"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    # Check for both segments and playlists
    local s3_segments
    local s3_playlists
    s3_segments=$(aws s3 ls "s3://$S3_BUCKET/" --recursive | grep "$TEST_STREAM_KEY.*\.ts" | wc -l)
    s3_playlists=$(aws s3 ls "s3://$S3_BUCKET/" --recursive | grep "$TEST_STREAM_KEY.*\.m3u8" | wc -l)
    
    if [ "$s3_segments" -gt 0 ]; then
        print_success "Video segments uploaded to S3 ($s3_segments segments)"
        ((PASSED_TESTS++))
    else
        print_error "No video segments found in S3"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    if [ "$s3_playlists" -gt 0 ]; then
        print_success "Playlists uploaded to S3 ($s3_playlists playlists)"
        ((PASSED_TESTS++))
    else
        print_error "No playlists found in S3"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Test Fail-Safe Recovery
test_failsafe_recovery() {
    print_section "FAIL-SAFE RECOVERY TEST"
    
    # Create a fake failed upload entry
    print_info "Testing S3 recovery mechanism..."
    docker exec rtmp-server bash -c "echo '/mnt/hls/fake_segment.ts' >> /var/log/failed_uploads.log"
    
    # Run recovery script
    if docker exec rtmp-server /usr/local/bin/s3-recovery.sh >/dev/null 2>&1; then
        print_success "S3 recovery script executed successfully"
        ((PASSED_TESTS++))
    else
        print_error "S3 recovery script failed"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    # Test container health monitoring
    print_info "Testing container health monitoring..."
    if [ -f "./scripts/container-health-monitor.sh" ]; then
        if timeout 10s ./scripts/container-health-monitor.sh check >/dev/null 2>&1; then
            print_success "Container health monitoring works"
            ((PASSED_TESTS++))
        else
            print_error "Container health monitoring failed"
            ((FAILED_TESTS++))
        fi
    else
        print_warning "Container health monitor script not found"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Test Stream Continuity
test_stream_continuity() {
    print_section "STREAM CONTINUITY TEST"
    
    print_info "Testing stream continuity for remaining duration..."
    
    local start_time=$(date +%s)
    local test_end_time=$((start_time + 20))  # Test for 20 more seconds
    local continuity_check_count=0
    local successful_checks=0
    
    while [ $(date +%s) -lt $test_end_time ]; do
        ((continuity_check_count++))
        
        if curl -s "$HLS_BASE_URL/${TEST_STREAM_KEY}.m3u8" >/dev/null 2>&1; then
            ((successful_checks++))
        fi
        
        sleep 2
    done
    
    local success_rate=$((successful_checks * 100 / continuity_check_count))
    
    if [ $success_rate -ge 90 ]; then
        print_success "Stream continuity excellent ($success_rate% uptime)"
        ((PASSED_TESTS++))
    elif [ $success_rate -ge 75 ]; then
        print_warning "Stream continuity acceptable ($success_rate% uptime)"
        ((PASSED_TESTS++))
    else
        print_error "Stream continuity poor ($success_rate% uptime)"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Monitor and display logs
monitor_logs() {
    print_section "LOG MONITORING"
    
    print_info "Recent S3 upload logs:"
    docker exec rtmp-server tail -10 /var/log/s3_upload_errors.log 2>/dev/null || echo "No S3 upload logs yet"
    
    print_info "Recent container logs:"
    docker logs --tail 10 rtmp-server 2>/dev/null || echo "No container logs available"
    
    print_info "Failed uploads log:"
    docker exec rtmp-server cat /var/log/failed_uploads.log 2>/dev/null || echo "No failed uploads"
}

# Cleanup test stream
cleanup_test_stream() {
    print_section "CLEANUP"
    
    # Stop FFmpeg
    if [ -f /tmp/ffmpeg_test.pid ]; then
        local ffmpeg_pid=$(cat /tmp/ffmpeg_test.pid)
        if kill -0 "$ffmpeg_pid" 2>/dev/null; then
            print_info "Stopping FFmpeg (PID: $ffmpeg_pid)..."
            kill "$ffmpeg_pid" 2>/dev/null || true
            sleep 2
        fi
        rm -f /tmp/ffmpeg_test.pid
    fi
    
    # Clean up local test files
    rm -f /tmp/ffmpeg_test.log
    
    print_info "Waiting for final uploads..."
    sleep 10
    
    # Optional: Clean up S3 test files (commented out by default)
    # print_info "Cleaning up S3 test files..."
    # aws s3 rm "s3://$S3_BUCKET/" --recursive --exclude "*" --include "*$TEST_STREAM_KEY*"
    
    print_success "Cleanup completed"
}

# Generate test report
generate_report() {
    print_section "TEST REPORT"
    
    echo -e "${BLUE}Total Tests: $TOTAL_TESTS${NC}"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "${YELLOW}Success Rate: $success_rate%${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 ALL TESTS PASSED! RTMP Fail-Safe System is working perfectly!${NC}"
        return 0
    elif [ $success_rate -ge 80 ]; then
        echo -e "\n${YELLOW}⚠️ Most tests passed but some issues detected. Check failed tests above.${NC}"
        return 1
    else
        echo -e "\n${RED}❌ Multiple test failures detected. RTMP Fail-Safe System needs attention.${NC}"
        return 2
    fi
}

# Main execution
main() {
    print_header
    
    # Set up cleanup trap
    trap cleanup_test_stream EXIT
    
    # Run test suite
    test_prerequisites
    test_container_status
    test_aws_config
    
    if start_test_stream; then
        test_hls_generation
        test_s3_upload
        test_failsafe_recovery
        test_stream_continuity
        monitor_logs
    else
        print_error "Failed to start test stream, skipping streaming tests"
    fi
    
    # Generate final report
    generate_report
}

# Run main function
main "$@" 
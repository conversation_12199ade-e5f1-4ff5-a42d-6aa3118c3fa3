#!/bin/bash
set -euo pipefail

# --- RTMP Test Environment Setup Script ---
# Prepares the environment for comprehensive RTMP fail-safe testing

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================================="
    echo "   RTMP TEST ENVIRONMENT SETUP"
    echo "   Configuring: Docker + AWS + Dependencies"
    echo "=============================================================="
    echo -e "${NC}"
}

# Check and install dependencies
check_dependencies() {
    print_info "Checking dependencies..."
    
    # Check Docker
    if command -v docker >/dev/null 2>&1; then
        print_success "Docker is installed"
    else
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        print_success "Docker Compose is installed"
    else
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check FFmpeg
    if command -v ffmpeg >/dev/null 2>&1; then
        print_success "FFmpeg is installed"
    else
        print_warning "FFmpeg is not installed. Installing..."
        sudo apt-get update && sudo apt-get install -y ffmpeg
        print_success "FFmpeg installed"
    fi
    
    # Check AWS CLI
    if command -v aws >/dev/null 2>&1; then
        print_success "AWS CLI is installed"
    else
        print_warning "AWS CLI is not installed. Installing..."
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
        unzip -q awscliv2.zip
        sudo ./aws/install
        rm -rf awscliv2.zip aws/
        print_success "AWS CLI installed"
    fi
    
    # Check netcat
    if command -v nc >/dev/null 2>&1; then
        print_success "Netcat is available"
    else
        print_warning "Netcat is not installed. Installing..."
        sudo apt-get update && sudo apt-get install -y netcat
        print_success "Netcat installed"
    fi
    
    # Check curl
    if command -v curl >/dev/null 2>&1; then
        print_success "Curl is available"
    else
        print_warning "Curl is not installed. Installing..."
        sudo apt-get update && sudo apt-get install -y curl
        print_success "Curl installed"
    fi
}

# Setup AWS credentials
setup_aws_credentials() {
    print_info "Setting up AWS credentials..."
    
    # Check if credentials are already configured
    if aws sts get-caller-identity >/dev/null 2>&1; then
        print_success "AWS credentials are already configured"
        local identity=$(aws sts get-caller-identity --output text --query 'Arn')
        print_info "Current AWS identity: $identity"
        return 0
    fi
    
    # Check environment variables
    if [ -n "${AWS_ACCESS_KEY_ID:-}" ] && [ -n "${AWS_SECRET_ACCESS_KEY:-}" ]; then
        print_success "AWS credentials found in environment variables"
        
        # Test the credentials
        if aws sts get-caller-identity >/dev/null 2>&1; then
            print_success "AWS credentials are valid"
        else
            print_error "AWS credentials in environment are invalid"
            exit 1
        fi
    else
        print_error "AWS credentials not found in environment variables"
        print_info "Please set the following environment variables:"
        echo "  export AWS_ACCESS_KEY_ID=your_access_key"
        echo "  export AWS_SECRET_ACCESS_KEY=your_secret_key"
        echo "  export AWS_REGION=your_region"
        echo "  export AWS_S3_BUCKET_NAME=your_bucket_name"
        exit 1
    fi
}

# Setup project environment
setup_project_env() {
    print_info "Setting up project environment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        print_info "Creating .env file..."
        cat > .env << EOF
# AWS Configuration
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
AWS_REGION=${AWS_REGION}
AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}

# Server Configuration
NODE_ENV=production
PORT=8081
SERVER_PORT=3000

# Streaming Configuration
RTMP_PORT=1935
HLS_PORT=8080

# JWT Configuration (generate your own in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
COOKIE_SECRET=your-super-secret-cookie-key-change-this-in-production
EOF
        print_success ".env file created"
    else
        print_success ".env file already exists"
    fi
    
    # Make scripts executable
    print_info "Making scripts executable..."
    chmod +x scripts/*.sh 2>/dev/null || true
    chmod +x live2/*.sh 2>/dev/null || true
    print_success "Scripts made executable"
}

# Build Docker images
build_docker_images() {
    print_info "Building Docker images..."
    
    # Build Live2 image first
    print_info "Building Live2 RTMP/HLS image..."
    cd live2
    if ./build_docker.sh; then
        print_success "Live2 image built successfully"
    else
        print_error "Failed to build Live2 image"
        exit 1
    fi
    cd ..
    
    # Build main application image
    print_info "Building main application image..."
    if docker-compose build vyoo-server; then
        print_success "Main application image built successfully"
    else
        print_error "Failed to build main application image"
        exit 1
    fi
}

# Test AWS S3 bucket access
test_s3_access() {
    print_info "Testing S3 bucket access..."
    
    local bucket_name="${AWS_S3_BUCKET_NAME}"
    
    # Test bucket access
    if aws s3 ls "s3://$bucket_name" >/dev/null 2>&1; then
        print_success "S3 bucket '$bucket_name' is accessible"
    else
        print_error "Cannot access S3 bucket '$bucket_name'"
        print_info "Please check:"
        echo "  1. Bucket name is correct: $bucket_name"
        echo "  2. AWS credentials have access to the bucket"
        echo "  3. Bucket exists in the specified region: ${AWS_REGION}"
        exit 1
    fi
    
    # Test write permissions
    local test_file="test_file_$(date +%s).txt"
    if echo "test" | aws s3 cp - "s3://$bucket_name/$test_file" >/dev/null 2>&1; then
        print_success "S3 bucket has write permissions"
        # Clean up test file
        aws s3 rm "s3://$bucket_name/$test_file" >/dev/null 2>&1
    else
        print_error "S3 bucket does not have write permissions"
        exit 1
    fi
}

# Start services
start_services() {
    print_info "Starting Docker services..."
    
    # Stop any existing services
    docker-compose down >/dev/null 2>&1 || true
    
    # Start services
    if docker-compose up -d; then
        print_success "Docker services started"
    else
        print_error "Failed to start Docker services"
        exit 1
    fi
    
    # Wait for services to be ready
    print_info "Waiting for services to be ready..."
    sleep 15
    
    # Check service health
    local retry_count=0
    local max_retries=12
    
    while [ $retry_count -lt $max_retries ]; do
        if curl -s http://localhost:8080/stat >/dev/null 2>&1; then
            print_success "RTMP server is ready"
            break
        else
            print_info "Waiting for RTMP server... (attempt $((retry_count + 1))/$max_retries)"
            sleep 5
            ((retry_count++))
        fi
    done
    
    if [ $retry_count -eq $max_retries ]; then
        print_error "RTMP server failed to start within timeout"
        docker-compose logs rtmp-server
        exit 1
    fi
}

# Verify fail-safe scripts
verify_failsafe_scripts() {
    print_info "Verifying fail-safe scripts..."
    
    # Check if S3 upload script is running in container
    if docker exec rtmp-server pgrep -f "upload-to-s3.sh" >/dev/null 2>&1; then
        print_success "S3 upload script is running in container"
    else
        print_warning "S3 upload script is not running, starting it..."
        docker exec -d rtmp-server /usr/local/bin/upload-to-s3.sh
        sleep 3
        if docker exec rtmp-server pgrep -f "upload-to-s3.sh" >/dev/null 2>&1; then
            print_success "S3 upload script started successfully"
        else
            print_error "Failed to start S3 upload script"
        fi
    fi
    
    # Test AWS credentials inside container
    if docker exec rtmp-server aws sts get-caller-identity >/dev/null 2>&1; then
        print_success "AWS credentials work inside container"
    else
        print_error "AWS credentials not working inside container"
        print_info "Container environment variables:"
        docker exec rtmp-server env | grep AWS || echo "No AWS variables found"
    fi
    
    # Test S3 access from container
    if docker exec rtmp-server aws s3 ls "s3://${AWS_S3_BUCKET_NAME}" >/dev/null 2>&1; then
        print_success "S3 bucket accessible from container"
    else
        print_error "S3 bucket not accessible from container"
    fi
}

# Display final status
display_status() {
    print_info "Environment setup complete! Here's what's ready:"
    echo ""
    echo "🔧 Services:"
    echo "  • RTMP Server: rtmp://localhost:1935/live"
    echo "  • HLS Server: http://localhost:8080/hls"
    echo "  • Statistics: http://localhost:8080/stat"
    echo ""
    echo "☁️ AWS Configuration:"
    echo "  • Region: ${AWS_REGION}"
    echo "  • S3 Bucket: ${AWS_S3_BUCKET_NAME}"
    echo "  • Access: ✓ Verified"
    echo ""
    echo "🛡️ Fail-Safe Systems:"
    echo "  • S3 Upload Monitor: ✓ Running"
    echo "  • Recovery Scripts: ✓ Available"
    echo "  • Container Health: ✓ Monitored"
    echo ""
    echo "🧪 Ready for Testing:"
    echo "  Run: ./scripts/test-rtmp-failsafe.sh"
    echo ""
}

# Main execution
main() {
    print_header
    
    check_dependencies
    setup_aws_credentials
    setup_project_env
    build_docker_images
    test_s3_access
    start_services
    verify_failsafe_scripts
    display_status
    
    print_success "RTMP test environment is ready! 🚀"
}

# Run main function
main "$@" 
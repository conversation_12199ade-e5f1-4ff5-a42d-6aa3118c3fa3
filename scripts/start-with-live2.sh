#!/bin/bash

# Vyoo Server with Live2 Streaming - Startup Script with Fail-Safe System
echo "🚀 Starting Vyoo Server with Live2 Streaming Container + Fail-Safe S3 System"
echo "============================================================================="

# Set script to exit on any error
set -e

# Load environment variables if .env exists
if [ -f ".env" ]; then
    echo "Loading environment variables from .env file..."
    export $(grep -v '^#' .env | xargs) 2>/dev/null || true
else
    echo "No .env file found. Creating one with default values..."
    ./setup-env.sh
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Stop existing containers if running
print_status "Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Build the live2 streaming container
print_status "Building Live2 RTMP/HLS streaming container..."
cd live2
if ./build_docker.sh; then
    print_success "Live2 container built successfully!"
else
    print_error "Failed to build Live2 container"
    exit 1
fi
cd ..

# Build the main application
print_status "Building Vyoo server..."
if npm run build; then
    print_success "Vyoo server built successfully!"
else
    print_error "Failed to build Vyoo server"
    exit 1
fi

# Start all services with Docker Compose
print_status "Starting all services..."
if docker-compose up -d; then
    print_success "All services started successfully!"
else
    print_error "Failed to start services"
    exit 1
fi

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Health checks
print_status "Performing health checks..."

# Check if Live2 streaming server is healthy
if curl -f http://localhost:8080/stat > /dev/null 2>&1; then
    print_success "✓ Live2 streaming server is healthy"
else
    print_warning "⚠ Live2 streaming server might not be ready yet"
fi

# Check if main server is healthy
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    print_success "✓ Vyoo server is healthy"
else
    print_warning "⚠ Vyoo server might not be ready yet"
fi

# Display service information
echo ""
echo "🎉 Services are running with Comprehensive Fail-Safe System!"
echo "============================================================="
echo "📺 RTMP Streaming:"
echo "   - RTMP Input: rtmp://localhost:1935/live/<stream_key>"
echo "   - HLS Output: http://localhost:8080/hls/<stream_key>.m3u8"
echo "   - Statistics: http://localhost:8080/stat"
echo "   - Health Check: http://localhost:8080/health"
echo ""
echo "🌐 Vyoo Server:"
echo "   - API Endpoint: http://localhost:3000"
echo "   - Health Check: http://localhost:3000/health"
echo ""
echo "☁️  S3 Fail-Safe System:"
echo "   - Bucket: ${AWS_S3_BUCKET_NAME:-vyooo-abr}"
echo "   - Region: ${AWS_REGION:-ap-south-1}"
echo "   - Upload Monitoring: ACTIVE"
echo "   - Recovery Service: ACTIVE (every 10 min)"
echo ""
echo "📊 Available Quality Levels:"
echo "   - 4K (3840x2160) - Ultra High Quality"
echo "   - 2K (2560x1440) - High Quality  "
echo "   - 1080p (1920x1080) - Standard Quality"
echo ""
echo "🔧 Management Commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop all: docker-compose down"
echo "   - Monitor streaming: cd live2 && ./monitor.sh"
echo "   - Test S3 integration: cd live2 && ./test-s3-integration.sh"
echo "   - Monitor S3 uploads: docker logs -f rtmp-server"
echo "   - Check failed uploads: docker exec rtmp-server cat /var/log/failed_uploads.log"
echo ""
echo "🎮 Test Stream:"
echo "   ffmpeg -re -f lavfi -i testsrc2=size=1280x720:rate=30 \\"
echo "   -c:v libx264 -preset ultrafast -f flv \\"
echo "   rtmp://localhost:1935/live/test"
echo ""
print_success "Setup complete! 🚀" 
#!/bin/bash

# Vyoo Production Monitor - Health Check Script
echo "🔍 Vyoo Production Health Monitor"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MAIN_SERVER_URL="http://localhost:3000"
RTMP_SERVER_URL="http://localhost:8080"
RTMP_STATS_URL="http://localhost:8080/stat"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓ HEALTHY]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠ WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗ ERROR]${NC} $1"
}

# Function to check HTTP endpoint
check_endpoint() {
    local url=$1
    local name=$2
    local timeout=${3:-10}
    
    print_status "Checking $name at $url..."
    
    response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout $timeout "$url" 2>/dev/null)
    
    if [ "$response" = "200" ]; then
        print_success "$name is healthy (HTTP $response)"
        return 0
    else
        print_error "$name is unhealthy (HTTP $response)"
        return 1
    fi
}

# Function to check Docker container
check_container() {
    local container_name=$1
    
    if docker ps --format "table {{.Names}}" | grep -q "^$container_name$"; then
        print_success "Container '$container_name' is running"
        
        # Check container health if health check is configured
        health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null)
        if [ "$health_status" = "healthy" ]; then
            print_success "Container '$container_name' health check: $health_status"
        elif [ "$health_status" = "unhealthy" ]; then
            print_error "Container '$container_name' health check: $health_status"
            return 1
        elif [ -n "$health_status" ]; then
            print_warning "Container '$container_name' health check: $health_status"
        fi
        
        return 0
    else
        print_error "Container '$container_name' is not running"
        return 1
    fi
}

# Function to check RTMP streaming stats
check_rtmp_stats() {
    print_status "Checking RTMP streaming statistics..."
    
    stats=$(curl -s --connect-timeout 10 "$RTMP_STATS_URL" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$stats" ]; then
        print_success "RTMP stats endpoint is accessible"
        
        # Parse active streams count (simple XML parsing)
        active_streams=$(echo "$stats" | grep -o '<stream>' | wc -l)
        print_status "Active streams: $active_streams"
        
        # Check for live streams
        if echo "$stats" | grep -q '<publishing>'; then
            print_success "Live streams detected"
        else
            print_status "No active live streams"
        fi
        
        return 0
    else
        print_error "Failed to get RTMP statistics"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    print_status "Checking disk space..."
    
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -lt 80 ]; then
        print_success "Disk usage: ${disk_usage}% (healthy)"
    elif [ "$disk_usage" -lt 90 ]; then
        print_warning "Disk usage: ${disk_usage}% (warning)"
    else
        print_error "Disk usage: ${disk_usage}% (critical)"
        return 1
    fi
    
    return 0
}

# Function to check memory usage
check_memory() {
    print_status "Checking memory usage..."
    
    mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$mem_usage" -lt 80 ]; then
        print_success "Memory usage: ${mem_usage}% (healthy)"
    elif [ "$mem_usage" -lt 90 ]; then
        print_warning "Memory usage: ${mem_usage}% (warning)"
    else
        print_error "Memory usage: ${mem_usage}% (critical)"
        return 1
    fi
    
    return 0
}

# Function to check Docker volumes
check_volumes() {
    print_status "Checking Docker volumes..."
    
    volumes=("rtmp-hls-data" "rtmp-dash-data" "app-data")
    
    for volume in "${volumes[@]}"; do
        if docker volume inspect "$volume" >/dev/null 2>&1; then
            print_success "Volume '$volume' exists"
        else
            print_error "Volume '$volume' is missing"
            return 1
        fi
    done
    
    return 0
}

# Main monitoring function
run_health_check() {
    local failed_checks=0
    
    echo ""
    print_status "Starting comprehensive health check..."
    echo ""
    
    # Check Docker containers
    print_status "=== Container Health ==="
    check_container "vyoo-server" || ((failed_checks++))
    check_container "rtmp-server" || ((failed_checks++))
    echo ""
    
    # Check HTTP endpoints
    print_status "=== HTTP Endpoints ==="
    check_endpoint "$MAIN_SERVER_URL/health" "Main Server" || ((failed_checks++))
    check_endpoint "$RTMP_SERVER_URL/stat" "RTMP Server" || ((failed_checks++))
    echo ""
    
    # Check RTMP streaming
    print_status "=== Streaming Services ==="
    check_rtmp_stats || ((failed_checks++))
    echo ""
    
    # Check system resources
    print_status "=== System Resources ==="
    check_disk_space || ((failed_checks++))
    check_memory || ((failed_checks++))
    echo ""
    
    # Check Docker volumes
    print_status "=== Storage Volumes ==="
    check_volumes || ((failed_checks++))
    echo ""
    
    # Summary
    echo "==============================="
    if [ "$failed_checks" -eq 0 ]; then
        print_success "All health checks passed! ✨"
        exit 0
    else
        print_error "$failed_checks health check(s) failed"
        exit 1
    fi
}

# Parse command line arguments
case "${1:-check}" in
    "check"|"health")
        run_health_check
        ;;
    "quick")
        print_status "Running quick health check..."
        check_endpoint "$MAIN_SERVER_URL/health" "Main Server"
        check_endpoint "$RTMP_SERVER_URL/stat" "RTMP Server"
        ;;
    "containers")
        print_status "Checking containers only..."
        check_container "vyoo-server"
        check_container "rtmp-server"
        ;;
    "system")
        print_status "Checking system resources only..."
        check_disk_space
        check_memory
        ;;
    "--help"|"-h")
        echo "Usage: $0 [check|quick|containers|system|help]"
        echo ""
        echo "Commands:"
        echo "  check      - Run full health check (default)"
        echo "  quick      - Quick endpoint checks only"
        echo "  containers - Check Docker containers only"
        echo "  system     - Check system resources only"
        echo "  help       - Show this help message"
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 --help' for usage information"
        exit 1
        ;;
esac 
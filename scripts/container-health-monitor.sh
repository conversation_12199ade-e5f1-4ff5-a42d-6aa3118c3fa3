#!/bin/bash
set -Eeuo pipefail

# --- Container Health Monitor ---
# Monitors Live2 container health and automatically restarts if needed

# --- Configuration ---
LIVE2_CONTAINER_NAME="${LIVE2_CONTAINER_NAME:-rtmp-server}"
MAIN_CONTAINER_NAME="${MAIN_CONTAINER_NAME:-vyoo-server}"
MONGODB_CONTAINER_NAME="${MONGODB_CONTAINER_NAME:-mongodb}"
HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-30}"
MAX_RESTART_ATTEMPTS="${MAX_RESTART_ATTEMPTS:-3}"
RESTART_COOLDOWN="${RESTART_COOLDOWN:-60}"
ALERT_LOG="${ALERT_LOG:-/var/log/container_health_alerts.log}"
HEALTH_LOG="${HEALTH_LOG:-/var/log/container_health.log}"

# --- State Tracking ---
LIVE2_RESTART_COUNT=0
MAIN_RESTART_COUNT=0
MONGODB_RESTART_COUNT=0
LAST_RESTART_TIME=0

# --- Colors for output ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# --- Logging Functions ---
log() {
    local type="$1"
    local message="$2"
    local timestamp=$(date --iso-8601=seconds)
    echo "{\"timestamp\": \"$timestamp\", \"level\": \"$type\", \"message\": \"$message\"}" | tee -a "$HEALTH_LOG"
}

alert() {
    local message="$1"
    local timestamp=$(date --iso-8601=seconds)
    echo "{\"timestamp\": \"$timestamp\", \"level\": \"ALERT\", \"message\": \"$message\"}" | tee -a "$ALERT_LOG"
    log "ALERT" "$message"
}

print_status() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# --- Health Check Functions ---

# Check if container is running
is_container_running() {
    local container_name="$1"
    docker ps --format "table {{.Names}}" | grep -q "^$container_name$"
}

# Check container health status
get_container_health() {
    local container_name="$1"
    docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "no-health-check"
}

# Check Live2 streaming functionality
check_live2_streaming() {
    local rtmp_port_open=false
    local hls_port_open=false
    local stats_accessible=false
    
    # Check RTMP port (1935)
    if nc -z localhost 1935 2>/dev/null; then
        rtmp_port_open=true
    fi
    
    # Check HLS port (8080)
    if nc -z localhost 8080 2>/dev/null; then
        hls_port_open=true
    fi
    
    # Check stats endpoint
    if curl -s --connect-timeout 5 "http://localhost:8080/stat" >/dev/null 2>&1; then
        stats_accessible=true
    fi
    
    if $rtmp_port_open && $hls_port_open && $stats_accessible; then
        return 0
    else
        log "WARNING" "Live2 streaming check failed - RTMP:$rtmp_port_open, HLS:$hls_port_open, Stats:$stats_accessible"
        return 1
    fi
}

# Check main server health
check_main_server() {
    if curl -s --connect-timeout 10 "http://localhost:3000/health" | grep -q "healthy"; then
        return 0
    else
        log "WARNING" "Main server health check failed"
        return 1
    fi
}

# Check MongoDB connection
check_mongodb() {
    if docker exec "$MONGODB_CONTAINER_NAME" mongosh --eval "db.adminCommand('ping')" --quiet >/dev/null 2>&1; then
        return 0
    else
        log "WARNING" "MongoDB health check failed"
        return 1
    fi
}

# --- Container Management Functions ---

# Restart container with cooldown protection
restart_container() {
    local container_name="$1"
    local restart_count_var="$2"
    local current_time=$(date +%s)
    
    # Check cooldown period
    if [ $((current_time - LAST_RESTART_TIME)) -lt $RESTART_COOLDOWN ]; then
        log "WARNING" "Restart cooldown active for $container_name. Skipping restart."
        return 1
    fi
    
    # Check max restart attempts
    local current_count
    eval "current_count=\$$restart_count_var"
    
    if [ $current_count -ge $MAX_RESTART_ATTEMPTS ]; then
        alert "CRITICAL: Maximum restart attempts reached for $container_name. Manual intervention required."
        return 1
    fi
    
    log "INFO" "Attempting to restart container: $container_name (attempt $((current_count + 1))/$MAX_RESTART_ATTEMPTS)"
    
    # Perform restart
    if docker restart "$container_name" >/dev/null 2>&1; then
        eval "$restart_count_var=$((current_count + 1))"
        LAST_RESTART_TIME=$current_time
        log "INFO" "Successfully restarted container: $container_name"
        
        # Wait for container to be ready
        sleep 10
        return 0
    else
        alert "CRITICAL: Failed to restart container: $container_name"
        return 1
    fi
}

# Reset restart counters on successful health checks
reset_restart_counters() {
    local container_name="$1"
    local restart_count_var="$2"
    local current_count
    eval "current_count=\$$restart_count_var"
    
    if [ $current_count -gt 0 ]; then
        eval "$restart_count_var=0"
        log "INFO" "Reset restart counter for $container_name (was $current_count)"
    fi
}

# --- Health Monitoring Functions ---

# Monitor Live2 container
monitor_live2() {
    print_status "Checking Live2 container health..."
    
    if ! is_container_running "$LIVE2_CONTAINER_NAME"; then
        print_error "Live2 container is not running"
        alert "CRITICAL: Live2 container ($LIVE2_CONTAINER_NAME) is not running"
        
        if restart_container "$LIVE2_CONTAINER_NAME" "LIVE2_RESTART_COUNT"; then
            print_success "Live2 container restarted successfully"
        else
            print_error "Failed to restart Live2 container"
            return 1
        fi
    else
        local health_status=$(get_container_health "$LIVE2_CONTAINER_NAME")
        
        if [ "$health_status" = "healthy" ] || [ "$health_status" = "no-health-check" ]; then
            # Additional functional check
            if check_live2_streaming; then
                print_success "Live2 container is healthy"
                reset_restart_counters "$LIVE2_CONTAINER_NAME" "LIVE2_RESTART_COUNT"
            else
                print_warning "Live2 container is running but streaming is not functional"
                
                if restart_container "$LIVE2_CONTAINER_NAME" "LIVE2_RESTART_COUNT"; then
                    print_success "Live2 container restarted for streaming issues"
                else
                    print_error "Failed to restart Live2 container for streaming issues"
                    return 1
                fi
            fi
        else
            print_error "Live2 container health check failed: $health_status"
            
            if restart_container "$LIVE2_CONTAINER_NAME" "LIVE2_RESTART_COUNT"; then
                print_success "Live2 container restarted due to health check failure"
            else
                print_error "Failed to restart Live2 container"
                return 1
            fi
        fi
    fi
    
    return 0
}

# Monitor main server container
monitor_main_server() {
    print_status "Checking main server health..."
    
    if ! is_container_running "$MAIN_CONTAINER_NAME"; then
        print_error "Main server container is not running"
        alert "CRITICAL: Main server container ($MAIN_CONTAINER_NAME) is not running"
        
        if restart_container "$MAIN_CONTAINER_NAME" "MAIN_RESTART_COUNT"; then
            print_success "Main server container restarted successfully"
        else
            print_error "Failed to restart main server container"
            return 1
        fi
    else
        local health_status=$(get_container_health "$MAIN_CONTAINER_NAME")
        
        if [ "$health_status" = "healthy" ] || [ "$health_status" = "no-health-check" ]; then
            # Additional functional check
            if check_main_server; then
                print_success "Main server container is healthy"
                reset_restart_counters "$MAIN_CONTAINER_NAME" "MAIN_RESTART_COUNT"
            else
                print_warning "Main server container is running but not responding properly"
                
                if restart_container "$MAIN_CONTAINER_NAME" "MAIN_RESTART_COUNT"; then
                    print_success "Main server container restarted for health issues"
                else
                    print_error "Failed to restart main server container"
                    return 1
                fi
            fi
        else
            print_error "Main server container health check failed: $health_status"
            
            if restart_container "$MAIN_CONTAINER_NAME" "MAIN_RESTART_COUNT"; then
                print_success "Main server container restarted due to health check failure"
            else
                print_error "Failed to restart main server container"
                return 1
            fi
        fi
    fi
    
    return 0
}

# Monitor MongoDB container
monitor_mongodb() {
    print_status "Checking MongoDB health..."
    
    if ! is_container_running "$MONGODB_CONTAINER_NAME"; then
        print_error "MongoDB container is not running"
        alert "CRITICAL: MongoDB container ($MONGODB_CONTAINER_NAME) is not running"
        
        if restart_container "$MONGODB_CONTAINER_NAME" "MONGODB_RESTART_COUNT"; then
            print_success "MongoDB container restarted successfully"
        else
            print_error "Failed to restart MongoDB container"
            return 1
        fi
    else
        local health_status=$(get_container_health "$MONGODB_CONTAINER_NAME")
        
        if [ "$health_status" = "healthy" ] || [ "$health_status" = "no-health-check" ]; then
            # Additional functional check
            if check_mongodb; then
                print_success "MongoDB container is healthy"
                reset_restart_counters "$MONGODB_CONTAINER_NAME" "MONGODB_RESTART_COUNT"
            else
                print_warning "MongoDB container is running but not responding properly"
                
                if restart_container "$MONGODB_CONTAINER_NAME" "MONGODB_RESTART_COUNT"; then
                    print_success "MongoDB container restarted for health issues"
                else
                    print_error "Failed to restart MongoDB container"
                    return 1
                fi
            fi
        else
            print_error "MongoDB container health check failed: $health_status"
            
            if restart_container "$MONGODB_CONTAINER_NAME" "MONGODB_RESTART_COUNT"; then
                print_success "MongoDB container restarted due to health check failure"
            else
                print_error "Failed to restart MongoDB container"
                return 1
            fi
        fi
    fi
    
    return 0
}

# --- System Resource Monitoring ---

# Check system resources
monitor_system_resources() {
    print_status "Checking system resources..."
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        alert "CRITICAL: Disk usage is at ${disk_usage}% - running out of space"
        print_error "Critical disk usage: ${disk_usage}%"
    elif [ "$disk_usage" -gt 80 ]; then
        log "WARNING" "Disk usage is at ${disk_usage}% - consider cleanup"
        print_warning "High disk usage: ${disk_usage}%"
    else
        print_success "Disk usage: ${disk_usage}%"
    fi
    
    # Check memory usage
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -gt 95 ]; then
        alert "CRITICAL: Memory usage is at ${mem_usage}% - system may become unstable"
        print_error "Critical memory usage: ${mem_usage}%"
    elif [ "$mem_usage" -gt 85 ]; then
        log "WARNING" "Memory usage is at ${mem_usage}% - consider optimization"
        print_warning "High memory usage: ${mem_usage}%"
    else
        print_success "Memory usage: ${mem_usage}%"
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_percentage=$(echo "scale=0; $load_avg * 100 / $cpu_cores" | bc)
    
    if [ "$load_percentage" -gt 90 ]; then
        alert "CRITICAL: System load is at ${load_percentage}% of CPU capacity"
        print_error "Critical system load: ${load_avg} (${load_percentage}%)"
    elif [ "$load_percentage" -gt 70 ]; then
        log "WARNING" "System load is at ${load_percentage}% of CPU capacity"
        print_warning "High system load: ${load_avg} (${load_percentage}%)"
    else
        print_success "System load: ${load_avg} (${load_percentage}%)"
    fi
}

# --- Main Monitoring Loop ---

# Run single health check cycle
run_health_check() {
    local failed_checks=0
    
    echo ""
    print_status "=== Container Health Check Cycle ==="
    
    # Monitor all containers
    monitor_live2 || ((failed_checks++))
    monitor_main_server || ((failed_checks++))
    monitor_mongodb || ((failed_checks++))
    
    # Monitor system resources
    monitor_system_resources
    
    # Log summary
    if [ $failed_checks -eq 0 ]; then
        log "INFO" "Health check cycle completed successfully"
        print_success "All containers healthy"
    else
        log "WARNING" "Health check cycle completed with $failed_checks failures"
        print_warning "$failed_checks container(s) had issues"
    fi
    
    echo ""
}

# Continuous monitoring mode
run_continuous_monitoring() {
    log "INFO" "Starting continuous container health monitoring"
    print_status "Container Health Monitor - Continuous Mode"
    print_status "Check interval: ${HEALTH_CHECK_INTERVAL}s"
    print_status "Max restart attempts: $MAX_RESTART_ATTEMPTS"
    print_status "Restart cooldown: ${RESTART_COOLDOWN}s"
    
    while true; do
        run_health_check
        sleep "$HEALTH_CHECK_INTERVAL"
    done
}

# --- Main Function ---

main() {
    # Create log files
    touch "$HEALTH_LOG" "$ALERT_LOG"
    
    # Parse command line arguments
    case "${1:-continuous}" in
        "continuous"|"monitor")
            run_continuous_monitoring
            ;;
        "check"|"once")
            run_health_check
            ;;
        "status")
            print_status "Container Status:"
            echo "Live2: $(is_container_running "$LIVE2_CONTAINER_NAME" && echo "Running" || echo "Stopped")"
            echo "Main Server: $(is_container_running "$MAIN_CONTAINER_NAME" && echo "Running" || echo "Stopped")"
            echo "MongoDB: $(is_container_running "$MONGODB_CONTAINER_NAME" && echo "Running" || echo "Stopped")"
            ;;
        "--help"|"-h")
            echo "Usage: $0 [continuous|check|status|help]"
            echo ""
            echo "Commands:"
            echo "  continuous - Run continuous monitoring (default)"
            echo "  check      - Run single health check"
            echo "  status     - Show container status"
            echo "  help       - Show this help message"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 --help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@" 
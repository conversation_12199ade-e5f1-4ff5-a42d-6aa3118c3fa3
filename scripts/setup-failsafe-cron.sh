#!/bin/bash
set -euo pipefail

# --- Fail-Safe Cron Setup Script ---
# Sets up all fail-safe mechanisms as automated cron jobs

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

print_status "Setting up Vyoo Streaming Platform fail-safe automation"
echo "Project root: $PROJECT_ROOT"
echo ""

# Make scripts executable
print_status "Making scripts executable..."
chmod +x "$PROJECT_ROOT/live2/s3-recovery.sh"
chmod +x "$PROJECT_ROOT/live2/upload-to-s3.sh"
chmod +x "$PROJECT_ROOT/scripts/container-health-monitor.sh"
chmod +x "$PROJECT_ROOT/scripts/production-monitor.sh"
print_success "Scripts made executable"

# Create cron jobs
print_status "Setting up cron jobs..."

# Create temporary cron file
TEMP_CRON=$(mktemp)

# Add existing cron jobs (preserve user's existing crons)
crontab -l > "$TEMP_CRON" 2>/dev/null || echo "# Vyoo Streaming Platform Cron Jobs" > "$TEMP_CRON"

# Add fail-safe cron jobs
cat >> "$TEMP_CRON" << EOF

# === VYOO STREAMING PLATFORM FAIL-SAFE SYSTEM ===
# Generated by setup-failsafe-cron.sh on $(date)

# S3 Recovery - Run every 10 minutes to recover failed uploads
*/10 * * * * $PROJECT_ROOT/live2/s3-recovery.sh >> /var/log/s3_recovery_cron.log 2>&1

# Container Health Monitor - Run every 30 seconds (using systemd-run for sub-minute intervals)
* * * * * systemd-run --on-active=0s --on-unit-active=30s $PROJECT_ROOT/scripts/container-health-monitor.sh check >> /var/log/container_health_cron.log 2>&1

# Production Health Check - Run every 5 minutes
*/5 * * * * $PROJECT_ROOT/scripts/production-monitor.sh quick >> /var/log/production_health_cron.log 2>&1

# Full System Health Check - Run every hour
0 * * * * $PROJECT_ROOT/scripts/production-monitor.sh check >> /var/log/full_health_cron.log 2>&1

# Log Rotation - Clean old logs daily at 2 AM
0 2 * * * find /var/log -name "*vyoo*" -type f -mtime +7 -delete
0 2 * * * find /var/log -name "*s3_*" -type f -mtime +7 -delete
0 2 * * * find /var/log -name "*container_*" -type f -mtime +7 -delete

# === END VYOO FAIL-SAFE SYSTEM ===
EOF

# Install the new crontab
crontab "$TEMP_CRON"
rm "$TEMP_CRON"

print_success "Cron jobs installed successfully"

# Create systemd service for continuous container monitoring
print_status "Creating systemd service for container health monitoring..."

sudo tee /etc/systemd/system/vyoo-container-monitor.service > /dev/null << EOF
[Unit]
Description=Vyoo Container Health Monitor
After=docker.service
Requires=docker.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_ROOT
ExecStart=$PROJECT_ROOT/scripts/container-health-monitor.sh continuous
Restart=always
RestartSec=10
StandardOutput=append:/var/log/vyoo-container-monitor.log
StandardError=append:/var/log/vyoo-container-monitor-error.log

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
sudo systemctl daemon-reload
sudo systemctl enable vyoo-container-monitor.service
sudo systemctl start vyoo-container-monitor.service

print_success "Container health monitoring service installed and started"

# Verify cron jobs
print_status "Verifying cron installation..."
if crontab -l | grep -q "VYOO STREAMING PLATFORM"; then
    print_success "Cron jobs verified successfully"
else
    print_error "Cron job installation may have failed"
fi

# Show status
echo ""
print_status "=== FAIL-SAFE SYSTEM STATUS ==="
echo ""

print_status "Cron Jobs:"
crontab -l | grep -A 20 "VYOO STREAMING PLATFORM" | grep -v "^#" | while read line; do
    if [[ -n "$line" ]]; then
        echo "  ✓ $line"
    fi
done

echo ""
print_status "Systemd Services:"
if systemctl is-active --quiet vyoo-container-monitor.service; then
    print_success "vyoo-container-monitor.service is running"
else
    print_warning "vyoo-container-monitor.service is not running"
fi

echo ""
print_status "Log Files:"
echo "  • S3 Recovery: /var/log/s3_recovery.log"
echo "  • Container Health: /var/log/container_health.log"
echo "  • Production Monitor: /var/log/production_health_cron.log"
echo "  • Container Monitor Service: /var/log/vyoo-container-monitor.log"

echo ""
print_success "Fail-safe system setup complete!"
echo ""
print_status "What's now automated:"
echo "  ✓ S3 upload failure recovery (every 10 minutes)"
echo "  ✓ Container health monitoring (continuous + cron backup)"
echo "  ✓ Production health checks (every 5 minutes)"
echo "  ✓ Full system health audit (hourly)"
echo "  ✓ Log rotation (daily cleanup)"
echo ""
print_status "Manual commands:"
echo "  • Check status: systemctl status vyoo-container-monitor"
echo "  • View logs: journalctl -u vyoo-container-monitor -f"
echo "  • Run manual recovery: $PROJECT_ROOT/live2/s3-recovery.sh"
echo "  • Force health check: $PROJECT_ROOT/scripts/production-monitor.sh check" 
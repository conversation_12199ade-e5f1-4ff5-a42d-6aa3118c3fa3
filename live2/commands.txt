#Docker image for ui
docker run -d -p 8090:80 airensoft/ovenplayerdemo:latest

# custamized conf
docker run --name custum-ome -d -e OME_HOST_IP=localhost \
  -p 1935:1935 -p 8080:8080 \
  -v $(pwd)/ome_conf:/opt/ovenmediaengine/bin/origin_conf \
  -v $(pwd)/recordings:/recordings \
  airensoft/ovenmediaengine:latest

  # Inspect the container’s CPU settings
sudo docker inspect custum-ome \
  --format 'Allowed CPUs: {{.HostConfig.CpusetCpus}} | CPU quota: {{.HostConfig.NanoCpus}} nanoseconds'

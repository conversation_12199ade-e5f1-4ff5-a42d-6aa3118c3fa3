# 🚀 Production Deployment Checklist

This checklist ensures your Vyoo server with Live2 streaming is production-ready.

## ✅ Pre-Deployment Setup

### 1. Environment Configuration
- [ ] Copy `.env.example` to `.env` and fill in all required values
- [ ] Set `NODE_ENV=production`
- [ ] Configure secure `JWT_SECRET` and `COOKIE_SECRET` (use crypto-random values)
- [ ] Set up MongoDB connection string (`MONGODB_URI`)
- [ ] Configure AWS credentials and S3 buckets
- [ ] Set up email server credentials
- [ ] Configure OAuth providers (Google, Apple)
- [ ] Set up Stripe keys for payments

### 2. Live2 Streaming Configuration
- [ ] Verify `RTMP_BASE_URL` points to your streaming server
- [ ] Configure `HLS_BASE_URL` for viewer access
- [ ] Set appropriate `LIVE2_RTMP_PORT` and `LIVE2_HTTP_PORT`
- [ ] Test RTMP ingestion endpoint accessibility
- [ ] Verify HLS output endpoint accessibility

### 3. Security Configuration
- [ ] Generate secure random secrets for JWT and cookies
- [ ] Configure CORS origins for your domain
- [ ] Set up HTTPS certificates (if not using load balancer)
- [ ] Review and restrict API access patterns
- [ ] Enable rate limiting for public endpoints

### 4. Infrastructure Requirements
- [ ] Minimum 4GB RAM for streaming workloads
- [ ] Minimum 2 CPU cores for transcoding
- [ ] At least 50GB storage for logs and temporary files
- [ ] High-bandwidth network connection (recommend 100Mbps+)
- [ ] Docker and Docker Compose installed

## ✅ Build and Test

### 1. Code Compilation
```bash
npm install
npm run build
```
- [ ] TypeScript compilation successful
- [ ] No build errors or warnings
- [ ] Dependencies installed correctly

### 2. Docker Build
```bash
docker-compose build
```
- [ ] Main app container builds successfully
- [ ] Live2 streaming container builds successfully
- [ ] No Docker build errors

### 3. Local Testing
```bash
docker-compose up -d
./scripts/production-monitor.sh
```
- [ ] Both containers start successfully
- [ ] Health checks pass
- [ ] API endpoints respond correctly
- [ ] RTMP server accepts connections
- [ ] HLS endpoints serve content

## ✅ Deployment Steps

### 1. Initial Deployment
```bash
# Start services
npm run start:live2

# Monitor deployment
./scripts/production-monitor.sh
```
- [ ] Services start without errors
- [ ] Health checks are passing
- [ ] Logs show normal operation

### 2. Network Configuration
- [ ] Firewall allows port 1935 (RTMP)
- [ ] Firewall allows port 8080 (HLS)
- [ ] Firewall allows your app port (3000 by default)
- [ ] Load balancer configured (if applicable)
- [ ] SSL/TLS certificates installed

### 3. Domain and DNS
- [ ] Domain points to your server
- [ ] SSL certificate is valid
- [ ] RTMP URL accessible: `rtmp://yourdomain.com:1935/live`
- [ ] HLS URL accessible: `https://yourdomain.com:8080/hls`
- [ ] API URL accessible: `https://yourdomain.com/api`

## ✅ Post-Deployment Verification

### 1. Streaming Tests
- [ ] Test RTMP stream from OBS/FFmpeg
- [ ] Verify HLS playback in browsers
- [ ] Test multiple quality levels
- [ ] Verify adaptive bitrate switching
- [ ] Test stream recording (if enabled)

### 2. API Tests
- [ ] Authentication endpoints work
- [ ] Stream management endpoints work
- [ ] User registration and login work
- [ ] File uploads work correctly
- [ ] Database operations complete successfully

### 3. Performance Tests
- [ ] API response times < 500ms
- [ ] Streaming latency < 5 seconds
- [ ] Multiple concurrent streams work
- [ ] Memory usage stays under 80%
- [ ] CPU usage reasonable under load

## ✅ Monitoring Setup

### 1. Logging
- [ ] Log files rotate properly
- [ ] Error logs are captured
- [ ] Streaming events are logged
- [ ] Log retention policy configured

### 2. Health Monitoring
```bash
# Set up cron job for monitoring
echo "*/5 * * * * /path/to/scripts/production-monitor.sh quick" | crontab -
```
- [ ] Health check script runs periodically
- [ ] Alerts configured for failures
- [ ] Resource monitoring enabled
- [ ] Uptime monitoring configured

### 3. Backup Strategy
- [ ] Database backups automated
- [ ] Environment files backed up
- [ ] Docker volumes backed up
- [ ] Stream recordings backed up (if applicable)

## ✅ Security Hardening

### 1. Server Security
- [ ] Operating system updated
- [ ] Unnecessary services disabled
- [ ] SSH key authentication only
- [ ] Fail2ban or similar intrusion detection
- [ ] Regular security updates scheduled

### 2. Application Security
- [ ] Rate limiting configured
- [ ] Input validation enabled
- [ ] SQL injection protection
- [ ] XSS protection headers
- [ ] Secure cookie settings

### 3. Network Security
- [ ] Firewall properly configured
- [ ] VPC/private networks (if cloud)
- [ ] DDoS protection enabled
- [ ] CDN configured for static assets

## ✅ Final Verification

### 1. End-to-End Testing
- [ ] Complete user journey works
- [ ] Streaming workflow works
- [ ] Payment processing works (if enabled)
- [ ] Email notifications work
- [ ] Mobile app compatibility (if applicable)

### 2. Documentation
- [ ] API documentation updated
- [ ] Deployment notes documented
- [ ] Emergency procedures documented
- [ ] Contact information updated

### 3. Team Handover
- [ ] Operations team trained
- [ ] Emergency contacts provided
- [ ] Monitoring access granted
- [ ] Backup procedures tested

## 🚨 Emergency Procedures

### Quick Commands
```bash
# Check service status
./scripts/production-monitor.sh

# Restart services
docker-compose restart

# View logs
docker-compose logs -f vyoo-server
docker-compose logs -f rtmp-server

# Scale down for maintenance
docker-compose down

# Emergency stop
docker-compose kill
```

### Rollback Plan
1. Stop current deployment: `docker-compose down`
2. Restore previous image/configuration
3. Start services: `docker-compose up -d`
4. Verify health: `./scripts/production-monitor.sh`

## 📊 Performance Baselines

Record these metrics after successful deployment:

- **API Response Time**: _____ ms
- **Stream Start Time**: _____ seconds
- **Memory Usage**: _____ %
- **CPU Usage**: _____ %
- **Disk Usage**: _____ %
- **Network Bandwidth**: _____ Mbps

## 📞 Support Contacts

- **DevOps Team**: _____________
- **Database Admin**: _____________
- **Network Admin**: _____________
- **Emergency Contact**: _____________

---

**Deployment Date**: ___________  
**Deployed By**: ___________  
**Version**: ___________  
**Environment**: Production  

**✅ All checks completed and verified**  
**🚀 Ready for production traffic** 
# Vyoo Streaming Server

Welcome to the Vyoo Streaming Server! This is the backend for a powerful streaming platform, featuring live RTMP ingestion, HLS playback, VOD processing, and a comprehensive API for managing users, streams, and content.

![CI/CD](https://github.com/your-repo/vyoo-server/actions/workflows/main.yml/badge.svg)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 🚀 Features

- **Live Streaming**: Ingest RTMP streams from OBS, FFmpeg, or other broadcasting software.
- **HLS Playback**: Deliver streams to viewers using Apple's HTTP Live Streaming (HLS) protocol for wide compatibility.
- **VOD Support**: Automatically record live streams and make them available for on-demand viewing.
- **Authentication**: Secure endpoints with JWT-based authentication.
- **User Management**: Full-featured user system with profiles, follows, and more.
- **Content Management**: API for creating, managing, and discovering streams, shorts, and stories.
- **Dockerized**: Fully containerized with <PERSON><PERSON> and Docker Compose for easy setup and deployment.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- [Node.js](https://nodejs.org/) (v16 or higher)
- [npm](https://www.npmjs.com/) (or [yarn](https://yarnpkg.com/))
- [Docker](https://www.docker.com/products/docker-desktop)
- [Docker Compose](https://docs.docker.com/compose/install/)
- [ffmpeg](https://ffmpeg.org/download.html) (for testing streams locally)

## ⚙️ Setup & Installation

1.  **Clone the repository:**
    ```bash
    git clone <your-repository-url>
    cd vyoo-server
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    -   Copy the example environment file:
        ```bash
        cp .env.example .env
        ```
    -   Open `.env` and fill in the required values, especially for the database and secrets. For local development, the defaults for services like MongoDB and streaming ports should work out of the box.

## ▶️ Running the Application

There are two primary ways to run the server:

### 1. Development Mode (with Hot Reloading)

This method is best for active development. It uses `ts-node-dev` to automatically restart the server when you make changes.

```bash
npm run dev
```
The server will start on the port specified in your `.env` file (default is `8000`). The RTMP/HLS server will not be running in this mode.

### 2. Production Mode (with Docker)

This method runs the entire infrastructure, including the main server and the Live2 RTMP/HLS streaming server, in Docker containers. This is the recommended way to test the full streaming functionality.

```bash
# Make sure Docker is running
sudo docker-compose up --build -d
```
This command will:
- Build the Docker images for the main server and the RTMP server.
- Start all services in the background.
- Your infrastructure will be available at:
    - **Main Server API**: `http://localhost:8000`
    - **HLS Playback**: `http://localhost:8080`
    - **RTMP Ingest**: `rtmp://localhost:1935/live`

## 🧪 Testing

The project includes a comprehensive test suite to validate all parts of the streaming infrastructure.

- **To run a full system test:**
    ```bash
    chmod +x scripts/comprehensive-streaming-test.sh
    ./scripts/comprehensive-streaming-test.sh
    ```

- **To run a real-world streaming scenario test:**
    ```bash
    chmod +x scripts/test-real-streaming.sh
    ./scripts/test-real-streaming.sh
    ```
This script provides an example `ffmpeg` command to start a test stream.

## 📂 Project Structure

```
.
├── docker/            # Docker configurations for ancillary services
├── live2/             # Configuration and scripts for the Live2 streaming server
├── scripts/           # Test and utility scripts
├── src/               # Main application source code
│   ├── config/        # Environment, database, passport, etc.
│   ├── constants/     # Application-wide constants
│   ├── controllers/   # Express route handlers
│   ├── cron/          # Cron jobs
│   ├── lib/           # Helper libraries and utilities
│   ├── middlewares/   # Express middlewares
│   ├── models/        # Mongoose schemas and models
│   ├── routes/        # API route definitions
│   ├── services/      # Business logic
│   ├── sockets/       # WebSocket handlers
│   ├── types/         # TypeScript type definitions
│   ├── validators/    # Joi validation schemas
│   └── webhooks/      # Webhook handlers
├── .env               # Local environment variables (gitignored)
├── .env.example       # Example environment file
├── docker-compose.yml # Docker Compose configuration
├── Dockerfile         # Main application Dockerfile
├── package.json       # Project dependencies and scripts
└── README.md          # This file
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a pull request.

1.  Fork the Project
2.  Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3.  Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4.  Push to the Branch (`git push origin feature/AmazingFeature`)
5.  Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details. 
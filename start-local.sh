#!/bin/bash

echo "=== Starting Vyoo Server Locally with Fail-Safe System ==="

# Load environment variables from .env if it exists
if [ -f ".env" ]; then
    echo "Loading environment variables from .env file..."
    export $(grep -v '^#' .env | xargs)
else
    echo "No .env file found. Using default values."
    echo "Run ./setup-env.sh to create one with AWS credentials."
fi

# Local Development Configuration
export NODE_ENV=development
export PORT=8081
export MONGODB_URI=mongodb://localhost:27017/vyoo

# Live2 Integration
export RTMP_BASE_URL=rtmp://localhost:1935/live
export RTMP_LIVE_URL=rtmp://localhost:1935/live
export HLS_BASE_URL=http://localhost:8080/hls
export HLS_CLOUDFRONT_URL=https://d1s6yky0m2rx9y.cloudfront.net/hls
export LIVE2_CONTAINER_NAME=rtmp-server
export LIVE2_HTTP_PORT=8080
export LIVE2_RTMP_PORT=1935

# AWS S3 Integration (use defaults if not in .env)
export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-********************}
export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-RdIHiTGoBlr/ncBxmie4/3a+3GS0gsMxJXi0Q13k}
export AWS_REGION=${AWS_REGION:-ap-south-1}
export AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-vyooo-abr}

# Development JWT Secrets (CHANGE IN PRODUCTION)
export JWT_SECRET=${JWT_SECRET:-vyoo-dev-jwt-secret}
export COOKIE_SECRET=${COOKIE_SECRET:-vyoo-dev-cookie-secret}

echo ""
echo "=== Environment Configuration ==="
echo "Node Environment: $NODE_ENV"
echo "Server Port: $PORT"
echo "MongoDB URI: $MONGODB_URI"
echo "RTMP URL: $RTMP_BASE_URL"
echo "HLS URL: $HLS_BASE_URL"
echo "AWS Region: $AWS_REGION"
echo "S3 Bucket: $AWS_S3_BUCKET_NAME"
echo ""

# Check if the compiled JavaScript exists
if [ ! -f "dist/index.js" ]; then
    echo "⚠️  Compiled JavaScript not found. Building TypeScript..."
    if command -v npm &> /dev/null; then
        npm run build
    else
        echo "❌ npm not found. Please install Node.js and npm, then run 'npm run build'"
        exit 1
    fi
fi

echo "🚀 Starting Vyoo server..."
echo "Access the server at: http://localhost:$PORT"
echo ""

node dist/index.js 
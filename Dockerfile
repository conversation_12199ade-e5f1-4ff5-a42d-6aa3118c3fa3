# Use a builder stage to build the application
FROM node:18-alpine AS builder

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

# Final stage
FROM node:18-alpine

WORKDIR /app

# Install curl, supervisor for process management, and other dependencies
RUN apk add --no-cache \
    curl \
    python3 \
    py3-pip \
    supervisor \
    bash

# Create logs directory with proper permissions
RUN mkdir -p /app/logs && \
    chmod 755 /app/logs

COPY package*.json ./
COPY tsconfig.json ./

# Install only production dependencies
RUN npm ci --only=production

COPY --from=builder /app/dist ./dist

# Copy configuration files
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/start-servers.sh /app/start-servers.sh

# Make startup script executable
RUN chmod +x /app/start-servers.sh

# Expose main server port, making it configurable
ARG PORT=8081
ENV PORT=${PORT}
EXPOSE ${PORT}

# Healthcheck for main server
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

ENTRYPOINT ["/app/start-servers.sh"] 